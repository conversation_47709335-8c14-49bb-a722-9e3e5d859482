const path = require('path');

console.log('ENVIRONMENT:', process.env.ENVIRONMENT);
require('dotenv').config({
  path: './service-integration-tests/environments/.env',
});
require('dotenv').config({
  path: `./service-integration-tests/environments/${process.env.ENVIRONMENT}/.env`,
});

module.exports = {
  reporters: [
    'default',
    [
      'jest-junit',
      {
        outputName: 'svc-junit.xml',
      },
    ],
  ],
  testMatch: [
    '<rootDir>/**/tests/map/map.test.ts',
    '<rootDir>/**/tests/map/map.negative.test.ts',
    '<rootDir>/**/tests/map/favourite.test.ts',
    '<rootDir>/**/tests/map/prices.test.ts',

    '<rootDir>/**/tests/charge/chargeWithoutCard.test.ts',
    '<rootDir>/**/tests/charge/charge.test.ts',
    '<rootDir>/**/tests/history/transactionHistory.test.ts',
    '<rootDir>/**/tests/invoice/invoice.test.ts',

    '<rootDir>/**/tests/user/marketingPreferences.test.ts',
    '<rootDir>/**/tests/user/registerUser.test.ts',
    '<rootDir>/**/tests/user/loginEmail.test.ts',
    '<rootDir>/**/tests/user/personalInformation.test.ts',
    '<rootDir>/**/tests/user/changeEmail.test.ts',
    '<rootDir>/**/tests/user/changePhone.test.ts',
    '<rootDir>/**/tests/user/deleteUser.test.ts',

    '<rootDir>/**/tests/wallet/addPaymentMethod.test.ts',
    '<rootDir>/**/tests/wallet/changeDefaultPaymentMethod.test.ts',

    '<rootDir>/**/tests/links/verify.links.test.ts',

    '<rootDir>/**/tests/rfid/addRfidCard.test.ts',
    '<rootDir>/**/tests/subscription/subscription.test.ts',

    //'<rootDir>/**/tests/user/createTestUser.test.ts',  //Helper utility to create test users without requiring a mobile number.
  ],
  verbose: true,
  rootDir: path.resolve(__dirname, 'service-integration-tests'),
  setupFilesAfterEnv: ['<rootDir>/jest.setup.ts'], // Path to your setup file
  globalSetup: '<rootDir>/jest.globalSetup.ts',
  testEnvironment: 'node',
  maxWorkers: 5,
};
