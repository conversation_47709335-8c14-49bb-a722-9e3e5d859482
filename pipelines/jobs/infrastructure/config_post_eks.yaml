parameters:
  helmCharts:
    # Kubernetes Cluster Autoscaler
    - repoUrl: 'https://kubernetes.github.io/autoscaler'
      repoName: 'autoscaler'
      package: 'cluster-autoscaler'
      namespace: 'kube-system'
      additionalArgs: '--values $(System.DefaultWorkingDirectory)/infrastructure/helm/cluster-autoscaler/values.yaml'
    # Prometheus (for Monitoring and Alerting)
    - repoUrl: 'https://prometheus-community.github.io/helm-charts'
      repoName: 'prometheus-community'
      package: 'kube-prometheus-stack'
      namespace: 'prometheus'
      additionalArgs: '--values $(System.DefaultWorkingDirectory)/infrastructure/helm/prometheus/values.yaml'

jobs:
  - deployment: helm_deploy
    condition: and( ne( canceled(), True), or(eq(stageDependencies.detect_changes.detect_changes.outputs['detect_changes_${{ parameters.stageName }}.${{ parameters.stageName }}Update'], 'true'), and(ne( '${{ parameters.environment }}', 'pr'), ne( '${{ parameters.environment }}', 'dev')), contains(variables['Build.Reason'], 'Manual')))
    displayName: Deploy Helm Charts (${{ upper(parameters.environment) }})
    environment: Pulse-Apps-Backend-${{ parameters.environment }}
    strategy:
      runOnce:
        deploy:
          steps:
            - checkout: self
              persistCredentials: true

            - template: ../../templates/AwsAuthenticationPs1.yaml
              parameters:
                IAMRole: '$(AwsLogicalAccountNameUpperCase)-role_AUTOMATION'

            - task: AWSCLI@1
              displayName: Setup EKS KubeConfig
              inputs:
                regionName: $(AwsRegion)
                awsCommand: 'eks'
                awsSubCommand: 'update-kubeconfig'
                awsArguments: '--name $(cluster-name)'

            - task: HelmInstaller@0
              inputs:
                helmVersion: '3.15.2'
                checkLatestHelmVersion: false
                installKubectl: true
                kubectlVersion: '1.31.0'
                checkLatestKubectl: false

            - task: replacetokens@4
              displayName: Inject ADO variables into Helm Values files
              inputs:
                rootDirectory: '$(System.DefaultWorkingDirectory)/infrastructure/helm/'
                targetFiles: '**/*.yaml'
                encoding: 'auto'
                tokenPattern: 'default'
                writeBOM: true
                actionOnMissing: 'warn'
                keepToken: false
                actionOnNoFiles: 'fail'
                enableTransforms: false
                useLegacyPattern: false
                enableTelemetry: false

            - task: qetza.replacetokens.replacetokens-task.replacetokens@6
              displayName: 'replace ingress manifest tokens'
              inputs:
                sources: $(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/argo-rollouts/ingress-rollout.yaml
                ifNoFilesFound: error

            - script: |
                FluentBitHttpPort='2020'
                FluentBitReadFromHead='Off'
                [[ ${FluentBitReadFromHead} = 'On' ]] && FluentBitReadFromTail='Off'|| FluentBitReadFromTail='On'
                [[ -z ${FluentBitHttpPort} ]] && FluentBitHttpServer='Off' || FluentBitHttpServer='On'
                sed 's/{{cluster_name}}/$(cluster-name)/g; s/{{region_name}}/$(AwsRegion)/g; s/{{http_server_toggle}}/${FluentBitHttpServer}/g; s/{{http_server_port}}/${FluentBitHttpPort}/g; s/{{read_from_head}}/"Off"/g; s/{{read_from_tail}}/"On"/g' ./pipelines/manifests/cwagent-fluent-bit-quickstart.yaml | kubectl apply -f -
              displayName: Deploy CloudWatch Container Insights

            - script: |
                kubectl apply -f ./pipelines/manifests/aws-ssm-daemonset.yaml
              displayName: Deploy SSM Agent Daemonset

            - script: |
                kubectl apply -f https://github.com/kubernetes-sigs/metrics-server/releases/latest/download/components.yaml
              displayName: Deploy Metrics Server

            - script: |
                kubectl apply -f https://github.com/cert-manager/cert-manager/releases/latest/download/cert-manager.yaml
              displayName: Deploy cert-manager Server

            - script: |
                kubectl apply -n argo-rollouts -f https://github.com/argoproj/argo-rollouts/releases/latest/download/install.yaml

                kubectl annotate serviceaccount -n argo-rollouts argo-rollouts eks.amazonaws.com/role-arn=arn:aws:iam::************:role/WS-00CS-role_EKS_SecretsManager_OCPI_Prod --overwrite=true

                kubectl patch deployment argo-rollouts -n argo-rollouts -p='
                {
                  "spec": {
                    "template": {
                      "spec": {
                        "containers": [
                          {
                            "name": "argo-rollouts",
                            "env": [
                              {
                                "name": "AWS_DEFAULT_REGION",
                                "value": "$(AwsRegion)"
                              },
                              {
                                "name": "AWS_REGION",
                                "value": "$(AwsRegion)"
                              }
                            ]
                          }
                        ]
                      }
                    }
                  }
                }'

                if kubectl get deploy/argo-rollouts -n argo-rollouts >/dev/null 2>&1; then
                    kubectl rollout restart deploy/argo-rollouts -n argo-rollouts
                fi

                kubectl apply -n argo-rollouts -f https://raw.githubusercontent.com/argoproj/argo-rollouts/stable/manifests/dashboard-install.yaml

                kubectl apply -f $(System.DefaultWorkingDirectory)/pipelines/manifests/server-reusable-manifests/argo-rollouts/ingress-rollout.yaml -n argo-rollouts --record
              displayName: Deploy Argo Rollouts

            - ${{ each chart in parameters.helmCharts }}:
                - script: |
                    helm repo add ${{ chart.repoName }} ${{ chart.repoUrl }}
                    helm upgrade ${{ chart.package }} ${{ chart.repoName }}/${{ chart.package }} --install --namespace=${{ chart.namespace }} --create-namespace ${{ chart.additionalArgs }}
                  displayName: Deploy ${{ chart.package }} Helm Package

            ## The version of the helm chart being installed will need to be reviewed, updated and tested when a new version is released
            ## Reviewed 1.4.0 works with kubectl 1.19 +
            - script: |
                helm repo add stakater https://stakater.github.io/stakater-charts
                helm repo update stakater
                helm upgrade --install reloader stakater/reloader
              displayName: Install reloader from stakater/reloader repo

            - script: |
                helm repo add secrets-store-csi-driver https://kubernetes-sigs.github.io/secrets-store-csi-driver/charts
                helm repo update secrets-store-csi-driver
                helm upgrade -n kube-system csi-secrets-store secrets-store-csi-driver/secrets-store-csi-driver \
                --install --version v1.5.1 --set syncSecret.enabled=true --set enableSecretRotation=true --set rotationPollInterval=300s
                kubectl apply -f https://raw.githubusercontent.com/aws/secrets-store-csi-driver-provider-aws/main/deployment/aws-provider-installer.yaml
              displayName: Install secrets-store-csi-driver

            - script: |
                helm repo add eks https://aws.github.io/eks-charts
                helm repo update eks
                helm upgrade --install aws-load-balancer-controller eks/aws-load-balancer-controller --namespace kube-system --set clusterName=$(cluster-name) --set serviceAccount.create=false --set serviceAccount.name=aws-load-balancer-controller
              displayName: Deploying aws-load-balancer-controller

            - script: |
                kubectl apply -f ./pipelines/manifests/aws-load-balancer-service-account.yaml
              displayName: Create aws-load-balancer-controller Service Account

            - script: |
                kubectl annotate serviceaccount -n kube-system aws-load-balancer-controller eks.amazonaws.com/role-arn=$(EKSNodeRoleArn)
              displayName: Annotate aws-load-balancer-controller service account
  - job: configure_rbac
    displayName: Configure RBAC
    condition: and( ne( canceled(), True), or(eq(stageDependencies.detect_changes.detect_changes.outputs['detect_changes_${{ parameters.stageName }}.${{ parameters.stageName }}Update'], 'true'), and(  ne( '${{ parameters.environment }}', 'pr'), ne( '${{ parameters.environment }}', 'dev')), contains(variables['Build.Reason'], 'Manual')))
    dependsOn: helm_deploy
    steps:
      - template: ../../templates/AwsAuthenticationPs1.yaml
        parameters:
          IAMRole: '$(AwsLogicalAccountNameUpperCase)-role_AUTOMATION'

      # Install Kubectl 1.28.3 (AWS Required Version)
      # Install Kubectl 1.29.0 (updated AWS Version)
      - task: KubectlInstaller@0
        inputs:
          kubectlVersion: '1.31.0'

      - task: AWSCLI@1
        displayName: Setup EKS KubeConfig
        inputs:
          regionName: $(AwsRegion)
          awsCommand: 'eks'
          awsSubCommand: 'update-kubeconfig'
          awsArguments: '--name $(cluster-name)'

      - script: |
          kubectl apply -f ./pipelines/manifests/developer-cluster-permissions.yaml
          sed -i -e "s#\${AwsAccountId}#$(AwsAccountId)#" '$(System.DefaultWorkingDirectory)/pipelines/manifests/aws-auth-configmap.yaml'
          sed -i -e "s#\${EKSNodeRoleArn}#$(EKSNodeRoleArn)#" '$(System.DefaultWorkingDirectory)/pipelines/manifests/aws-auth-configmap.yaml'
          sed -i -e "s#\${AwsLogicalAccountNameUpperCase}#$(AwsLogicalAccountNameUpperCase)#" '$(System.DefaultWorkingDirectory)/pipelines/manifests/aws-auth-configmap.yaml'
          kubectl apply -f ./pipelines/manifests/aws-auth-configmap.yaml -n kube-system
          kubectl describe configmap aws-auth -n kube-system
        displayName: Configure RBAC Roles

      - script: |
          kubectl apply -f ./pipelines/manifests/namespaces.yaml
        displayName: Create Namespaces

      - script: |
          kubectl apply -f ./pipelines/manifests/adot-permissions.yaml
        displayName: Creating adot collector Service Accounts
