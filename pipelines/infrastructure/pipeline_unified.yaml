# Infrastructure as code CI/CD pipeline

pool:
  name: GenericPoolLinux-SS
trigger: none
resources:
  repositories:
    - repository: pipelines
      type: git
      name: bp_pulse/bp-pulse-platform
parameters:
  - name: environment
    displayName: Pick environment
    type: string
    default: preprod
    values:
      - test
      - preprod
      - performance
      - prod
      - hotfix
      - us-uat
      - us-prod
  - name: apiVersion
    displayName: Api Version
    type: string
    default: v7
  - name: snow_cr
    displayName: SNOW Change Request ID
    type: string
    default: 'Insert Change Request ID when running Prod pipeline'
  - name: snow_create_prod
    displayName: Create Snow CR
    type: boolean
    default: false
  - name: prodChangeCrDescription
    type: string
    default: 'Add reason for prod deployment if using automated CR'
    displayName: Prod Change Description
  - name: hotifx_commit
    displayName: Hotfix Commit ID
    type: string
    default: 'Insert Hotfix Commit ID when the pipeline as hotfix'
  # Platform
  - name: DynamoDbBool
    displayName: DynamoDb
    type: boolean
    default: false
  - name: DynamoDbTag
    displayName: DynamoDb Tag
    type: string
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-08-06_10-10-49.dev
      - 2025-08-06_10-10-49.test
      - 2025-08-06_10-10-49.preprod
      - value4.prod
      - 2025-02-10_13-18-13.performance
      - Custom
      - Hotfix
  - name: DynamoDbVersionNumber
    displayName: DynamoDb Version Number(if Custom)
    type: string
    default: 'Check tag list for DynamoDb'
  - name: OpenSearchBool
    displayName: OpenSearch
    type: boolean
    default: false
  - name: OpenSearchTag
    displayName: OpenSearch Tag
    type: string
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2025-06-12_12-08-45.test
      - 2025-02-05_15-04-16.preprod
      - 2025-02-05_15-04-16.prod
      - 2025-06-12_12-08-45.performance
      - Custom
      - Hotfix
  - name: OpenSearchVersionNumber
    displayName: OpenSearch Version Number(if Custom)
    type: string
    default: 'Check tag list for OpenSearch'
  - name: OpenTelemetryBool
    displayName: OpenTelemetry
    type: boolean
    default: false
  - name: OpenTelemetryTag
    displayName: OpenTelemetry Tag
    type: string
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-00-00_00-00-00.dev
      - 2025-00-00_00-00-00.test
      - 2025-00-00_00-00-00.preprod
      - 2025-00-00_00-00-00.prod
      - 2025-00-00_00-00-00.performance
      - Custom
      - Hotfix
  - name: OpenTelemetryVersionNumber
    displayName: OpenTelemetry Version Number(if Custom)
    type: string
    default: 'Check tag list for OpenTelemetry'
  - name: ResourceCheckMonitoringBool
    displayName: Resource-Check-Monitoring
    type: boolean
    default: false
  - name: Resource-Check-MonitoringTag
    displayName: Resource-Check-Monitoring Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2025-04-02_09-29-27.test
      - 2025-04-02_09-29-27.preprod
      - 2025-04-02_09-29-27.prod
      - 2025-04-02_09-29-27.performance
      - Custom
      - Hotfix
  - name: ResourceCheckMonitoringVersionNumber
    displayName: Resource-Check-Monitoring Version Number(if Custom)
    default: 'Check tag list for Resource-Check-Monitoring(eg: Resource-Check-Monitoring.20240207150402)'
  - name: InfrastructureMonitoringBool
    displayName: Infrastructure-Monitoring
    type: boolean
    default: false
  - name: Infrastructure-MonitoringTag
    displayName: Infrastructure-Monitoring Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2025-06-11_09-09-31.test
      - 2025-06-11_09-09-31.preprod
      - 2025-01-13_10-51-31.prod
      - 2024-09-12_10-07-14.performance
      - Custom
      - Hotfix
  - name: InfrastructureMonitoringVersionNumber
    displayName: Infrastructure-Monitoring Version Number(if Custom)
    type: string
    default: 'Check tag list for Infrastructure-Monitoring(eg: Infrastructure-Monitoring.20240207150402)'
  - name: AuroraQueryStackBool
    displayName: AuroraQueryStack
    type: boolean
    default: false
  - name: Aurora-StackTag
    displayName: AuroraQuery-Stack Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-24_10-59-40.dev
      - 2025-07-24_10-59-40.test
      - 2025-07-24_10-59-40.preprod
      - 2025-05-29_12-49-41.prod
      - 2025-05-29_12-49-41.performance
      - Custom
      - Hotfix
  - name: AuroraQueryStackVersionNumber
    displayName: AuroraQuery-Stack Version Number(if Custom)
    type: string
    default: 'Check tag list for AuroraQuery-Stack(eg: AuroraQuery-Stack.20240207150402)'
  - name: UserDataBool
    displayName: User-Data
    type: boolean
    default: false
  - name: UserData-StackTag
    displayName: User-Data Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-08-14_14-33-29.dev
      - 2025-07-24_10-59-40.test
      - 2025-07-24_10-59-40.preprod
      - 2025-06-03_08-05-00.prod
      - 2025-06-13_13-40-07.performance
      - Custom
      - Hotfix
  - name: UserDataStackVersionNumber
    displayName: User-Data Version Number(if Custom)
    type: string
    default: 'Check tag list for User-Data(eg: User-Data.20240207150402)'
  - name: ArgoDashboardsBool
    displayName: ArgoDashboards
    type: boolean
    default: false
  - name: ArgoDashboards-StackTag
    displayName: ArgoDashboards Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2025-04-24_08-15-15.test
      - 2025-04-24_08-15-15.preprod
      - faketag.prod
      - 2025-04-24_08-15-15.performance
      - Custom
      - Hotfix
  - name: ArgoDashboardsStackVersionNumber
    displayName: ArgoDashboards Stack Version Number(if Custom)
    type: string
    default: 'Check tag list for ArgoDashboards(eg: ArgoDashboards.2024-02-07_15-04-02)'
  - name: ServiceUpgradesMonitoringBool
    displayName: Service-Upgrades-Monitoring
    type: boolean
    default: false
  - name: Service-Upgrades-MonitoringTag
    displayName: Service-Upgrades-Monitoring Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2025-06-11_09-09-31.test
      - 2025-06-11_09-09-31.preprod
      - 2024-08-26_19-18-16.prod
      - 2025-01-24_08-20-41.performance
      - Custom
      - Hotfix
  - name: ServiceUpgradesMonitoringVersionNumber
    displayName: Service-Upgrades-Monitoring Version Number(if Custom)
    default: 'Check tag list for Service-Upgrades-Monitoring(eg: Service-Upgrades-Monitoring.20240207150402)'
  - name: TeamsAlarmIntegrationBool
    displayName: Teams-Alarm-Integration
    type: boolean
    default: false
  - name: Teams-AlarmIntegration-Tag
    displayName: Teams-Alarm-Integration Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - test1.dev
      - test1.test
      - test2.preprod
      - test3.prod
      - test4.performance
      - Custom
      - Hotfix
  - name: TeamsAlarmIntegrationVersionNumber
    displayName: Teams-Alarm-Integration Version Number(if Custom)
    type: string
    default: 'Check tag list for Teams-Alarm-Integration(eg: Teams-Alarm-Integration.20240207150402)'
  # Services
  - name: BppayServiceBool
    displayName: Bppay-Service
    type: boolean
    default: false
  - name: Bppay-ServiceTag
    displayName: Bppay-Service Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-08-21_12-52-52.dev
      - 2025-07-31_13-40-41.test
      - 2025-07-31_13-40-41.preprod
      - 2025-06-23_12-05-13.prod
      - 2025-01-28_13-44-18.performance
      - Custom
      - Hotfix
  - name: BppayServiceVersionNumber
    displayName: Bppay-Service Version Number(if Custom)
    type: string
    default: 'Check tag list for Bppay Service(eg: Bppay-Service.20240207150402)'
  - name: OcpiModuleServiceBool
    displayName: OcpiModule-Service
    type: boolean
    default: false
  - name: OcpiModule-ServiceTag
    displayName: OcpiModule-Service Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-08-21_08-21-55.dev
      - 2025-08-21_08-21-55.test
      - 2025-08-21_08-21-55.preprod
      - 2025-01-24_14-14-31.prod
      - 2025-02-11_11-05-59.performance
      - Custom
      - Hotfix
  - name: OcpiModuleServiceVersionNumber
    displayName: Ocpi-Module-Service Version Number(if Custom)
    type: string
    default: 'Check tag list for Ocpi Module Service(eg: OcpiModule-Service.20240207150402)'
  - name: ChargeServiceBool
    displayName: Charge-Service
    type: boolean
    default: false
  - name: Charge-ServiceTag
    displayName: Charge-Service Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-08-21_14-30-51.dev
      - 2025-08-21_14-07-42.test
      - 2025-08-19_06-50-57.preprod
      - 2025-07-28_15-10-14.hotfix.preprod.prod
      - 2025-02-19_12-10-17.performance
      - Custom
      - Hotfix
  - name: ChargeServiceVersionNumber
    displayName: Charge-Service Version Number(if Custom)
    type: string
    default: 'Check tag list for Charge Service(eg: Charge-Service.20240207150402)'
  - name: SAPIntegrationBool
    displayName: SAP-Integration
    type: boolean
    default: false
  - name: SAP-IntegrationTag
    displayName: SAP-Integration Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-08-06_14-11-32.dev
      - 2025-07-21_12-44-37.test
      - 2025-07-21_12-44-37.preprod
      - 2025-07-21_12-44-37.prod
      - 2025-03-04_19-26-34.performance
      - Custom
      - Hotfix
  - name: SAPIntegrationVersionNumber
    displayName: SAP-Integration Version Number(if Custom)
    type: string
    default: 'Check tag list for SAP Service(eg: SAP-Integration.20240207150402)'
  - name: HtbOcppLoggingBool
    displayName: HtbOcppLogging
    type: boolean
    default: false
  - name: Htb-Ocpp-LoggingTag
    displayName: HtbOcppLogging Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2025-01-24_07-31-58.test
      - test2.preprod
      - test3.prod
      - test4.performance
      - Custom
      - Hotfix
  - name: HtbOcppLoggingVersionNumber
    displayName: HtbOcpp Logging Version Number(if Custom)
    type: string
    default: 'Check tag list for HtbOcpp Logging(eg: Htb-Ocpp-Logging.20240207150402)'
  - name: HistoryServiceBool
    displayName: History-Service
    type: boolean
    default: false
  - name: History-ServiceTag
    displayName: History-Service Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2025-02-04_13-59-50.test
      - 2025-02-04_13-59-50.preprod
      - 2024-09-25_08-48-15.prod
      - 2025-02-04_13-59-50.performance
      - Custom
      - Hotfix
  - name: HistoryServiceVersionNumber
    displayName: History-Service Version Number(if Custom)
    type: string
    default: 'Check tag list for History Service(eg: History-Service.20240207150402)'
  - name: MapServiceBool
    displayName: Map-Service
    type: boolean
    default: false
  - name: Map-ServiceTag
    displayName: Map-Service Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-08-21_12-52-52.dev
      - 2025-08-14_10-01-21.test
      - 2025-08-14_10-01-21.preprod
      - 2024-06-10_08-02-38.prod
      - 2025-06-12_09-54-47.performance
      - Custom
      - Hotfix
  - name: MapServiceVersionNumber
    displayName: Map-Service Version Number(if Custom)
    type: string
    default: 'Check tag list for Map Service(eg: Map-Service.20240207150402)'
  - name: OfferServiceBool
    displayName: Offer-Service
    type: boolean
    default: false
  - name: Offer-ServiceTag
    displayName: Offer-Service Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-08-21_12-52-52.dev
      - 2025-06-03_14-31-53.test
      - 2025-06-03_14-31-53.preprod
      - 2025-02-09_21-56-32.prod
      - 2025-02-09_21-56-32.performance
      - Custom
      - Hotfix
  - name: OfferServiceVersionNumber
    displayName: Offer-Service Version Number(if Custom)
    type: string
    default: 'Check tag list for Offer Service(eg: Offer-Service.20240207150402)'
  - name: RfidServiceBool
    displayName: Rfid-Service
    type: boolean
    default: false
  - name: Rfid-ServiceTag
    displayName: Rfid-Service Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-31_20-50-37.dev
      - 2025-07-31_19-30-25.test
      - 2025-07-31_20-50-37.preprod
      - 2025-06-19_10-34-07.hotfix.prod
      - 2025-02-10_13-18-13.performance
      - Custom
      - Hotfix
  - name: RfidServiceVersionNumber
    displayName: Rfid-Service Version Number(if Custom)
    type: string
    default: 'Check tag list for Rfid Service(eg: Rfid-Service.20240207150402)'
  - name: PdfServiceBool
    displayName: Pdf-Service
    type: boolean
    default: false
  - name: Pdf-ServiceTag
    displayName: Pdf-Service Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-08-21_08-21-55.dev
      - 2025-08-21_08-21-55.test
      - 2025-08-21_08-21-55.preprod
      - 2025-05-12_14-52-09.prod
      - 2025-02-11_12-36-40.performance
      - Custom
      - Hotfix
  - name: PdfServiceVersionNumber
    displayName: Pdf-Service Version Number(if Custom)
    type: string
    default: 'Check tag list for Pdf Service(eg: Pdf-Service.20240207150402)'
  - name: UserServiceBool
    displayName: User-Service
    type: boolean
    default: false
  - name: User-ServiceTag
    displayName: User-Service Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-08-20_11-04-02.dev
      - 2025-07-16_13-10-38.test
      - 2025-07-16_13-10-38.preprod
      - 2025-06-04_15-28-44.hotfix.prod
      - 2025-05-29_12-49-41.performance
      - Custom
      - Hotfix
  - name: UserServiceVersionNumber
    displayName: User-Service Version Number(if Custom)
    type: string
    default: 'Check tag list for User Service(eg: User-Service.20240207150402)'
  - name: WalletServiceBool
    displayName: Wallet-Service
    type: boolean
    default: false
  - name: Wallet-ServiceTag
    displayName: Wallet-Service Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2025-02-10_13-18-13.test
      - 2025-02-10_13-18-13.preprod
      - test3.prod
      - 2025-02-10_13-18-13.performance
      - Custom
      - Hotfix
  - name: WalletServiceVersionNumber
    displayName: Wallet-Service Version Number(if Custom)
    type: string
    default: 'Check tag list for Wallet Service(eg: Wallet-Service.20240207150402)'
  - name: SubscriptionServiceBool
    displayName: Subscription-Service
    type: boolean
    default: false
  - name: Subscription-ServiceTag
    displayName: Subscription-Service Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-08-07_12-42-30.dev
      - 2025-08-07_12-42-30.test
      - 2025-08-07_12-42-30.preprod
      - 2025-08-07_12-42-30.prod
      - 2025-02-10_13-18-13.performance
      - Custom
      - Hotfix
  - name: SubscriptionServiceVersionNumber
    displayName: Subscription-Service Version Number(if Custom)
    type: string
    default: 'Check tag list for Subscription Service(eg: Subscription-Service.20240207150402)'
  # Shared
  - name: EKSClusterBool
    displayName: EKS-Cluster
    type: boolean
    default: false
  - name: EKS-ClusterTag
    displayName: EKS-Cluster Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-08-04_10-29-33.dev
      - 2025-07-15_12-48-35.test
      - 2025-07-15_12-48-35.preprod
      - 2025-07-15_12-48-35.prod
      - 2025-07-14_00-00-00.performance
      - Custom
      - Hotfix
  - name: EKSClusterVersionNumber
    displayName: EKS-Cluster Version Number(if Custom)
    type: string
    default: 'Check tag list for EKS Cluster(eg: EKS-Cluster.20240207150402)'
  - name: EKSAddonsBool
    displayName: EKS-Addons
    type: boolean
    default: false
  - name: EKS-AddonsTag
    displayName: EKS-Addons Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-08-04_10-29-33.dev
      - 2025-08-04_10-29-33.test
      - 2025-08-04_10-29-33.preprod
      - 2025-08-04_10-29-33.prod
      - 2025-08-04_10-29-33.performance
      - Custom
      - Hotfix
  - name: EKSAddonsVersionNumber
    displayName: EKS-Addons Version Number(if Custom)
    type: string
    default: 'Check tag list for EKS Addons(eg: EKS-Addons.20240207150402)'
  - name: CustomDomainNameBool
    displayName: Custom-Domain-Name
    type: boolean
    default: false
  - name: Custom-Domain-NameTag
    displayName: Custom-Domain-Name Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - test1.dev
      - test1.test
      - test2.preprod
      - test3.prod
      - test4.performance
      - Custom
      - Hotfix
  - name: CustomDomainNameVersionNumber
    displayName: Custom-Domain-Name Version Number(if Custom)
    type: string
    default: 'Check tag list for Custom-Domain-Name(eg: Custom-Domain-Name.20240207150402)'
  # Network
  - name: BackendGraphqlProxyApiBool
    displayName: Backend-Graphql-Proxy-Api
    type: boolean
    default: false
  - name: Backend-Graphql-Proxy-ApiTag
    displayName: Backend-Graphql-Proxy-Api Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-08-21_08-21-55.dev
      - 2025-08-21_08-21-55.test
      - 2025-08-21_08-21-55.preprod
      - 2024-11-11_10-12-24.prod
      - 2025-04-01_11-36-30.performance
      - Custom
      - Hotfix
  - name: BackendGraphqlProxyApiVersionNumber
    displayName: Backend-Graphql-Proxy-Api Version Number(if Custom)
    type: string
    default: 'Check tag list for Backend-Graphql-Proxy-Api(eg: Backend-Graphql-Proxy-Api.20240207150402)'
  - name: KinesisProxyApiBool
    displayName: KinesisProxy-Api
    type: boolean
    default: false
  - name: KinesisProxy-ApiTag
    displayName: KinesisProxy-Api Tag
    type: string
    values:
      - 2025-07-16_13-10-38.dev
      - Custom
      - Hotfix
  - name: KinesisProxyApiVersionNumber
    displayName: KinesisProxy-Api Version Number(if Custom)
    type: string
    default: 'Check tag list for KinesisProxy-Api(eg: KinesisProxy-Api.20240207150402)'
  - name: PrivateRestApiBool
    displayName: Private-Rest-Api
    type: boolean
    default: false
  - name: Private-Rest-ApiTag
    displayName: Private-Rest-Api Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2025-01-24_07-31-58.test
      - 2025-01-24_07-31-58.preprod
      - 2024-07-04_13-42-26.prod
      - 2024-07-04_13-42-26.performance
      - Custom
      - Hotfix
  - name: PrivateRestApiVersionNumber
    displayName: Private-Rest-Api Version Number(if Custom)
    type: string
    default: 'Check tag list for Private-Rest-Api(eg: BPrivate-Rest-Api.20240207150402)'
  - name: AnonymousApiBool
    displayName: Anonymous-Api
    type: boolean
    default: false
  - name: Anonymous-ApiTag
    displayName: Anonymous-Api Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2025-01-24_07-31-58.test
      - 2024-10-17_09-17-35.preprod
      - 2024-10-17_09-17-35.prod
      - 2024-10-17_09-17-35.performance
      - Custom
      - Hotfix
  - name: AnonymousApiVersionNumber
    displayName: Anonymous-Api Version Number(if Custom)
    type: string
    default: 'Check tag list for Anonymous-Api(eg: Anonymous-Api.20240207150402)'
  - name: OcpiApiBool
    displayName: Ocpi-Api
    type: boolean
    default: false
  - name: Ocpi-ApiTag
    displayName: Ocpi-Api Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2025-02-10_13-18-13.test
      - 2025-02-10_13-18-13.preprod
      - 2024-04-30_19-39-52.prod
      - 2024-04-30_19-39-52.performance
      - Custom
      - Hotfix
  - name: OcpiApiVersionNumber
    displayName: Ocpi-Api Version Number(if Custom)
    type: string
    default: 'Check tag list for Ocpi-Api(eg: Ocpi-Api.20240207150402)'
  - name: MapApiBool
    displayName: Map-Api
    type: boolean
    default: false
  - name: Map-ApiTag
    displayName: Map-Api Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2025-02-04_08-36-33.test
      - 2025-02-04_08-36-33.preprod
      - test4.prod
      - 2025-02-04_08-36-33.performance
      - Custom
      - Hotfix
  - name: MapApiVersionNumber
    displayName: Map-Api Version Number(if Custom)
    type: string
    default: 'Check tag list for Map-Api(eg: Map-Api.20240207150402)'
  - name: SubscriptionApiBool
    displayName: Subscription-Api
    type: boolean
    default: false
  - name: Subscription-ApiTag
    displayName: Subscription-Api Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2025-02-10_13-18-13.test
      - 2025-02-10_13-18-13.preprod
      - test4.prod
      - 2025-02-10_13-18-13.performance
      - Custom
      - Hotfix
  - name: SubscriptionApiVersionNumber
    displayName: Subscription-Api Version Number(if Custom)
    type: string
    default: 'Check tag list for Subscription-Api(eg: Subscription-Api.20240207150402)'
  - name: PulseAlertApiBool
    displayName: Pulse-Alert-Api
    type: boolean
    default: false
  - name: Pulse-Alert-ApiTag
    displayName: Pulse-Alert-Api Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-04_07-39-40.dev
      - 2025-01-24_07-31-58.test
      - 2025-01-24_07-31-58.preprod
      - 2024-10-17_09-17-35.prod
      - 2025-01-24_07-31-58.performance
      - Custom
      - Hotfix
  - name: PulseAlertApiVersionNumber
    displayName: Pulse-Alert-Api Version Number(if Custom)
    type: string
    default: 'Check tag list for Pulse-Alert-Api(eg: Pulse-Alert-Api.20240207150402)'
  - name: PaymentCallbackApibool
    displayName: Payment-Callback-Api
    type: boolean
    default: false
  - name: Payment-Callback-ApiTag
    displayName: Payment-Callback-Api Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-00-00_00-00-00.dev
      - 2025-00-00_00-00-00.test
      - 2025-00-00_00-00-00.preprod
      - 2025-00-00_00-00-00.prod
      - 2025-00-00_00-00-00.performance
      - Custom
      - Hotfix
  - name: PaymentCallbackApiVersionNumber
    displayName: Payment-Callback-Api Version Number(if Custom)
    type: string
    default: 'Check tag list for Payment-Callback-Api(eg: Payment-Callback-Api.20240207150402)'
  - name: BpChargemasterApibool
    displayName: Bp-Chargemaster-Api
    type: boolean
    default: false
  - name: BpChargemaster-ApiTag
    displayName: Bp-Chargemaster-Api Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-00-00_00-00-00.dev
      - 2025-00-00_00-00-00.test
      - 2025-00-00_00-00-00.preprod
      - 2025-00-00_00-00-00.prod
      - 2025-00-00_00-00-00.performance
      - Custom
      - Hotfix
  - name: BpChargemasterApiVersionNumber
    displayName: Bp-Chargemaster-Api Version Number(if Custom)
    type: string
    default: 'Check tag list for Bp-Chargemaster-Api(eg: Bp-Chargemaster-Api.20240207150402)'
  # Monitoring
  - name: AnonymousMonitoringBool
    displayName: Anonymous-Monitoring
    type: boolean
    default: false
  - name: Anonymous-MonitoringTag
    displayName: Anonymous-Monitoring Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2024-07-04_13-42-26.test
      - 2024-07-04_13-42-26.preprod
      - 2024-04-09_16-14-04.prod
      - 2024-04-09_16-14-04.performance
      - Custom
      - Hotfix
  - name: AnonymousMonitoringVersionNumber
    displayName: Anonymous-Monitoring Version Number(if Custom)
    type: string
    default: 'Check tag list for Anonymous-Monitoring(eg: Anonymous-Monitoring.20240207150402)'
  - name: BppayMonitoringBool
    displayName: Bppay-Monitoring
    type: boolean
    default: false
  - name: Bppay-MonitoringTag
    displayName: Bppay-Monitoring Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-24_11-47-22.dev
      - 2025-07-24_11-47-22.test
      - 2025-07-24_11-47-22.preprod
      - 2025-06-16_10-47-42.prod
      - 2024-12-20_13-18-47.performance
      - Custom
      - Hotfix
  - name: BppayMonitoringVersionNumber
    displayName: Bppay-Monitoring Version Number(if Custom)
    type: string
    default: 'Check tag list for Bppay-Monitoring(eg: Bppay-Monitoring.20240207150402)'
  - name: ChargeMonitoringBool
    displayName: Charge-Monitoring
    type: boolean
    default: false
  - name: Charge-MonitoringTag
    displayName: Charge-Monitoring Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-08-21_10-57-50.dev
      - 2025-08-21_10-57-50.test
      - 2025-08-05_13-12-19.preprod
      - 2025-06-12_05-45-18.prod
      - 2024-12-20_13-18-47.performance
      - Custom
      - Hotfix
  - name: ChargeMonitoringVersionNumber
    displayName: Charge-Monitoring Version Number(if Custom)
    type: string
    default: 'Check tag list for Charge-Monitoring(eg: Charge-Monitoring.20240207150402)'
  - name: PDFMonitoringBool
    displayName: PDF-Monitoring
    type: boolean
    default: false
  - name: PDF-MonitoringTag
    displayName: PDF-Monitoring Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2024-07-04_13-42-26.test
      - 2024-07-04_13-42-26.preprod
      - test3.prod
      - 2024-07-04_13-42-26.performance
      - Custom
      - Hotfix
  - name: PDFMonitoringVersionNumber
    displayName: PDF-Monitoring Version Number(if Custom)
    type: string
    default: 'Check tag list for PDF-Monitoring(eg: PDF-Monitoring.20240207150402)'
  - name: ExternalServicesMonitoringBool
    displayName: External-Services-Monitoring
    type: boolean
    default: false
  - name: External-Services-MonitoringTag
    displayName: External-Services-Monitoring Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2024-07-04_13-42-26.test
      - 2024-07-04_13-42-26.preprod
      - 2024-04-09_16-14-04.prod
      - 2024-04-09_16-14-04.performance
      - Custom
      - Hotfix
  - name: ExternalServicesMonitoringVersionNumber
    displayName: External-Services-Monitoring Version Number(if Custom)
    type: string
    default: 'Check tag list for External-Services-Monitoring(eg: External-Services-Monitoring.20240207150402)'
  - name: MapMonitoringBool
    displayName: Map-Monitoring
    type: boolean
    default: false
  - name: Map-MonitoringTag
    displayName: Map-Monitoring Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2025-06-09_13-21-07.test
      - 2025-06-09_13-21-07.preprod
      - 2024-04-09_16-14-04.prod
      - 2024-04-09_16-14-04.performance
      - Custom
      - Hotfix
  - name: MapMonitoringVersionNumber
    displayName: Map-Monitoring Version Number(if Custom)
    type: string
    default: 'Check tag list for Map-Monitoring(eg: Map-Monitoring.20240207150402)'
  - name: RfidMonitoringVersionNumber
    displayName: Rfid-Monitoring Version Number(if Custom)
    type: string
    default: 'Check tag list for Rfid-Monitoring(eg: Rfid-Monitoring.20240207150402)'
  - name: UserMonitoringBool
    displayName: User-Monitoring
    type: boolean
    default: false
  - name: User-MonitoringTag
    displayName: User-Monitoring Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2025-05-14_11-24-02.test
      - 2025-04-25_09-01-25.preprod
      - 2025-05-14_11-24-02.prod
      - 2025-01-24_13-31-25.performance
      - Custom
      - Hotfix
  - name: UserMonitoringVersionNumber
    displayName: User-Monitoring Version Number(if Custom)
    type: string
    default: 'Check tag list for User-Monitoring(eg: User-Monitoring.20240207150402)'
  - name: WalletMonitoringBool
    displayName: Wallet-Monitoring
    type: boolean
    default: false
  - name: Wallet-MonitoringTag
    displayName: Wallet-Monitoring Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2024-07-04_13-42-26.test
      - 2024-07-04_13-42-26.preprod
      - test3.prod
      - 2024-07-04_13-42-26.performance
      - Custom
      - Hotfix
  - name: WalletMonitoringVersionNumber
    displayName: Wallet-Monitoring Version Number(if Custom)
    type: string
    default: 'Check tag list for Wallet-Monitoring(eg: Wallet-Monitoring.20240207150402)'
  - name: OfferMonitoringBool
    displayName: Offer-Monitoring
    type: boolean
    default: false
  - name: Offer-MonitoringTag
    displayName: Offer-Monitoring Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2025-02-11_15-30-50.test
      - 2025-02-11_15-30-50.preprod
      - 2025-02-11_15-30-50.prod
      - 2025-02-11_15-30-50.performance
      - Custom
      - Hotfix
  - name: OfferMonitoringVersionNumber
    displayName: Offer-Monitoring Version Number(if Custom)
    type: string
    default: 'Check tag list for Offer-Monitoring(eg: Offer-Monitoring.20240207150402)'
  - name: OcpiMonitoringBool
    displayName: Ocpi-Monitoring
    type: boolean
    default: false
  - name: Ocpi-MonitoringTag
    displayName: Ocpi-Monitoring Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2024-11-20_08-08-21.test
      - 2024-11-20_08-08-21.preprod
      - 2024-11-20_08-08-21.prod
      - 2024-11-20_08-08-21.performance
      - Custom
      - Hotfix
  - name: OcpiMonitoringVersionNumber
    displayName: Ocpi-Monitoring Version Number(if Custom)
    type: string
    default: 'Check tag list for Ocpi-Monitoring(eg: Ocpi-Monitoring.20240207150402)'
  - name: PricesMonitoringBool
    displayName: Prices-Monitoring
    type: boolean
    default: false
  - name: prices-MonitoringTag
    displayName: Prices-Monitoring Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2024-12-20_13-18-47.dev
      - 2024-00-00_00-00-00.test
      - 2024-00-00_00-00-00.preprod
      - 2024-00-00_00-00-00.prod
      - 2024-00-00_00-00-00.performance
      - Custom
      - Hotfix
  - name: pricesMonitoringVersionNumber
    displayName: Prices-Monitoring Version Number(if Custom)
    type: string
    default: 'Check tag list for Prices-Monitoring(eg: Prices-Monitoring.20240207150402)'
  - name: SubsMonitoringBool
    displayName: Subs-Monitoring
    type: boolean
    default: false
  - name: Subs-MonitoringTag
    displayName: Subs-Monitoring Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2025-02-26_08-53-20.test
      - 2025-02-26_08-53-20.preprod
      - 2025-02-26_08-53-20.prod
      - 2025-02-05_13-11-46.performance
      - Custom
      - Hotfix
  - name: SubsMonitoringVersionNumber
    displayName: Subs-Monitoring Version Number(if Custom)
    type: string
    default: 'Check tag list for Subs-Monitoring(eg: Subs-Monitoring.20240207150402)'
  - name: RFIDMonitoringBool
    displayName: RFID-Monitoring
    type: boolean
    default: false
  - name: RFID-MonitoringTag
    displayName: RFID-Monitoring Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2025-05-09_09-14-54.test
      - 2025-05-09_09-14-54.preprod
      - 2025-05-09_09-14-54.prod
      - 2024-11-15_16-26-43.performance
      - Custom
      - Hotfix
  - name: RegistrationMonitoringBool
    displayName: Registration-Monitoring
    type: boolean
    default: false
  - name: Registration-MonitoringTag
    displayName: Registration-Monitoring Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2024-09-13_13-07-31.test
      - 2024-09-13_13-07-31.preprod
      - 2024-09-13_13-07-31.prod
      - 2024-09-13_13-07-31.performance
      - Custom
      - Hotfix
  - name: RegistrationMonitoringVersionNumber
    displayName: Registration-Monitoring Version Number(if Custom)
    type: string
    default: 'Check tag list for Rfid-Monitoring(eg: Rfid-Monitoring.20240207150402)'
  - name: SapIntegrationMonitoringBool
    displayName: Sap-Integration-Monitoring
    type: boolean
    default: false
  - name: Sap-Integration-MonitoringTag
    displayName: Sap-Integration-Monitoring Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2024-07-04_14-16-45.test
      - 2024-07-04_14-16-45.preprod
      - test3.prod
      - 2024-07-04_14-16-45.performance
      - Custom
      - Hotfix
  - name: SapIntegrationMonitoringVersionNumber
    displayName: Sap-Integration-Monitoring Version Number(if Custom)
    type: string
    default: 'Check tag list for Sap-Integration-Monitoring(eg: Sap-Integration-Monitoring.20240207150402)'
  # Unversioned
  - name: CloudwatchStackBool
    displayName: Cloudwatch-Stack
    type: boolean
    default: false
  - name: Cloudwatch-StackTag
    displayName: Cloudwatch-Stack Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2025-04-01_11-36-30.test
      - 2025-04-01_11-36-30.preprod
      - 2025-04-01_11-36-30.prod
      - 2025-04-01_11-36-30.performance
      - Custom
      - Hotfix
  - name: CloudwatchStackVersionNumber
    displayName: Cloudwatch-Stack Version Number(if Custom)
    type: string
    default: 'Check tag list for Cloudwatch-Stack(eg: Cloudwatch-Stack.20240207150402)'
  - name: MapServiceStackUnversionedBool
    displayName: mapService-StackUnversioned
    type: boolean
    default: false
  - name: Map-Service-UnversionedTag
    displayName: MapService-StackUnversioned Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-16_13-10-38.dev
      - 2024-07-04_13-42-26.test
      - 2024-07-04_13-42-26.preprod
      - test3.prod
      - 2024-07-04_13-42-26.performance
      - Custom
      - Hotfix
  - name: MapServiceStackUnversionedVersionNumber
    displayName: MapService-StackUnversioned Version Number(if Custom)
    type: string
    default: 'Check tag list for MapService-StackUnversioned(eg: Map-Service-Unversioned.20240207150402)'
  - name: ChargeServiceStackUnversionedBool
    displayName: chargeService-StackUnversioned
    type: boolean
    default: false
  - name: Charge-Service-UnversionedTag
    displayName: ChargeService-StackUnversioned Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-07-17_07-46-40.dev
      - 2025-07-17_07-46-40.test
      - 2025-07-17_07-46-40.preprod
      - test3.prod
      - test4.performance
      - Custom
      - Hotfix
  - name: ChargeServiceStackUnversionedVersionNumber
    displayName: ChargeService-StackUnversioned Version Number(if Custom)
    type: string
    default: 'Check tag list for Charge-Service-Unversioned(eg: Charge-Service-Unversioned.20240207150402)'
  - name: NotificationsStackBool
    displayName: Notifications-Stack
    type: boolean
    default: false
  - name: Notifications-StackTag
    displayName: Notifications-Stack Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-08-21_08-21-55.dev
      - 2025-08-21_08-21-55.test
      - 2025-08-21_08-21-55.preprod
      - 2025-01-24_08-20-41.prod
      - 2025-01-24_08-20-41.performance
      - Custom
      - Hotfix
  - name: NotificationsStackVersionNumber
    displayName: Notifications-Stack Version Number(if Custom)
    type: string
    default: 'Check tag list for Notifications-Stack(eg: Notifications-Stack.20240207150402)'
  - name: ElasticSearchBool
    displayName: ElasticSearch-Stack
    type: boolean
    default: false
  - name: ElasticSearch-StackTag
    displayName: ElasticSearch-Stack Tag
    type: string
    default: '--- Please select an option from the dropdown ---'
    values:
      - '--- Please select an option from the dropdown ---'
      - 2025-08-22_14-00-26.dev
      - 2025-08-22_14-00-26.test
      - 2025-08-22_14-00-26.preprod
      - 2025-01-24_07-31-58.prod
      - 2025-01-24_07-31-58.performance
      - Custom
      - Hotfix
  - name: ElasticSearchStackVersionNumber
    displayName: ElasticSearch-Stack Version Number(if Custom)
    type: string
    default: 'Check tag list for ElasticSearch-Stack(eg: ElasticSearch-Stack.20240207150402)'
variables:
  buildDate: $[format('{0:dd}-{0:MM}-{0:yyyy}', pipeline.startTime)]
stages:
  - template: backend/stages/infrastructure.yaml@pipelines
    parameters:
      environment: ${{ parameters.environment }}
      snowCR: ${{ parameters.snow_cr }}
      hotifx_commit: ${{ parameters.hotifx_commit }}
      prodChangeCrDescription: ${{ parameters.prodChangeCrDescription }}
      snow_create_prod: ${{ parameters.snow_create_prod }}
      WorkItems: 'Production IaC Update'
      isManual: true
      buildValidation: false
      apiVersion: ${{ parameters.apiVersion }}
      buildDate: $(buildDate)
      stacks:
        # Platform
        - name: DynamoDb
          folderName: dynamoDbStack
          stageName: dynamoDbStack
          category: platform
          tag: HEAD
          serviceVersion: ${{ parameters.DynamoDbVersionNumber }}
          deployTag: ${{ parameters.DynamoDbTag }}
          bool: ${{ parameters.DynamoDbBool }}
        - name: OpenSearch
          folderName: openSearchStack
          stageName: openSearchStack
          category: platform
          tag: HEAD
          serviceVersion: ${{ parameters.OpenSearchVersionNumber }}
          deployTag: ${{ parameters.OpenSearchTag }}
          bool: ${{ parameters.OpenSearchBool }}
        - name: OpenTelemetry
          folderName: opentelemetryStack
          stageName: openTelemetryStack
          category: platform
          tag: HEAD
          serviceVersion: ${{ parameters.OpenTelemetryVersionNumber }}
          deployTag: ${{ parameters.OpenTelemetryTag }}
          bool: ${{ parameters.OpenTelemetryBool }}
        - name: Resource-Check-Monitoring
          folderName: resourceCheckMonitoringStack
          stageName: resourceCheckMonitoringStack
          category: platform
          serviceVersion: ${{ parameters.ResourceCheckMonitoringVersionNumber }}
          deployTag: ${{ parameters['Resource-Check-MonitoringTag'] }}
          bool: ${{ parameters.ResourceCheckMonitoringBool }}
          boolStackPerAccount: true
        - name: Infrastructure-Monitoring
          folderName: infrastructureMonitoringStack
          stageName: infrastructureMonitoringStack
          category: platform
          serviceVersion: ${{ parameters.InfrastructureMonitoringVersionNumber }}
          deployTag: ${{ parameters['Infrastructure-MonitoringTag'] }}
          bool: ${{ parameters.InfrastructureMonitoringBool }}
          preconfig: canary
        - name: Aurora-Stack
          folderName: auroraStack
          stageName: auroraStack
          category: platform
          serviceVersion: ${{ parameters.AuroraQueryStackVersionNumber }}
          deployTag: ${{ parameters['Aurora-StackTag'] }}
          bool: ${{ parameters.AuroraQueryStackBool }}
        - name: UserData-Stack
          folderName: userDataStack
          stageName: userDataStack
          category: platform
          serviceVersion: ${{ parameters.UserDataStackVersionNumber }}
          deployTag: ${{ parameters['UserData-StackTag'] }}
          bool: ${{ parameters.UserDataBool }}
        - name: ArgoDashboards-Stack
          folderName: argoDashboardsStack
          stageName: argoDashboardsStack
          category: platform
          serviceVersion: ${{ parameters.ArgoDashboardsStackVersionNumber }}
          deployTag: ${{ parameters['ArgoDashboards-StackTag'] }}
          bool: ${{ parameters.ArgoDashboardsBool }}
        - name: Service-Upgrades-Monitoring
          folderName: serviceUpgradesMonitoringStack
          stageName: serviceUpgradesMonitoringStack
          category: platform
          serviceVersion: ${{ parameters.ServiceUpgradesMonitoringVersionNumber }}
          deployTag: ${{ parameters['Service-Upgrades-MonitoringTag'] }}
          bool: ${{ parameters.ServiceUpgradesMonitoringBool }}
        - name: Teams-Alarm-Integration
          folderName: teamsAlarmIntegration
          stageName: teamsAlarmIntegration
          category: platform
          serviceVersion: ${{ parameters.TeamsAlarmIntegrationVersionNumber }}
          deployTag: ${{ parameters['Teams-AlarmIntegration-Tag'] }}
          bool: ${{ parameters.TeamsAlarmIntegrationBool }}
          preconfig: canary
        # Services (includes unversioned services)
        - name: Bppay-Service
          folderName: bppayServiceStack
          stageName: bppayServiceStack
          category: services
          serviceVersion: ${{ parameters.BppayServiceVersionNumber }}
          deployTag: ${{ parameters['Bppay-ServiceTag'] }}
          bool: ${{ parameters.BppayServiceBool }}
        - name: OcpiModule-Service
          folderName: ocpiModuleServiceStack
          stageName: ocpiModuleServiceStack
          category: services
          serviceVersion: OcpiModule-Service.${{ parameters.OcpiModuleServiceVersionNumber }}
          deployTag: ${{ parameters['OcpiModule-ServiceTag'] }}
          bool: ${{ parameters.OcpiModuleServiceBool }}
        - name: Charge-Service
          folderName: chargeServiceStack
          stageName: chargeServiceStack
          category: services
          serviceVersion: ${{ parameters.ChargeServiceVersionNumber }}
          deployTag: ${{ parameters['Charge-ServiceTag'] }}
          bool: ${{ parameters.ChargeServiceBool }}
        - name: Charge-Service-Unversioned
          folderName: chargeServiceStackUnversioned
          stageName: chargeServiceStackUnversioned
          category: services
          serviceVersion: ${{ parameters.ChargeServiceStackUnversionedVersionNumber }}
          deployTag: ${{ parameters['Charge-Service-UnversionedTag'] }}
          bool: ${{ parameters.ChargeServiceStackUnversionedBool }}
        - name: SAP-Integration
          folderName: sapIntegrationStack
          stageName: sapIntegrationStack
          category: dataTransformations
          serviceVersion: SAP-Integration.${{ parameters.SAPIntegrationVersionNumber }}
          deployTag: ${{ parameters['SAP-IntegrationTag'] }}
          bool: ${{ parameters.SAPIntegrationBool }}
        - name: Htb-Ocpp-Logging
          folderName: htbOcppLogging
          stageName: htbOcppLogging
          category: dataTransformations
          serviceVersion: ${{ parameters.HtbOcppLoggingVersionNumber }}
          deployTag: ${{ parameters['Htb-Ocpp-LoggingTag'] }}
          bool: ${{ parameters.HtbOcppLoggingBool }}
          path: infrastructure/cloudformation/stacks/dataTransformations/htbOcppLogging
        - name: History-Service
          folderName: historyServiceStack
          stageName: historyServiceStack
          category: services
          serviceVersion: ${{ parameters.HistoryServiceVersionNumber }}
          deployTag: ${{ parameters['History-ServiceTag'] }}
          bool: ${{ parameters.HistoryServiceBool }}
        - name: Map-Service
          folderName: mapServiceStack
          stageName: mapServiceStack
          category: services
          serviceVersion: ${{ parameters.MapServiceVersionNumber }}
          deployTag: ${{ parameters['Map-ServiceTag'] }}
          bool: ${{ parameters.MapServiceBool }}
          config: elasticsearch
        - name: Map-Service-Unversioned
          folderName: mapServiceStackUnversioned
          stageName: mapServiceStackUnversioned
          category: services
          serviceVersion: ${{ parameters.MapServiceStackUnversionedVersionNumber }}
          deployTag: ${{ parameters['Map-Service-UnversionedTag'] }}
          bool: ${{ parameters.MapServiceStackUnversionedBool }}
        - name: Offer-Service
          folderName: offerServiceStack
          stageName: offerServiceStack
          category: services
          serviceVersion: ${{ parameters.OfferServiceVersionNumber }}
          deployTag: ${{ parameters['Offer-ServiceTag'] }}
          bool: ${{ parameters.OfferServiceBool }}
        - name: Rfid-Service
          folderName: rfidServiceStack
          stageName: rfidServiceStack
          category: services
          serviceVersion: ${{ parameters.RfidServiceVersionNumber }}
          deployTag: ${{ parameters['Rfid-ServiceTag'] }}
          bool: ${{ parameters.RfidServiceBool }}
        - name: Pdf-Service
          folderName: pdfServiceStack
          stageName: pdfServiceStack
          category: services
          serviceVersion: ${{ parameters.PdfServiceVersionNumber }}
          deployTag: ${{ parameters['Pdf-ServiceTag'] }}
          bool: ${{ parameters.PdfServiceBool }}
        - name: Wallet-Service
          folderName: walletServiceStack
          stageName: walletServiceStack
          category: services
          serviceVersion: ${{ parameters.WalletServiceVersionNumber }}
          deployTag: ${{ parameters['Wallet-ServiceTag'] }}
          bool: ${{ parameters.WalletServiceBool }}
        - name: User-Service
          folderName: userServiceStack
          stageName: userServiceStack
          category: services
          serviceVersion: ${{ parameters.UserServiceVersionNumber }}
          deployTag: ${{ parameters['User-ServiceTag'] }}
          bool: ${{ parameters.UserServiceBool }}
        - name: Subscription-Service
          folderName: subscriptionServiceStack
          stageName: subscriptionServiceStack
          category: services
          serviceVersion: ${{ parameters.SubscriptionServiceVersionNumber }}
          deployTag: ${{ parameters['Subscription-ServiceTag'] }}
          bool: ${{ parameters.SubscriptionServiceBool }}
        - name: Notifications-Stack
          folderName: notificationsStack
          stageName: notificationsStack
          category: services
          serviceVersion: ${{ parameters.NotificationsStackVersionNumber}}
          deployTag: ${{ parameters['Notifications-StackTag'] }}
          bool: ${{ parameters.NotificationsStackBool }}
        # Shared
        - name: EKS-Cluster
          folderName: eks
          stageName: eks
          category: shared
          serviceVersion: ${{ parameters.EKSClusterVersionNumber }}
          deployTag: ${{ parameters['EKS-ClusterTag'] }}
          bool: ${{ parameters.EKSClusterBool }}
          config: eks
        - name: EKS-Addons
          folderName: eksaddons
          stageName: eksaddons
          category: shared
          serviceVersion: ${{ parameters.EKSAddonsVersionNumber }}
          deployTag: ${{ parameters['EKS-AddonsTag'] }}
          bool: ${{ parameters.EKSAddonsBool }}
        - name: Custom-Domain-Name
          folderName: customDomainName
          stageName: customDomainName
          category: shared
          serviceVersion: ${{ parameters.CustomDomainNameVersionNumber }}
          deployTag: ${{ parameters['Custom-Domain-NameTag'] }}
          bool: ${{ parameters.CustomDomainNameBool }}
        # Network
        - name: Backend-Graphql-Proxy-Api
          folderName: backendGraphqlProxyApi
          stageName: backendGraphqlProxyApi
          category: network
          serviceVersion: ${{ parameters.BackendGraphqlProxyApiVersionNumber }}
          deployTag: ${{ parameters['Backend-Graphql-Proxy-ApiTag'] }}
          bool: ${{ parameters.BackendGraphqlProxyApiBool }}
        - name: KinesisProxy-Api
          folderName: kinesisProxyApi
          stageName: kinesisProxyApi
          category: network
          serviceVersion: ${{ parameters.KinesisProxyApiVersionNumber }}
          deployTag: ${{ parameters['KinesisProxy-ApiTag'] }}
          bool: ${{ parameters.KinesisProxyApiBool }}
        - name: Private-Rest-Api
          folderName: privateRestApi
          stageName: privateRestApi
          category: network
          serviceVersion: ${{ parameters.PrivateRestApiVersionNumber }}
          deployTag: ${{ parameters['Private-Rest-ApiTag'] }}
          bool: ${{ parameters.PrivateRestApiBool }}
        - name: Anonymous-Api
          folderName: anonymousApi
          stageName: anonymousApi
          category: network
          serviceVersion: ${{ parameters.AnonymousApiVersionNumber }}
          deployTag: ${{ parameters['Anonymous-ApiTag'] }}
          bool: ${{ parameters.AnonymousApiBool }}
        - name: Ocpi-Api
          folderName: ocpiApi
          stageName: ocpiApi
          category: network
          serviceVersion: ${{ parameters.OcpiApiVersionNumber }}
          deployTag: ${{ parameters['Ocpi-ApiTag'] }}
          bool: ${{ parameters.OcpiApiBool }}
        - name: Map-Api
          folderName: mapApi
          stageName: mapApi
          category: network
          serviceVersion: ${{ parameters.MapApiVersionNumber }}
          deployTag: ${{ parameters['Map-ApiTag'] }}
          bool: ${{ parameters.MapApiBool }}
        - name: Subscription-Api
          folderName: subscriptionApi
          stageName: subscriptionApi
          category: network
          serviceVersion: ${{ parameters.SubscriptionApiVersionNumber }}
          deployTag: ${{ parameters['Subscription-ApiTag'] }}
          bool: ${{ parameters.SubscriptionApiBool }}
        - name: Pulse-Alert-Api
          folderName: pulseAlertApi
          stageName: pulseAlertApi
          category: network
          serviceVersion: ${{ parameters.PulseAlertApiVersionNumber }}
          deployTag: ${{ parameters['Pulse-Alert-ApiTag'] }}
          bool: ${{ parameters.PulseAlertApiBool }}
        - name: paymentCallbackApi
          folderName: paymentCallbackApiStack
          stageName: paymentCallbackApiStack
          category: platform
          serviceVersion: ${{ parameters.PaymentCallbackApiVersionNumber }}
          deployTag: ${{ parameters['Payment-Callback-ApiTag'] }}
          bool: ${{ parameters.PaymentCallbackApiBool }}
        - name: BpChargemasterApi
          folderName: BPChargemasterCPApi
          stageName: BPChargemasterCPApi
          category: network
          serviceVersion: ${{ parameters.BpChargemasterApiVersionNumber }}
          deployTag: ${{ parameters['BpChargemaster-ApiTag'] }}
          bool: ${{ parameters.BpChargemasterApiBool }}
        # Monitoring
        - name: Anonymous-Monitoring
          folderName: anonymousMonitoringStack
          stageName: anonymousMonitoringStack
          category: monitoring
          serviceVersion: ${{ parameters.AnonymousMonitoringVersionNumber }}
          deployTag: ${{ parameters['Anonymous-MonitoringTag'] }}
          bool: ${{ parameters.AnonymousMonitoringBool }}
          preconfig: canary
        - name: Bppay-Monitoring
          folderName: bppayMonitoringStack
          stageName: bppayMonitoringStack
          category: monitoring
          serviceVersion: ${{ parameters.BppayMonitoringVersionNumber }}
          deployTag: ${{ parameters['Bppay-MonitoringTag'] }}
          bool: ${{ parameters.BppayMonitoringBool }}
          preconfig: canary
        - name: Charge-Monitoring
          folderName: chargeMonitoringStack
          stageName: chargeMonitoringStack
          category: monitoring
          serviceVersion: ${{ parameters.ChargeMonitoringVersionNumber }}
          deployTag: ${{ parameters['Charge-MonitoringTag'] }}
          bool: ${{ parameters.ChargeMonitoringBool }}
          preconfig: canary
        - name: PDF-Monitoring
          folderName: pdfMonitoringStack
          stageName: pdfMonitoringStack
          category: monitoring
          serviceVersion: ${{ parameters.PDFMonitoringVersionNumber }}
          deployTag: ${{ parameters['PDF-MonitoringTag'] }}
          bool: ${{ parameters.PDFMonitoringBool }}
          preconfig: canary
        - name: External-Services-Monitoring
          folderName: externalServicesMonitoringStack
          stageName: externalServicesMonitoringStack
          category: monitoring
          serviceVersion: ${{ parameters.ExternalServicesMonitoringVersionNumber }}
          deployTag: ${{ parameters['External-Services-MonitoringTag'] }}
          bool: ${{ parameters.ExternalServicesMonitoringBool }}
          preconfig: canary
        - name: Map-Monitoring
          folderName: mapMonitoringStack
          stageName: mapMonitoringStack
          category: monitoring
          serviceVersion: ${{ parameters.MapMonitoringVersionNumber }}
          deployTag: ${{ parameters['Map-MonitoringTag'] }}
          bool: ${{ parameters.MapMonitoringBool }}
          preconfig: canary
        - name: User-Monitoring
          folderName: userMonitoringStack
          stageName: userMonitoringStack
          category: monitoring
          serviceVersion: ${{ parameters.UserMonitoringVersionNumber }}
          deployTag: ${{ parameters['User-MonitoringTag'] }}
          bool: ${{ parameters.UserMonitoringBool }}
          preconfig: canary
        - name: Wallet-Monitoring
          folderName: walletMonitoringStack
          stageName: walletMonitoringStack
          category: monitoring
          serviceVersion: ${{ parameters.WalletMonitoringVersionNumber }}
          deployTag: ${{ parameters['Wallet-MonitoringTag'] }}
          bool: ${{ parameters.WalletMonitoringBool }}
          preconfig: canary
        - name: Offer-Monitoring
          folderName: offerMonitoringStack
          stageName: offferMonitoringStack
          category: monitoring
          serviceVersion: ${{ parameters.offerMonitoringVersionNumber }}
          deployTag: ${{ parameters['Offer-MonitoringTag'] }}
          bool: ${{ parameters.OfferMonitoringBool }}
          preconfig: canary
        - name: Ocpi-Monitoring
          folderName: ocpiMonitoringStack
          stageName: ocpiMonitoringStack
          category: monitoring
          serviceVersion: ${{ parameters.OcpiMonitoringVersionNumber }}
          deployTag: ${{ parameters['Ocpi-MonitoringTag'] }}
          bool: ${{ parameters.OcpiMonitoringBool }}
          preconfig: canary
        - name: Prices-Monitoring
          folderName: pricesMonitoringStack
          stageName: pricesMonitoringStack
          category: monitoring
          serviceVersion: ${{ parameters.pricesMonitoringVersionNumber }}
          deployTag: ${{ parameters['Prices-MonitoringTag'] }}
          bool: ${{ parameters.PricesMonitoringBool }}
          preconfig: canary
        - name: Subs-Monitoring
          folderName: subsMonitoringStack
          stageName: subsMonitoringStack
          category: monitoring
          serviceVersion: ${{ parameters.SubsMonitoringVersionNumber }}
          deployTag: ${{ parameters['Subs-MonitoringTag'] }}
          bool: ${{ parameters.SubsMonitoringBool }}
          preconfig: canary
        - name: RFID-Monitoring
          folderName: rfidMonitoringStack
          stageName: rfidMonitoringStack
          category: monitoring
          serviceVersion: ${{ parameters.RFIDMonitoringVersionNumber }}
          deployTag: ${{ parameters['RFID-MonitoringTag'] }}
          bool: ${{ parameters.RFIDMonitoringBool }}
          preconfig: canary
        - name: Sap-Integration-Monitoring
          folderName: sapIntegrationMonitoringStack
          stageName: SapIntegrationMonitoringStack
          category: monitoring
          serviceVersion: ${{ parameters.SapIntegrationMonitoringVersionNumber }}
          deployTag: ${{ parameters['Sap-Integration-MonitoringTag'] }}
          bool: ${{ parameters.SapIntegrationMonitoringBool }}
          preconfig: canary
        - name: Registration-Monitoring
          folderName: registrationMonitoringStack
          stageName: registrationMonitoringStack
          category: monitoring
          serviceVersion: ${{ parameters.RegistrationMonitoringVersionNumber }}
          deployTag: ${{ parameters['Registration-MonitoringTag'] }}
          bool: ${{ parameters.RegistrationMonitoringBool }}
        # Unversioned (excludes unversioned services)
        - name: Cloudwatch-Stack
          folderName: cloudwatchStack
          stageName: cloudwatchStack
          category: unversioned
          serviceVersion: ${{ parameters.CloudwatchStackVersionNumber }}
          deployTag: ${{ parameters['Cloudwatch-StackTag'] }}
          bool: ${{ parameters.CloudwatchStackBool }}
        - name: ElasticSearch-Stack
          folderName: elasticSearchStack
          stageName: elasticSearchStack
          category: unversioned
          serviceVersion: ${{ parameters.ElasticSearchStackVersionNumber }}
          deployTag: ${{ parameters['ElasticSearch-StackTag'] }}
          bool: ${{ parameters.ElasticSearchBool }}
