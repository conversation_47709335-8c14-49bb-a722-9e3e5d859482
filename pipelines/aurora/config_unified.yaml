pool:
  name: GenericPoolLinux-SS
trigger: none
parameters:
  - name: environment
    displayName: Environment
    type: string
    default: 'test'
    values:
      - test
      - performance
      - preprod
      - prod
      - us-uat
      - us-prod
  - name: snow_cr
    displayName: SNOW Change Request ID
    type: string
    default: 'Insert Change Request ID when running Prod pipeline'
  - name: snow_create_prod
    displayName: Create Snow CR
    type: boolean
    default: false
  - name: customDescription
    type: string
    default: 'Add reason for prod deployment if using automated CR'
    displayName: Prod Change Description
  - name: WorkItems
    displayName: Work Items
    type: string
    default: 'Work Item'
  - name: auroraconfigTag
    displayName: aurora-config Tag
    type: string
    default: 2024-06-19_07-43-51.dev
    values:
      - 2025-04-17_16-21-49.dev
      - 2025-08-13_13-16-10.test
      - 2025-07-17_12-09-02.preprod
      - 2025-04-03_13-05-47.prod
      - 2025-08-13_13-16-10.performance
      - 2025-08-13_13-16-16.us-uat
      - Custom
  - name: auroraconfigVersionNumber
    displayName: aurora-config Version Number(if Custom)
    type: string
    default: 'Check tag list for Aurora Config(eg: aurora-config.20240207150402)'
stages:
  - ${{ if and(eq(parameters.environment, 'prod'), eq(parameters.snow_create_prod, true)) }}:
      - stage: snow_create_cr
        displayName: 'Service Now Create Change Request'
        variables:
          - group: Pulse-Apps-Infrastructure-prod
        jobs:
          - template: ../jobs/snow/create_cr.yaml
            parameters:
              ${{ if notIn(parameters.customDescription, 'Add description', '', ' ') }}:
                ${{ if eq(parameters.auroraconfigTag, 'Custom' ) }}:
                  finalMessage: |
                    Deploying aurora config with following tags: ${{ parameters.auroraconfigVersionNumber }}
                    Additional info: ${{ parameters.customDescription }}
                ${{ if notIn(parameters.auroraconfigTag, 'Custom' ) }}:
                  finalMessage: |
                    Deploying aurora config with following tags: ${{ parameters.auroraconfigTag }}
                    Additional info: ${{ parameters.customDescription }}
              ${{ else }}:
                ${{ if eq(parameters.auroraconfigTag, 'Custom' ) }}:
                  finalMessage: 'Deploying aurora config with following tags: ${{ parameters.auroraconfigVersionNumber }}'
                ${{ if notIn(parameters.auroraconfigTag, 'Custom' ) }}:
                  finalMessage: 'Deploying aurora config with following tags: ${{ parameters.auroraconfigTag }}'
              Requester: 'BP Pulse'
              WorkItems: ${{ parameters.workItems }}
              customDescription: ${{ parameters.customDescription }}
              templateName: 'bp pulse eMSP production database sync'
  - stage: clean_version_aurora_config
    displayName: Clean aurora-config Version
    dependsOn:
      - ${{ if eq(parameters.environment, 'prod')}}:
          - snow_create_cr
    jobs:
      - template: ../jobs/steps/clean_unified_dropdown_version.yaml
        parameters:
          deployTag: ${{ parameters.auroraconfigTag }}
          serviceVersion: ${{ parameters.auroraconfigVersionNumber }}
  - stage: s3_sync_${{ replace(parameters.environment, '-', '_') }}
    dependsOn:
      - clean_version_aurora_config
    displayName: 's3 sync ${{ parameters.environment }}'
    variables:
      - name: deployTag
        value: $[ stageDependencies.clean_version_aurora_config.clean_version_job.outputs['clean_version_script.deployTag'] ]
      - name: serviceVersion
        value: $[ stageDependencies.clean_version_aurora_config.clean_version_job.outputs['clean_version_script.serviceVersion'] ]
    jobs:
      - template: ../jobs/steps/aurora_s3_sync.yaml
        parameters:
          environment: ${{ parameters.environment }}
          customDescription: ${{ parameters.customDescription }}
          snow_create_prod: ${{ parameters.snow_create_prod }}
          ${{ if eq(parameters.auroraconfigTag, 'Custom' ) }}:
            tag: aurora-config.$(serviceVersion)
            newTag: aurora-config.$(serviceVersion)
          ${{ if ne(parameters.auroraconfigTag, 'Custom' ) }}:
            tag: aurora-config.$(deployTag)
            newTag: aurora-config.$(deployTag)
  - stage: aurora_git_tag_${{ replace(parameters.environment, '-', '_') }}
    dependsOn:
      - s3_sync_${{ replace(parameters.environment, '-', '_') }}
      - clean_version_aurora_config
    displayName: 'Git Tag aurora-config in ${{ parameters.environment }}'
    variables:
      - name: deployTag
        value: $[ stageDependencies.clean_version_aurora_config.clean_version_job.outputs['clean_version_script.deployTag'] ]
      - name: serviceVersion
        value: $[ stageDependencies.clean_version_aurora_config.clean_version_job.outputs['clean_version_script.serviceVersion'] ]
    jobs:
      - template: ../jobs/steps/git_tag.yaml
        parameters:
          infra: false
          environment: ${{ parameters.environment }}
          ${{ if eq(parameters.auroraconfigTag, 'Custom' ) }}:
            tag: aurora-config.$(serviceVersion)
            newTag: aurora-config.$(serviceVersion)
          ${{ if notIn(parameters.auroraconfigTag, 'Custom' ) }}:
            tag: aurora-config.$(deployTag)
            newTag: aurora-config.$(deployTag)
          name: aurora-config
          stageName: aurora-config
  - stage: update_tags_aurora_config
    dependsOn:
      - clean_version_aurora_config
      - aurora_git_tag_${{ replace(parameters.environment, '-', '_') }}
    displayName: 'Update Params aurora-config'
    variables:
      - group: Pulse-Apps-Microservices-test
      - group: Credentials-Apollo-QA
      - name: deployTag
        value: $[ stageDependencies.clean_version_aurora_config.clean_version_job.outputs['clean_version_script.deployTag'] ]
      - name: serviceVersion
        value: $[ stageDependencies.clean_version_aurora_config.clean_version_job.outputs['clean_version_script.serviceVersion'] ]
    jobs:
      - template: ../jobs/steps/updateDropdownTags.yaml
        parameters:
          environment: '${{ parameters.environment }}'
          customDescription: ${{ parameters.customDescription }}
          snow_create_prod: ${{ parameters.snow_create_prod}}
          serviceTag: 'auroraconfigTag'
          #stageName: ${{ stack.stageName }}
          ${{ if eq(parameters.auroraconfigTag, 'Custom' ) }}:
            tagToUpdate: $(serviceVersion)
          ${{ if ne(parameters.auroraconfigTag, 'Custom' ) }}:
            tagToUpdate: $(deployTag)
          fileToEdit: 'aurora/config_unified'
  - ${{ if eq(parameters.environment, 'prod')}}:
      - stage: snow_close_cr
        displayName: 'Service Now Close Change Request'
        variables:
          changeRecordId: $[ stageDependencies.snow_create_cr.create_change_request.outputs['export_change_record_id.changeRecordId']]
        dependsOn:
          - s3_sync_${{ replace(parameters.environment, '-', '_') }}
          - ${{ if eq(parameters.snow_create_prod, true) }}:
              - snow_create_cr
        condition: always()
        jobs:
          - template: ../jobs/snow/close_cr.yaml
            parameters:
              ${{ if eq(parameters.snow_create_prod, true) }}:
                snowCR: $(changeRecordId)
              ${{ else }}:
                snowCR: ${{ parameters.snow_cr }}
