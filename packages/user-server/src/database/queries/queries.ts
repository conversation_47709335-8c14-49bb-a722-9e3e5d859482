export const queries = {
  checkExistingUser: `
    SELECT user_id 
    FROM customer.users 
    WHERE user_authentication_id = $1
  `,

  getOriginID: `
    SELECT origin_type_id 
    FROM customer.origin_type 
    WHERE country = $1
  `,

  getUserTypeID: `
    SELECT user_type_id 
    FROM customer.user_type 
    WHERE user_type_name = $1
  `,

  newUser: `
    INSERT INTO customer.users (
      user_authentication_id, 
      user_salesforce_id, 
      origin_type_id, 
      user_type_id, 
      user_language, 
      user_created_datetime, 
      user_status, 
      user_balance, 
      user_balance_currency,
      user_migration_date
    ) 
    VALUES ($1, 'REPLACED', $2, $3, $4, $5, $6, $7, $8, $9)
    RETURNING 
      (SELECT origin_type_id FROM customer.origin_type WHERE origin_type_id = $2),
      (SELECT user_type_id FROM customer.user_type WHERE user_type_id = $3)
  `,

  getUserInfo: `
    SELECT
      users.user_id,
      user_type.user_type_name,
      users.user_balance,
      users.user_status,
      users.origin_type_id,
      users.partner_type,
      users.user_migration_date,
      tag.tag_id,
      tag.tag_notes,
      tag.tag_card_number,
      tag.tag_serial_number,
      tag.tag_status,
      tag.tag_last_used_datetime,
      tag.tag_created_datetime,
      tag_type.tag_type_name,
      tag_category.tag_category_name,
      user_scheme.user_scheme_id,
      tag_scheme.tag_scheme_id,
      extended_scheme.scheme_id,
      extended_scheme.scheme_name,
      extended_scheme.subscription_plan_id,
      extended_scheme.subs_plan_name,
      extended_scheme.subs_plan_description,
      extended_scheme.subs_plan_created_date,
      extended_scheme.subs_plan_cost,
      extended_scheme.subs_plan_currency,
      extended_scheme.subs_plan_default,
      extended_scheme.subs_plan_duration,
      extended_scheme.subs_plan_updated_date,
      extended_scheme.subs_plan_deletion_date,
      extended_scheme.external_plan_id,
      revenue_plan.revenue_plan_name,
      revenue_plan.revenue_plan_description,
      origin_type.country,
      users.user_cancelled_datetime,
      entitlements.rfid_enabled AS "entitlements_rfid_enabled",
      entitlements.chargepoints_available AS "entitlements_chargepoints_available",
      entitlements.payment_methods AS "entitlements_payment_methods",
      entitlements.partner_schemes AS "entitlements_partner_schemes",
      entitlements.rfid_default_providers AS "entitlements_rfid_default_providers",
      entitlements.subs_enabled AS "entitlements_subs_enabled",
      user_token.token,
      user_token.token_type,
      user_token.token_status,
      user_token.token_expiry_date,
      provider.provider_name,
      revenue_plan.provider_id,
      membership.membership_id,
      membership.membership_status,
      membership.membership_start_date,
      membership.membership_end_date,
      membership.membership_request_cancel_date,
      membership.membership_billing_cycle_date,
      membership.membership_external_id,
      membership.partner_type AS "membership_partner_type",
      membership.user_type_id AS "membership_user_type_id",
      membership.user_id AS "membership_user_id"
    FROM customer.users
    LEFT JOIN customer.tag 
      ON customer.tag.user_id = users.user_id
      AND customer.users.user_authentication_id = $1
    LEFT JOIN customer.tag_type 
      ON customer.tag_type.tag_type_id = customer.tag.tag_type_id
      AND customer.users.user_authentication_id = $1
    LEFT JOIN customer.tag_category 
      ON customer.tag_category.tag_category_id = customer.tag.tag_category_id
      AND customer.users.user_authentication_id = $1
    LEFT JOIN customer.user_scheme 
      ON customer.user_scheme.user_id = users.user_id
      AND customer.users.user_authentication_id = $1
    LEFT JOIN customer.tag_scheme 
      ON customer.tag_scheme.tag_id = customer.tag.tag_id
      AND customer.users.user_authentication_id = $1
    LEFT JOIN customer.origin_type 
      ON customer.origin_type.origin_type_id = customer.users.origin_type_id
      AND customer.users.user_authentication_id = $1
    LEFT JOIN customer.entitlements 
      ON customer.users.origin_type_id = customer.entitlements.origin_entitlement_id
      AND customer.users.user_authentication_id = $1
    LEFT JOIN (
      SELECT
        scheme.scheme_id,
        scheme.scheme_name,
        scheme.subscription_plan_id,
        subs_plan_name,
        subs_plan_description,
        subs_plan_created_date,
        subs_plan_cost,
        subs_plan_currency,
        subs_plan_default,
        subs_plan_duration,
        subs_plan_updated_date,
        subs_plan_deletion_date,
        external_plan_id
      FROM customer.scheme
      LEFT JOIN customer.origin_type 
        ON customer.origin_type.origin_type_id = customer.scheme.origin_type_id
      LEFT JOIN customer.users 
        ON customer.users.user_authentication_id = $1
      LEFT JOIN customer.user_scheme 
        ON customer.user_scheme.user_id = customer.users.user_id
      LEFT JOIN customer.subscription_plan 
        ON customer.scheme.subscription_plan_id = customer.subscription_plan.subscription_plan_id
        AND customer.users.user_authentication_id = $1
      WHERE customer.scheme.scheme_id = customer.user_scheme.scheme_id
        AND ((($2 = '') IS NOT FALSE) OR (customer.origin_type.country = $2))
    ) AS extended_scheme 
      ON extended_scheme.scheme_id = customer.user_scheme.scheme_id
      AND customer.users.user_authentication_id = $1
    LEFT JOIN (
      SELECT
        revenue_plan.revenue_plan_id,
        revenue_plan.revenue_plan_name,
        revenue_plan.revenue_plan_description,
        revenue_plan.provider_id,
        revenue_scheme.scheme_id
      FROM customer.revenue_plan
      LEFT JOIN customer.revenue_scheme 
        ON customer.revenue_scheme.revenue_plan_id = customer.revenue_plan.revenue_plan_id
      LEFT JOIN customer.user_scheme 
        ON customer.user_scheme.user_scheme_id = customer.revenue_scheme.scheme_id
    ) AS revenue_plan 
      ON revenue_plan.scheme_id = customer.user_scheme.scheme_id
      AND customer.users.user_authentication_id = $1
    LEFT JOIN customer.provider 
      ON revenue_plan.provider_id = customer.provider.provider_id
    LEFT JOIN customer.user_token 
      ON customer.user_token.user_id = customer.users.user_id
      AND customer.user_token.last_updated >= now() - interval '1 year'
    LEFT JOIN customer.user_type 
      ON customer.users.user_type_id = customer.user_type.user_type_id
      AND customer.users.user_authentication_id = $1
    LEFT JOIN customer.membership 
      ON customer.membership.user_id = users.user_id
      AND customer.users.user_authentication_id = $1
    WHERE customer.users.user_authentication_id = $1
      AND customer.users.user_status != 'DELETED'
  `,

  getGuestEntitlement: `
    SELECT 
      customer.entitlements.chargepoints_available, 
      customer.entitlements.payment_methods, 
      customer.entitlements.rfid_enabled, 
      customer.entitlements.subs_enabled
    FROM customer.entitlements
    LEFT JOIN customer.origin_type
      ON customer.origin_type.origin_type_id = customer.entitlements.origin_entitlement_id
    WHERE customer.origin_type.country = $1;
  `,

  insertDefaultUserSchema: `
    INSERT INTO customer.user_scheme (scheme_id, user_id) 
    VALUES (
      (
        SELECT customer.scheme.scheme_id 
        FROM customer.scheme 
        LEFT JOIN customer.origin_type
          ON customer.origin_type.origin_type_id = customer.scheme.origin_type_id
        WHERE customer.origin_type.country = $2 
          AND customer.scheme.default = true
      ),
      (
        SELECT customer.users.user_id
        FROM customer.users
        WHERE customer.users.user_authentication_id = $1
      )
    )
  `,

  getUser: `
    SELECT * 
    FROM customer.users 
    WHERE customer.users.user_authentication_id = $1
  `,

  setUserInfo: `
    UPDATE customer.users 
    SET 
      user_notes = $2, 
      user_status = $3, 
      user_balance = $4, 
      user_balance_currency = $5, 
      user_cancelled_datetime = $6, 
      user_cancelled_reason = $7, 
      user_deleted_datetime = $8
    WHERE user_authentication_id = $1
  `,

  getTag: `
    SELECT 
      tag_status, 
      tag_barred_datetime, 
      tag_expires_datetime, 
      tag_created_datetime, 
      tag_serial_number, 
      tag_notes, 
      tag_last_used_datetime, 
      tag_deleted_flag, 
      tag_type_id, 
      tag.tag_category_id, 
      tag_category.tag_category_name, 
      tag_id, 
      tag_card_number, 
      users.user_authentication_id, 
      origin_type.country, 
      tag_updated_datetime
    FROM customer.tag
    LEFT JOIN customer.users
      ON customer.users.user_id = customer.tag.user_id
    LEFT JOIN customer.origin_type
      ON customer.origin_type.origin_type_id = customer.users.origin_type_id
    LEFT JOIN customer.tag_category
      ON customer.tag_category.tag_category_id = customer.tag.tag_category_id
    WHERE customer.tag.tag_card_number = $1 
       OR customer.tag.tag_serial_number = $2
  `,

  getTagByCategoryId: `
    SELECT * 
    FROM customer.tag 
    WHERE tag_category_id = $1 
      AND tag_status = 'ACTIVE'
  `,

  upsertTag: `
    INSERT INTO customer.tag (
      tag_card_number, 
      tag_status, 
      tag_serial_number, 
      tag_notes, 
      tag_deleted_flag, 
      tag_barred_datetime, 
      tag_expires_datetime, 
      tag_last_used_datetime, 
      tag_type_id, 
      tag_category_id, 
      tag_created_datetime, 
      tag_updated_datetime, 
      user_id
    )
    VALUES (
      $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, 
      now(), 
      now(),
      (
        SELECT customer.users.user_id
        FROM customer.users
        WHERE customer.users.user_authentication_id = $11
      )
    )
    ON CONFLICT (tag_card_number) 
    DO UPDATE SET 
      tag_status = $2,
      tag_barred_datetime = $6,
      tag_expires_datetime = $7,
      tag_serial_number = $3,
      tag_notes = $4,
      tag_last_used_datetime = $8,
      tag_deleted_flag = $5,
      tag_type_id = $9,
      tag_category_id = $10,
      tag_updated_datetime = now()
    WHERE customer.tag.tag_card_number = $1
  `,

  insertDefaultTagSchema: `
    INSERT INTO customer.tag_scheme (scheme_id, tag_id)
    VALUES (
      (
        SELECT customer.scheme.scheme_id 
        FROM customer.scheme 
        LEFT JOIN customer.origin_type
          ON customer.origin_type.origin_type_id = customer.scheme.origin_type_id
        WHERE customer.origin_type.country = $1 
          AND customer.scheme.default = true
      ),
      (
        SELECT customer.tag.tag_id 
        FROM customer.tag 
        WHERE customer.tag.tag_card_number = $2
      )
    )
  `,

  insertTagSchema: `
    INSERT INTO customer.tag_scheme (scheme_id, tag_id)
    VALUES (
      (
        SELECT customer.scheme.scheme_id 
        FROM customer.scheme 
        LEFT JOIN customer.origin_type
          ON customer.origin_type.origin_type_id = customer.scheme.origin_type_id
        WHERE customer.origin_type.country = $1 
          AND customer.scheme.default = false 
          AND customer.scheme.scheme_name LIKE '%' || $3 || '%'
      ),
      (
        SELECT customer.tag.tag_id 
        FROM customer.tag 
        WHERE customer.tag.tag_card_number = $2
      )
    )
  `,

  updateAccountStatus: `
    UPDATE customer.users 
    SET user_status = $1 
    WHERE user_authentication_id = ANY($2)
  `,

  updateMembership: `
    UPDATE customer.membership
    SET
      user_type_id = $3,
      membership_status = $4,
      membership_start_date = $5,
      membership_end_date = $6,
      membership_request_cancel_date = $7,
      membership_billing_cycle_date = $8,
      membership_tier = $9,
      partner_type = $10
    WHERE customer.membership.user_id = (
      SELECT user_id
      FROM customer.users
      WHERE user_authentication_id = $1
    )
    AND customer.membership.membership_id = $2
  `,

  getTagType: `
    SELECT tag_type_id
    FROM customer.tag_type
    WHERE customer.tag_type.tag_type_name = $1
  `,

  getAllUpdatedTagData: `
    SELECT 
      users.user_authentication_id,
      users.user_id, 
      user_type.user_type_name,
      users.user_status, 
      tag.tag_barred_datetime, 
      tag.tag_expires_datetime,
      tag.tag_last_used_datetime,
      tag.tag_deleted_flag,
      tag.tag_notes, 
      tag.tag_card_number, 
      tag.tag_serial_number, 
      tag.tag_status, 
      tag_type.tag_type_name, 
      tag_category.tag_category_name, 
      origin_type.country
    FROM customer.users
    LEFT JOIN customer.tag
      ON customer.tag.user_id = users.user_id
    LEFT JOIN customer.tag_type
      ON customer.tag_type.tag_type_id = customer.tag.tag_type_id
    LEFT JOIN customer.tag_category
      ON customer.tag_category.tag_category_id = customer.tag.tag_category_id
    LEFT JOIN customer.user_type
      ON customer.users.user_type_id = customer.user_type.user_type_id
    LEFT JOIN customer.origin_type
      ON customer.origin_type.origin_type_id = customer.users.origin_type_id
    WHERE tag.tag_last_used_datetime BETWEEN $1 AND $2
  `,

  getTagCategory: `
    SELECT tag_category_id
    FROM customer.tag_category
    WHERE customer.tag_category.tag_category_name = $1
  `,

  getTagTypeCount: `
    SELECT count(*)
    FROM customer.tag_type
  `,

  getTagCategoryCount: `
    SELECT count(*)
    FROM customer.tag_category
  `,

  insertTagCategory: `
    INSERT INTO customer.tag_category (tag_category_id, tag_category_name)
    VALUES ($1, $2)
  `,

  insertTagType: `
    INSERT INTO customer.tag_type (tag_type_id, tag_type_name)
    VALUES ($1, $2)
  `,

  updateRevenuePlanInternal: `
    UPDATE customer.revenue_plan 
    SET 
      charge_type_id = $2, 
      scheme_id = $3, 
      revenue_plan_updated_date = $4, 
      revenue_plan_name = $5, 
      revenue_plan_description = $6, 
      revenue_plan_deleted_date = $7, 
      revenue_plan_free_months = $8, 
      revenue_rate_cost = $9, 
      revenue_plan_energy_charge = $10, 
      revenue_plan_time_charge = $11, 
      revenue_plan_effective_date = $12 
    WHERE revenue_plan_id = $1
  `,

  setPartnerType: `
    UPDATE customer.users 
    SET partner_type = $2 
    WHERE user_authentication_id = $1
  `,

  insertUserScheme: `
    INSERT INTO customer.user_scheme (user_id, scheme_id)
    SELECT u.user_id, s.scheme_id
    FROM customer.users u
    JOIN customer.scheme s
      ON u.user_authentication_id = $1
      AND s.scheme_name = $2
  `,

  insertSubscriptionPlan: `
    INSERT INTO customer.subscription_plan (
      subs_plan_name,
      subs_plan_description,
      subs_plan_created_date,
      subs_plan_cost,
      subs_plan_currency,
      subs_plan_default,
      subs_plan_duration,
      subs_plan_updated_date,
      subs_plan_deletion_date,
      external_plan_id
    )
    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
    RETURNING subscription_plan_id, external_plan_id
  `,

  getSubscriptionPlanID: `
    SELECT * 
    FROM customer.subscription_plan 
    WHERE external_plan_id = $1
  `,

  upsertPartnerToken: `
    INSERT INTO customer.user_token (
      user_id, 
      token, 
      token_type, 
      token_status, 
      token_expiry_date, 
      last_updated
    )
    SELECT user_id, $2, $3, $4, $5, $6 
    FROM customer.users 
    WHERE user_authentication_id = $1
    ON CONFLICT (user_id)
    DO UPDATE SET 
      token = EXCLUDED.token, 
      token_status = EXCLUDED.token_status, 
      token_expiry_date = EXCLUDED.token_expiry_date, 
      last_updated = EXCLUDED.last_updated
  `,

  deletePartnerToken: `
    DELETE FROM customer.user_token 
    WHERE token_type = $2 
      AND user_id = (
        SELECT user_id 
        FROM customer.users 
        WHERE user_authentication_id = $1
      )
  `,

  getPartnerToken: `
    SELECT * 
    FROM customer.user_token 
    WHERE user_id = (
      SELECT user_id 
      FROM customer.users 
      WHERE user_authentication_id = $1
    )
  `,

  getSchemeNames: `
    SELECT * 
    FROM customer.scheme
  `,

  getScheme: `
    SELECT * 
    FROM customer.scheme 
    WHERE scheme_id = $1
  `,

  removeUserScheme: `
    DELETE FROM customer.user_scheme 
    WHERE user_id = (
      SELECT user_id 
      FROM customer.users 
      WHERE user_authentication_id = $1
    ) 
    AND scheme_id = (
      SELECT scheme_id 
      FROM customer.scheme 
      WHERE scheme_name = $2
    )
  `,

  removeAllUserSchemes: `
    DELETE FROM customer.user_scheme 
    WHERE user_id = (
      SELECT user_id 
      FROM customer.users 
      WHERE user_authentication_id = $1
    )
  `,

  insertMembership: `
    INSERT INTO customer.membership (
      user_id,
      user_type_id,
      membership_status,
      membership_start_date,
      membership_billing_cycle_date,
      membership_external_id,
      partner_type,
      membership_tier
    )
    VALUES (
      (
        SELECT customer.users.user_id
        FROM customer.users
        WHERE customer.users.user_authentication_id = $1
      ),
      (
        SELECT customer.user_type.user_type_id
        FROM customer.user_type
        WHERE customer.user_type.user_type_name = $2
      ),
      $3,
      now(),
      $4,
      $5,
      $6,
      $7
    )
  `,

  getMembership: `
    SELECT * 
    FROM customer.membership 
    WHERE user_id = (
      SELECT user_id 
      FROM customer.users 
      WHERE user_authentication_id = $1
    ) 
    AND membership_id = $2
  `,

  getSubsMembership: `
    SELECT * 
    FROM customer.membership 
    WHERE user_id = (
      SELECT user_id 
      FROM customer.users 
      WHERE user_authentication_id = $1
    ) 
    AND membership_external_id = $2
  `,

  getUserCountByMembershipId: `
    SELECT count(user_id) AS usercount  
    FROM customer.membership 
    WHERE membership_status = $1 
      AND membership_external_id = $2
  `,

  getActiveMembership: `
    SELECT * 
    FROM customer.membership 
    WHERE user_id = (
      SELECT user_id 
      FROM customer.users 
      WHERE user_authentication_id = $1
    ) 
    AND membership_status = 'ACTIVE'
  `,

  getInactiveMembership: `
    SELECT * 
    FROM customer.membership 
    WHERE user_id = (
      SELECT user_id 
      FROM customer.users 
      WHERE user_authentication_id = $1
    ) 
    AND membership_status = 'INACTIVE'
  `,

  updateUserType: `
    UPDATE customer.users 
    SET user_type_id = (
      SELECT user_type_id 
      FROM customer.user_type 
      WHERE user_type_name = $2
    ) 
    WHERE user_authentication_id = $1
  `,

  updateUserSchemeId: `
    UPDATE customer.user_scheme 
    SET scheme_id = (
      SELECT scheme_id 
      FROM customer.scheme
      WHERE scheme_name LIKE $2 || '%' 
        AND scheme_name LIKE '%' || $3 || '%'
    )
    WHERE user_id = (
      SELECT user_id 
      FROM customer.users 
      WHERE user_authentication_id = $1
    )
  `,

  updateSchemeSubscriptionPlanId: `
    UPDATE customer.scheme 
    SET subscription_plan_id = $1
    WHERE scheme_id = $2
  `,

  getSubscriptionPlan: `
    SELECT * 
    FROM customer.subscription_plan 
    WHERE customer.subscription_plan.subscription_plan_id = $1
  `,

  updateSubscriptionPlan: `
    UPDATE customer.subscription_plan
    SET
      subs_plan_name = $1,
      subs_plan_cost = $2,
      subs_plan_currency = $3,
      subs_plan_duration = $4,
      subs_plan_default = $5,
      subs_plan_updated_date = CURRENT_TIMESTAMP
    WHERE subscription_Plan_id = $6
  `,

  getAllPartnerTariffsInternal: `
    SELECT 
      users.user_authentication_id, 
      users.partner_type, 
      ut.token, 
      scheme.scheme_name, 
      membership.membership_id, 
      users.origin_type_id
    FROM customer.users
    LEFT JOIN (
      SELECT user_id, token, last_updated 
      FROM customer.user_token 
      WHERE token_type = 'Uber' 
        AND date(customer.user_token.last_updated) > (current_timestamp - INTERVAL '1 year')
    ) AS ut
      ON customer.users.user_id = ut.user_id
    JOIN customer.user_scheme
      ON customer.user_scheme.user_id = users.user_id
    JOIN customer.membership
      ON customer.membership.user_id = users.user_id
    JOIN customer.scheme
      ON customer.scheme.scheme_id = user_scheme.scheme_id
    WHERE (users.partner_type IS NOT NULL) 
      AND (users.partner_type = ANY($1::varchar[]) OR $1 IS NULL)
  `,

  getUberUKPartnerTariffsInternal: `
    SELECT 
      u.user_authentication_id, 
      u.partner_type, 
      ut.token, 
      u.origin_type_id
    FROM customer.user_token AS ut
    LEFT JOIN customer.users AS u
      ON ut.user_id = u.user_id
    WHERE u.partner_type = 'Uber'
      AND ut.token_type = 'Uber'
      AND u.origin_type_id = 1
  `,

  getSchemeName: `
    SELECT
      scheme_name,
      scheme_effective_date
    FROM customer.scheme
    INNER JOIN customer.origin_type
      ON scheme.origin_type_id = origin_type.origin_type_id
    WHERE scheme.default = $1
      AND origin_type.country = $2
      AND scheme.scheme_name LIKE ALL($3)
    ORDER BY scheme.scheme_effective_date DESC
  `,

  fetchPlan: `
    SELECT * 
    FROM customer.subscription_plan 
    WHERE subs_plan_default = true 
      AND subs_plan_name LIKE '%' || $1 || '%' 
      OR external_plan_id SIMILAR TO '(' || $2 || ')'
  `,

  getRevenuePlansBySchemeAndProvider: `
    SELECT 
      rp.revenue_plan_name, 
      rp.revenue_plan_effective_date 
    FROM customer.revenue_plan rp 
    INNER JOIN customer.revenue_scheme rs 
      ON rp.revenue_plan_id = rs.revenue_plan_id 
    INNER JOIN customer.scheme s 
      ON rs.scheme_id = s.scheme_id 
    INNER JOIN customer.origin_type ot 
      ON s.origin_type_id = ot.origin_type_id 
    INNER JOIN customer.provider pn 
      ON rp.provider_id = pn.provider_id 
    WHERE s.default = $1 
      AND ot.country = $2 
      AND pn.provider_name = $3 
      AND rp.revenue_plan_description ILIKE ALL($4)
    ORDER BY rp.revenue_plan_effective_date DESC
  `,

  queryOfferPlanByCountryDurationAndCost: `
    SELECT * 
    FROM customer.subscription_plan
    WHERE subs_plan_default = false
      AND subs_plan_duration = $1
      AND subs_plan_cost = $2
      AND subs_plan_name LIKE '%' || $3 || '%'
      AND subs_plan_deletion_date IS NULL
  `,

  queryDefaultSubscriptionPlanByCountry: `
    SELECT * 
    FROM customer.subscription_plan
    WHERE subs_plan_default = true
      AND subs_plan_name LIKE '%' || $1 || '%'
      AND subs_plan_deletion_date IS NULL
  `,

  getPendingSubsMemberships: `
    SELECT
      m.membership_billing_cycle_date,
      m.membership_end_date,
      m.membership_external_id,
      m.membership_id,
      m.membership_request_cancel_date,
      m.membership_start_date,
      m.membership_status,
      m.user_id,
      m.user_type_id
    FROM customer.membership AS m
    LEFT JOIN customer.users AS u 
      ON m.user_id = u.user_id
    LEFT JOIN customer.origin_type AS ot 
      ON u.origin_type_id = ot.origin_type_id
    WHERE m.membership_billing_cycle_date = ANY($1)
      AND u.user_type_id = (
        SELECT user_type_id 
        FROM customer.user_type 
        WHERE user_type_name = 'SUBS-WALLET'
      )
      AND (ot.country = $2 OR $2 IS NULL)
      AND membership_status = 'INACTIVE'
  `,

  getUniqueSubsPlanForCountry: `
    SELECT COUNT(sp.subs_plan_name)
    FROM customer.subscription_plan AS sp
    WHERE sp.subs_plan_default = $1
      AND sp.subs_plan_name LIKE  '% ' || $2 || ' %'
  `,

  softDeleteUser: `
    UPDATE customer.users AS u
    SET
      user_authentication_id = u.user_id,
      user_status = 'DELETED',
      user_deleted_datetime = CURRENT_TIMESTAMP
    WHERE u.user_authentication_id = $1
    RETURNING u.*
  `,

  deleteUser: `
    DELETE FROM customer.users AS u
    WHERE user_authentication_id = $1
    RETURNING u.*
  `,

  getRevenuePlanName: `
    SELECT 
      rp.revenue_plan_name, 
      rp.revenue_plan_effective_date 
    FROM customer.revenue_plan rp 
    INNER JOIN customer.revenue_scheme rs 
      ON rp.revenue_plan_id = rs.revenue_plan_id 
    INNER JOIN customer.scheme s 
      ON rs.scheme_id = s.scheme_id 
    INNER JOIN customer.origin_type ot 
      ON s.origin_type_id = ot.origin_type_id 
    INNER JOIN customer.provider pn 
      ON rp.provider_id = pn.provider_id 
    WHERE ot.country = $1 
      AND pn.provider_name = $2 
      AND s.scheme_name = $3 
    ORDER BY rp.revenue_plan_effective_date DESC
  `,

  getProviderByName: `
    SELECT 
      provider_id, provider_name 
    FROM customer.provider 
    WHERE provider_name = ANY($1)
  `,

  insertTagProvider: `
    INSERT INTO customer.tag_provider (tag_id, provider_id)
    VALUES (
      $1,
      $2
    )
  `,

  upsertTagProvider: `
  INSERT INTO customer.tag_provider (tag_id, provider_id, tag_status)
VALUES ($1, $2, $3)
ON CONFLICT (tag_id, provider_id)
DO UPDATE SET
  tag_status = EXCLUDED.tag_status
WHERE customer.tag_provider.tag_status IS DISTINCT FROM EXCLUDED.tag_status;
`,

  getPendingTagProvidersByCountry: `
    SELECT 
      t.*,
      u.user_authentication_id,
      ot.country,
      ARRAY(
        SELECT UNNEST(e.rfid_default_providers)
        EXCEPT
        SELECT p2.provider_name 
        FROM customer.tag_provider tp2
        JOIN customer.provider p2 ON tp2.provider_id = p2.provider_id
        WHERE tp2.tag_id = t.tag_id
      ) AS missing_providers	  
    FROM customer.tag as t
    JOIN customer.users as u on t.user_id = u.user_id
    JOIN customer.entitlements as e on e.origin_entitlement_id = u.origin_type_id
    JOIN customer.origin_type as ot on u.origin_type_id = ot.origin_type_id
    WHERE t.tag_notes ILIKE 'PHYSICAL-RFID'
    AND ot.country = $1
    AND (
      (
        t.tag_status = 'ACTIVE'
        AND EXISTS (
          SELECT UNNEST(e.rfid_default_providers)
          EXCEPT
          SELECT p2.provider_name 
          FROM customer.tag_provider tp2
          JOIN customer.provider p2 ON tp2.provider_id = p2.provider_id
          WHERE tp2.tag_id = t.tag_id
        )
      )
      OR
      EXISTS (
        SELECT 1
        FROM customer.tag_provider tp
        WHERE tp.tag_id = t.tag_id
          AND tp.tag_status IS DISTINCT FROM t.tag_status
      )
    )
    OFFSET $2
    LIMIT $3
  `,
  getTagByTagId: `SELECT * FROM customer.tag WHERE tag_id = $1`,
};

export const checkLegacyQueries = {
  getMigrations: `
    SELECT name 
    FROM customer.knex_migrations 
    ORDER BY id DESC
  `,
};

export const userDataDumpQueries = {
  User: `
    SELECT
      '' AS Account_Name,
      '' AS PersonAccount_PersonEmail,
      '' AS Account_BillingStreet,
      '' AS Account_BillingState,
      '' AS Account_BillingCountry,
      '' AS Account_BillingCity,
      originType.country AS Account_BPG_Country__pc,
      users.user_authentication_id AS Account_Integration_Id__C,
      'eMSP' AS Account_BPCM_Source_system__c,
      users.user_id AS Account_BPCM_CV_ID__C,
      users.user_balance_currency AS CurrencyISOCode
    FROM customer.users AS users
    INNER JOIN customer.origin_type AS originType
      ON users.origin_type_id = originType.origin_type_id
  `,

  eMSP_Account: `
    SELECT
      users.user_authentication_id AS BPCM_eMSP_Account__c_BPCM_Integration_Id__c,
      users.partner_type AS BPCM_eMSP_Account__c_BPCM_Partner_Type__c,
      users.user_status AS BPCM_EV_Customer_Status__c,
      users.user_created_datetime AS BPCM_eMSP_Account_Created_Date__c,
      userType.user_type_name AS BPCM_User_Type__c,
      scheme.scheme_name AS BPCM_Scheme_Name__c,
      users.user_balance AS BPCM_Customer_Overdrawn_Balance__c
    FROM customer.users AS users
    INNER JOIN user_scheme AS userScheme 
      ON users.user_id = userScheme.user_id
    INNER JOIN customer.scheme AS scheme 
      ON userScheme.scheme_id = scheme.scheme_id
    INNER JOIN customer.user_type AS userType 
      ON users.user_type_id = userType.user_type_id
  `,

  BPCM_Tag_c: `
    SELECT
      users.user_authentication_id AS BPCM_Tag_c_Integration_Id_c,
      tag.tag_id AS BPCM_Tag__c_BPCM_BPCV_Tag_ID__c,
      tag.tag_card_number AS BPCM_Card_Number__c,
      tag.tag_serial_number AS BPCM_Serial_Number__c,
      'eMSP' AS BPCM_Source_System__c,
      '' AS BPCM_Barred__c,
      tag.tag_status AS BPCM_Status__c,
      tag.tag_notes AS BPCM_Tag_Type_c,
      tag.tag_created_datetime AS BPCM_Tag__c_BPCM_Tag_Created_Date__c
    FROM customer.tag AS tag
    INNER JOIN customer.users AS users 
      ON users.user_id = tag.user_id
  `,

  Membership: `
    SELECT
      users.user_authentication_id AS BPCM_Membership_Integration_Id_c,
      users.user_cancelled_datetime AS BPCM_Membership_End_Date__c,
      users.user_created_datetime AS BPCM_Membership_Start_Date__c,
      '' AS BPCM_Actual_Downgrade_Date__c,
      '' AS BPCM_Integration_ID__c,
      '' AS BPCM_Payment_Status__c,
      '' AS BPCM_Registration_Date__c,
      '' AS BPCM_Subscription_Billing_Date__c,
      '' AS BPCM_Subscription_Downgrade_Request_Date__c,
      '' AS BPCM_Subscription_Status__c,
      '' AS BPCM_Subscription_Upgrade_Date__c,
      '' AS BPCM_Trial_Length__c,
      userType.user_type_name AS BPCM_User_Type__c
    FROM customer.users AS users 
    INNER JOIN customer.user_type AS userType
      ON users.user_type_id = userType.user_type_id
  `,
};
