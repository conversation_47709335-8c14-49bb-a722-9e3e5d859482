import axios from 'axios';

import { OrderStatus } from '../../enums';
import { formatUTCDate } from '../../utils/date';
import dateNow from '../../utils/dateNow';
import logger from '../../utils/logger';
import { contextMock } from '../../utils/mock';
import { getThreeDSData, makeWalletMITPayment, preAuth } from './bpPay';

jest.mock('axios');
jest.mock('../../env', () => ({
  DPAAS_PAY_ENDPOINT: 'https://dpaas',
  PAYMENT_REFERENCE_NUMBER: 'true',
}));
jest.spyOn(logger, 'error').mockImplementation(jest.fn());
jest.spyOn(logger, 'info').mockImplementation(jest.fn());
jest.spyOn(global.console, 'log').mockImplementation(jest.fn());

jest.mock('@bp/pulse-common', () => ({
  getDPaaSAuthToken: jest.fn().mockReturnValue(Promise.resolve('12345')),
  TokenType: jest.fn().mockReturnValue(Promise.resolve('12345')),
}));

const mockRequestBpPay = jest.fn();
jest.mock('./request', () => ({
  requestBpPay: () => mockRequestBpPay(),
}));

const INVALID_SCHEMA_MESSAGE = 'invalid-input-schema';
const VALIDATION_ERROR_MESSAGE = 'validation-error';

const dpaasErrorResponseOnZeroAmount = {
  response: {
    status: 400,
    data: [
      {
        errorCode: INVALID_SCHEMA_MESSAGE,
        errorMessage: 'operation amount must be greater than 0',
        errorType: VALIDATION_ERROR_MESSAGE,
        parameterName: 'operation amount',
      },
    ],
  },
};

const payValidationErrorsResponse = {
  response: {
    status: 400,
    data: [
      {
        errorCode: INVALID_SCHEMA_MESSAGE,
        errorMessage: 'operation.amount is required',
        errorType: VALIDATION_ERROR_MESSAGE,
        parameterName: 'operation.amount',
      },
      {
        errorCode: INVALID_SCHEMA_MESSAGE,
        errorMessage: 'operation.currency is required',
        errorType: VALIDATION_ERROR_MESSAGE,
        parameterName: 'operation.currency',
      },
    ],
  },
};
const payValidationErrorsResponseFormatted = {
  status: 400,
  data: [
    {
      errorCode: INVALID_SCHEMA_MESSAGE,
      errorMessage: 'operation.amount is required',
      errorType: VALIDATION_ERROR_MESSAGE,
      parameterName: 'operation.amount',
    },
    {
      errorCode: INVALID_SCHEMA_MESSAGE,
      errorMessage: 'operation.currency is required',
      errorType: VALIDATION_ERROR_MESSAGE,
      parameterName: 'operation.currency',
    },
  ],
};

const fraudServiceDeclinedResponse = {
  status: 200,
  data: {
    id: '89ksu0ss',
    status: 'initialized',
    operationOutcome: {
      fraud: {
        code: 'declined',
      },
      operationUId: '75e88c15-2152-4580-aeb3-9a79267a6eb7',
      result: 'failed',
    },
  },
};

const orderStateChangeConflictErrorsResponse = {
  response: {
    status: 409,
    data: [
      {
        errorCode: 'invalid-order-state-change-requested',
        errorMessage:
          'Cannot [capture] the order [1234] with current state [captured].',
        errorType: VALIDATION_ERROR_MESSAGE,
      },
    ],
  },
};

const gatewayInvalidDataResponse = {
  response: {
    status: 400,
    data: [
      {
        errorCode: 'gateway-invalid-data',
        errorMessage:
          '[{"errorCode":"others","errorMessage":"Mastercard Error: Bad Request (400) - E5603 Context: Fatal initialisation failure, Could not obtain valid Terminal, giving up. MerchantId=2101875578,  AcquirerId=ELAVON_S2A"}]',
        errorType: 'payments-service-validation-error',
      },
    ],
  },
};

const internalServerErrorResponse = {
  response: {
    status: 504,
    data: {
      errorData: {
        errorMessage:
          'Timeout occurred in wallet - [error response: undefined]',
        errorType: 'timeout-error',
      },
    },
  },
};

const paybackPointsResponse = {
  status: 200,
  data: {
    uniqueTransactionId: '20120423202543_214_123',
    transactionProcessingDateTime: '2021-04-14T12:17:30.320Z',
    transactionValue: 10.0,
    mainBalance: 30.0,
    mainPoints: 30.0,
  },
};

const ctx = contextMock();

describe('make Walllet MIT payment call', () => {
  afterEach(() => {
    axios.put.mockReset();
  });

  const data = {
    correlationId: '20120423202543_214_123',
    userId: 'oTTpCYCX5tACKC5X1jincz',
    paymentId: '1234-1234-1234-1234',
    paymentMethodId: '1234-12345-11234',
    operationId: '1234-12345-11234',
    country: 'DE',
    tenant: 'Aral',
    amount: '0',
    currency: 'EUR',
    timestamp: dateNow(),
    txnReferenceNumber: `000CG${formatUTCDate(dateNow())}`,
  };

  const putData = {
    operation: {
      operationUId: data.operationId,
      amount: data.amount,
      currency: data.currency,
      timestamp: data.timestamp,
      txnReferenceNumber: data.txnReferenceNumber,
    },
    paymentMethod: {
      bpWallet: {
        userId: data.userId,
        paymentMethodId: data.paymentMethodId,
      },
    },
    customer: {},
    site: {},
    fraud: {
      skipCheck: true,
    },
  };

  const headers = {
    headers: {
      Authorization: 'Bearer 12345',
      'x-correlation-id': data.correlationId,
      'x-tenant-country': data.country,
      'x-tenant-id': data.tenant,
    },
  };

  const putEndpoint =
    'https://dpaas/dsp/payments/pay/v1/us/orders/1234-1234-1234-1234/pay';

  it('should return payload as object when there are no errors returned from bppay', async () => {
    axios.put.mockImplementation(() => Promise.resolve(paybackPointsResponse));

    const result = await makeWalletMITPayment(ctx, data);

    expect(axios.put).toHaveBeenCalledTimes(1);

    expect(axios.put).toHaveBeenCalledWith(putEndpoint, putData, headers);

    expect(result.uniqueTransactionId).toBe('20120423202543_214_123');
  });

  it('should return payload as an array when there are errors returned from bppay', async () => {
    axios.put.mockImplementation(() =>
      Promise.reject(dpaasErrorResponseOnZeroAmount),
    );

    const result = await makeWalletMITPayment(ctx, data);

    expect(result.status).toBe(OrderStatus.OUTSTANDING);
    expect(result.message).toBe(
      '400 status invalid-input-schema received from DPaaS',
    );
  });

  it('should rejet and throw and error when there is a network error communicating with bppay', async () => {
    const networkError = new Error('Some network error');
    axios.put.mockImplementation(() => Promise.reject(networkError));

    await expect(makeWalletMITPayment(ctx, data)).rejects.toThrow(networkError);
  });

  it('should return Outstanding response when 400 PayValidationErrors are received from bppay', async () => {
    axios.put.mockImplementation(() =>
      Promise.reject(payValidationErrorsResponse),
    );

    const result = await makeWalletMITPayment(ctx, data);

    expect(result.status).toBe(OrderStatus.OUTSTANDING);
    expect(result.message).toBe(
      '400 status invalid-input-schema received from DPaaS',
    );
  });
  it('should return Outstanding response when 400 PayValidationErrors are received from bppay as a success response', async () => {
    axios.put.mockImplementation(() =>
      Promise.resolve(payValidationErrorsResponse),
    );

    const result = await makeWalletMITPayment(ctx, data);

    expect(result.status).toBe(OrderStatus.OUTSTANDING);
    expect(result.message).toBe(
      '400 status invalid-input-schema received from DPaaS',
    );
  });
  it('should return Outstanding response when 400 PayValidationErrors are received from bppay as a success formatted response', async () => {
    axios.put.mockImplementation(() =>
      Promise.resolve(payValidationErrorsResponseFormatted),
    );

    const result = await makeWalletMITPayment(ctx, data);

    expect(result.status).toBe(OrderStatus.OUTSTANDING);
    expect(result.message).toBe(
      '400 status invalid-input-schema received from DPaaS',
    );
  });

  it('should return INITIALIZED response when 200 FraudServiceDeclined is received from bppay', async () => {
    axios.put.mockImplementation(() =>
      Promise.resolve(fraudServiceDeclinedResponse),
    );

    const result = await makeWalletMITPayment(ctx, data);

    expect(result.status).toBe(OrderStatus.INITIALIZED);
  });

  it('should return CAPTURED response when 409 OrderStateChangeConflictErrors is received from bppay', async () => {
    axios.put.mockImplementation(() =>
      Promise.reject(orderStateChangeConflictErrorsResponse),
    );

    const result = await makeWalletMITPayment(ctx, data);

    expect(result.status).toBe(OrderStatus.CAPTURED);
    expect(result.message).toBe(
      '409 status OrderStateChangeConflictErrors received from DPaaS. Already captured',
    );
  });

  it('should return PROCESSING response when 202 status is received from bppay', async () => {
    axios.put.mockImplementation(() =>
      Promise.resolve({ status: 202, data: '' }),
    );

    const result = await makeWalletMITPayment(ctx, data);

    expect(result.status).toBe(OrderStatus.PROCESSING);
    expect(result.message).toBe('202 status received from DPaaS');
  });

  it('should return PROCESSING response when 400 gateway-invalid-data is received from bppay', async () => {
    axios.put.mockImplementation(() =>
      Promise.reject(gatewayInvalidDataResponse),
    );

    const result = await makeWalletMITPayment(ctx, data);

    expect(result.status).toBe(OrderStatus.PROCESSING);
    expect(result.message).toBe('gateway-invalid-data received from DPaaS');
  });

  it('should return PROCESSING response when 400 gateway-invalid-data is received from bppay', async () => {
    axios.put.mockImplementation(() =>
      Promise.reject(internalServerErrorResponse),
    );

    const result = await makeWalletMITPayment(ctx, data);

    expect(result.status).toBe(OrderStatus.PROCESSING);
    expect(result.message).toBe('504 status received from DPaaS');
  });

  it('should send the threeDS object if passed, filtering out any null or empty values and marking the transactionSource as CIT', async () => {
    const threeDS = {
      eciFlag: '02',
      cavv: 'MTIzNDU2Nzg5MDEyMzQ1Njc4OTA=',
      threeDSServerTransactionId: 'ee2ccbdc-dff2-4875-94c6-5b9cce272565',
      paresStatus: 'Y',
      threeDSVersion: '2.1.0',
      dsTransactionId: '951fc940-77a7-4e43-9b94-0b78d8d4dcac',
      acsTransactionId: 'f18ddc4a-c0a6-4c46-974f-0e0e84eec174',
    };

    axios.put.mockImplementation(() => Promise.resolve(paybackPointsResponse));
    await makeWalletMITPayment(ctx, {
      ...data,
      threeDS: {
        ...threeDS,
        enrolled: null,
        statusReason: '',
      },
    });

    expect(axios.put).toHaveBeenCalledTimes(1);

    expect(axios.put).toHaveBeenCalledWith(
      putEndpoint,
      {
        ...putData,
        threeDS,
        paymentMethod: { ...putData.paymentMethod, transactionSource: 'cit' },
      },
      headers,
    );
  });
});

describe('make preauth request', () => {
  const args = {
    journeyId: 'journeyId',
    token: 'token',
    paymentMethodType: 'card',
  };
  it('should return the result of the authorisation step', async () => {
    mockRequestBpPay.mockResolvedValue({
      data: {
        status: 'Authorised',
      },
    });
    const res = await preAuth(ctx, args);
    expect(res).toBe(true);
  });

  it('should log an error if the preauth status is not success', async () => {
    mockRequestBpPay.mockResolvedValue({
      data: {
        status: 'Failed',
      },
    });
    const res = await preAuth(ctx, args);
    expect(res).toBe(false);
    expect(logger.error).toHaveBeenLastCalledWith(
      'Status not authorised: Failed',
      {
        operationType: 'response',
        response: { status: 'Failed' },
        serviceApi: 'preAuth',
        serviceName: 'DPaaS',
        status: 'Failed',
      },
    );
  });

  it('should throw an error if the call to Bppay fails', async () => {
    const err = new Error('Failed to bppay');
    mockRequestBpPay.mockRejectedValue(err);

    await expect(preAuth(ctx, args)).rejects.toThrow(err);

    expect(logger.error).toHaveBeenCalledWith(
      'preAuth error',
      expect.objectContaining({ serviceApi: 'preAuth' }),
    );
  });
});

describe('getThreeDSData', () => {
  it('should return undefined if input is undefined', () => {
    expect(getThreeDSData(undefined)).toBeUndefined();
  });

  it('should return undefined if all fields are null, undefined, or empty string', () => {
    const input = {
      eciFlag: '',
      enrolled: null,
      cavv: undefined,
      threeDSServerTransactionId: '',
      paresStatus: '',
      threeDSVersion: null,
      statusReason: '',
      dsTransactionId: undefined,
      acsTransactionId: '',
    };
    expect(getThreeDSData(input)).toBeUndefined();
  });

  it('should return only the non-empty fields', () => {
    const input = {
      eciFlag: '05',
      enrolled: '',
      cavv: null,
      threeDSServerTransactionId: 'abc123',
      paresStatus: '',
      threeDSVersion: undefined,
      statusReason: '',
      dsTransactionId: 'ds-123',
      acsTransactionId: '',
    };

    expect(getThreeDSData(input)).toEqual({
      eciFlag: '05',
      threeDSServerTransactionId: 'abc123',
      dsTransactionId: 'ds-123',
    });
  });

  it('should return all fields if all are valid', () => {
    const input = {
      eciFlag: '05',
      enrolled: 'Y',
      cavv: 'abc',
      threeDSServerTransactionId: '123',
      paresStatus: 'A',
      threeDSVersion: '2.1.0',
      statusReason: '01',
      dsTransactionId: 'ds-123',
      acsTransactionId: 'acs-456',
    };

    expect(getThreeDSData(input)).toEqual(input);
  });
});
