import {
  OperationType,
  OrderStatus,
  PaymentService,
  PaymentServiceAPI,
} from '../../enums';
import { BpPayServerContext } from '../../types/apollo';
import logger from '../../utils/logger';
import {
  IMakeWalletMITPaymentParams,
  IThreeDS,
  walletRequestObjectParams,
} from './interfaces';

export const getResponse = (
  message: string,
  correlationId: string,
  userId: string,
  status: OrderStatus,
  response: any,
) => {
  logger.info(
    `makeWalletMITPayment: dpaas make walletMITPayment response: ${JSON.stringify(
      response?.data,
    )} status: ${
      response?.status
    } for userId ${userId} and correlationId ${correlationId}`,
  );
  return {
    status,
    message,
    correlationId,
    userId,
  };
};

export const formatAxiosResponse = (response: any) => {
  return response?.response || response;
};

export const processDPaaSErrorResponses = (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  response: any,
  userId: string,
  correlationId: string,
) => {
  const dpaasResponse = formatAxiosResponse(response);
  // Handle custom error scenarios from DPaaS
  if (dpaasResponse?.status == 409) {
    return getResponse(
      '409 status OrderStateChangeConflictErrors received from DPaaS. Already captured',
      correlationId,
      userId,
      OrderStatus.CAPTURED,
      dpaasResponse,
    );
  } else if (
    dpaasResponse?.status == 400 &&
    dpaasResponse?.data &&
    dpaasResponse?.data[0]?.errorCode == 'gateway-invalid-data' &&
    dpaasResponse?.data[0]?.errorMessage.includes(
      'E5603 Context: Fatal initialisation failure',
    )
  ) {
    return getResponse(
      `gateway-invalid-data received from DPaaS`,
      correlationId,
      userId,
      OrderStatus.PROCESSING,
      dpaasResponse,
    );
  } else if (dpaasResponse?.status == 504) {
    return getResponse(
      `504 status received from DPaaS`,
      correlationId,
      userId,
      OrderStatus.PROCESSING,
      dpaasResponse,
    );
  } else if (
    dpaasResponse?.status == 400 &&
    dpaasResponse?.data &&
    dpaasResponse?.data[0]?.errorCode == 'invalid-input-schema'
  ) {
    return getResponse(
      `400 status invalid-input-schema received from DPaaS`,
      correlationId,
      userId,
      OrderStatus.OUTSTANDING,
      dpaasResponse,
    );
  } else if (
    dpaasResponse?.status == 400 &&
    dpaasResponse?.data &&
    dpaasResponse?.data[0]?.errorCode == 'wallet-invalid-data'
  ) {
    return getResponse(
      `400 status wallet-invalid-data received by DPaaS. Wallet payment method provisioned by different tenant`,
      correlationId,
      userId,
      OrderStatus.OUTSTANDING,
      dpaasResponse,
    );
  } else {
    return null;
  }
};

export const processDPaaSSuccessResponses = (
  ctx: BpPayServerContext,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  response: any,
  userId: string,
  correlationId: string,
) => {
  const dpaasResponse = formatAxiosResponse(response);

  ctx.logger.info('makeWalletMITPayment success', {
    serviceName: PaymentService.DPaaS,
    serviceApi: PaymentServiceAPI.PAY,
    operationType: OperationType.Response,
    serviceApiResponseCode: dpaasResponse.status,
    response: dpaasResponse.data,
    correlationId,
  });

  // Handle custom success scenarios from DPaaS
  if (dpaasResponse?.status == 202) {
    return getResponse(
      '202 status received from DPaaS',
      correlationId,
      userId,
      OrderStatus.PROCESSING,
      dpaasResponse,
    );
  }

  if (!Array.isArray(dpaasResponse.data)) {
    // Return data in case of success response
    return dpaasResponse.data;
  }

  // Return default initialised response in all other cases
  return {
    status: OrderStatus.INITIALIZED,
    message: dpaasResponse?.data,
    correlationId,
    userId,
  };
};

export const getMakeWalletRequestObject = ({
  userId,
  paymentMethodId,
  operationId,
  amount,
  currency,
  timestamp,
  threeDS,
  txnReferenceNumber,
  PAYMENT_REFERENCE_ENABLED,
}: walletRequestObjectParams) => {
  const data: IMakeWalletMITPaymentParams = {
    operation: {
      operationUId: operationId,
      amount,
      currency,
      timestamp,
      txnReferenceNumber,
    },
    paymentMethod: {
      bpWallet: {
        userId,
        paymentMethodId,
      },
    },
    customer: {},
    site: {},
    fraud: {
      skipCheck: true,
    },
  };
  if (!PAYMENT_REFERENCE_ENABLED) {
    delete data.operation.txnReferenceNumber;
  }

  if (threeDS) {
    // dpaas/mastercard does not support empty or null values
    for (const variable in threeDS) {
      if (
        Object.hasOwn(threeDS, variable) &&
        !threeDS[variable as keyof IThreeDS]
      ) {
        delete threeDS[variable as keyof IThreeDS];
      }
    }
    data.threeDS = threeDS;
    data.paymentMethod.transactionSource = 'cit';
  }
  return data;
};
