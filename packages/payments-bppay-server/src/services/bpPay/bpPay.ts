// This must be imported first so that the getDpaasAuthToken lib can consume the process.env
// eslint-disable-next-line simple-import-sort/imports
import env from '../../env';

import { getDPaaSAuthToken, TokenType } from '@bp/pulse-common';
import axios, { AxiosResponse } from 'axios';

import {
  Countries,
  DataStatus,
  OperationType,
  PaymentMethodType,
  PaymentService,
  PaymentServiceAPI,
} from '../../enums';
import { BpPayServerContext } from '../../types/apollo';
import { ThreeDs } from '../../types/graphql';
import {
  AuthoriseWalletPaymentInterface,
  AuthoriseWalletPaymentResponse,
  CapturePaymentInterface,
  CapturePaymentResponse,
  CreateOrderInterface,
  CreateOrderResponse,
  IAuthoriseWalletPaymentParams,
  ICapturePaymentParams,
  IRefundOrderParams,
  IThreeDS,
  IVoidOrderParams,
  MakePaymentInterface,
  MakeWalletMITPaymentInterface,
  PreAuthInterface,
  RefundOrderInterface,
  StartJourneyInterface,
  StartJourneyResponse,
  VoidOrderInterface,
  VoidOrderResponse,
  VoidTransactionInterface,
} from './interfaces';
import { requestBpPay } from './request';
import {
  getMakeWalletRequestObject,
  processDPaaSErrorResponses,
  processDPaaSSuccessResponses,
} from './utils';

const INTEGRATION_TEST_SCOPE = 'integration-test';
const PAYMENT_REFERENCE_ENABLED = env.PAYMENT_REFERENCE_NUMBER === 'true';

const setAuthRequestForPaymentMethod = (
  journeyId: string,
  token: string,
  paymentMethodType: string,
) => {
  const mastercardRequestData = {
    journeyId,
    paymentMethod: {
      vaultType: 'Mastercard',
      tokenType: 'SessionId',
      tokenValue: token,
    },
  };

  const payPalRequestData = { journeyId };

  return paymentMethodType === PaymentMethodType.Mastercard
    ? mastercardRequestData
    : payPalRequestData;
};

const setDPaaSHeaders = (
  authToken: string,
  tenant: string,
  country: string,
  correlationId?: string,
) => {
  return correlationId
    ? {
        headers: {
          Authorization: `Bearer ${authToken}`,
          'x-correlation-id': correlationId,
          'x-tenant-id': tenant,
          'x-tenant-country': country,
        },
      }
    : {
        headers: {
          Authorization: `Bearer ${authToken}`,
          'x-tenant-id': tenant,
          'x-tenant-country': country,
        },
      };
};

const getDPaaSHeaders = async (
  ctx: BpPayServerContext,
  tenant: string,
  country: string,
  correlationId?: string,
): Promise<any> => {
  const authToken = await getDPaaSAuthToken(TokenType.PAY).catch((err) => {
    ctx.logger.error('getDPaaSAuthToken failed', {
      serviceName: PaymentService.DPaaS,
      err,
      serviceApi: PaymentServiceAPI.TOKEN,
      operationType: OperationType.Response,
    });

    throw err;
  });
  const tenantCountry = country === Countries.UK ? Countries.GB : country;
  if (authToken) {
    return setDPaaSHeaders(authToken, tenant, tenantCountry, correlationId);
  }
  ctx.logger.error(
    'getDPaaSHeaders: There was a problem fetching DPaaS Auth token',
    {
      serviceName: PaymentService.DPaaS,
      serviceApi: PaymentServiceAPI.TOKEN,
      operationType: OperationType.Response,
    },
  );
  return null;
};

export const preAuth = async (
  ctx: BpPayServerContext,
  { journeyId, token, paymentMethodType }: PreAuthInterface,
) => {
  const requestPayload = setAuthRequestForPaymentMethod(
    journeyId,
    token,
    paymentMethodType,
  );

  ctx.logger.info('preAuth triggered', {
    serviceName: PaymentService.DPaaS,
    serviceApi: PaymentServiceAPI.PRE_AUTH,
    operationType: OperationType.Request,
    requestPayload,
  });

  const response: any = await requestBpPay('/authorise', requestPayload).catch(
    (err) => {
      ctx.logger.error(`preAuth error`, {
        serviceName: PaymentService.DPaaS,
        serviceApi: PaymentServiceAPI.PRE_AUTH,
        operationType: OperationType.Response,
        err,
      });
      throw err;
    },
  );
  const preauthorised = response.data.status === DataStatus.Authorised;
  if (!preauthorised) {
    ctx.logger.error(`Status not authorised: ${response.data.status}`, {
      serviceName: PaymentService.DPaaS,
      serviceApi: PaymentServiceAPI.PRE_AUTH,
      operationType: OperationType.Response,
      status: response.data.status,
      response: response.data,
    });
  }
  return preauthorised;
};

export const startJourney = async (
  ctx: BpPayServerContext,
  {
    paymentId: orderId,
    preAuthAmount,
    preAuthCurrency,
    paymentMethodType,
    tenantConfig,
  }: StartJourneyInterface,
): Promise<StartJourneyResponse> => {
  const requestPayload = {
    order: { orderId },
    authAmount: { amount: preAuthAmount, currency: preAuthCurrency },
    tenantConfig,
    paymentMethodType,
  };

  ctx.logger.info('startJourney triggered', {
    serviceName: PaymentService.DPaaS,
    serviceApi: PaymentServiceAPI.START_JOURNEY,
    operationType: OperationType.Request,
    requestPayload,
    orderId,
  });

  const response: any = await requestBpPay(
    '/startjourney',
    requestPayload,
  ).catch((err) => {
    ctx.logger.error(`startJourney error`, {
      serviceName: PaymentService.DPaaS,
      serviceApi: PaymentServiceAPI.START_JOURNEY,
      operationType: OperationType.Response,
      err,
    });
    throw err;
  });

  ctx.logger.info('startJourney response', {
    serviceName: PaymentService.DPaaS,
    serviceApi: PaymentServiceAPI.START_JOURNEY,
    operationType: OperationType.Response,
    serviceApiResponseCode: response.status,
    response: response.data,
  });

  const {
    status,
    result: { journeyId, dropInSession },
  } = response.data;

  if (status === 'JourneyStarted') {
    ctx.logger.info(`Started Journey for order: ${orderId}`, {
      serviceName: PaymentService.DPaaS,
      serviceApi: PaymentServiceAPI.START_JOURNEY,
      orderId,
      status,
    });
    return { journeyId, dropInSession };
  } else {
    ctx.logger.error(`Status not JourneyStarted`, {
      serviceName: PaymentService.DPaaS,
      serviceApi: PaymentServiceAPI.START_JOURNEY,
      orderId,
      status,
    });

    throw new Error('Start Journey Error, Journey not started');
  }
};

export const setVoidTransaction = async (
  ctx: BpPayServerContext,
  { journeyId }: VoidTransactionInterface,
) => {
  const requestPayload = {
    journeyId,
  };

  ctx.logger.info('setVoidTransaction', {
    serviceName: PaymentService.DPaaS,
    serviceApi: PaymentServiceAPI.VOID,
    operationType: OperationType.Request,
    requestPayload,
  });

  const response: any = await requestBpPay('/void', requestPayload).catch(
    (err) => {
      ctx.logger.error(`setVoidTransaction error`, {
        serviceName: PaymentService.DPaaS,
        serviceApi: PaymentServiceAPI.VOID,
        operationType: OperationType.Response,
        err,
      });

      throw err;
    },
  );

  ctx.logger.info('setVoidTransaction', {
    serviceName: PaymentService.DPaaS,
    serviceApi: PaymentServiceAPI.VOID,
    operationType: OperationType.Response,
    serviceApiResponseCode: response.status,
    response: response.data,
  });

  return response.data.status === DataStatus.Voided;
};

export const makePayment = async (
  ctx: BpPayServerContext,
  { journeyId, amount, currency, userId }: MakePaymentInterface,
) => {
  const requestPayload = {
    journeyId,
    captureAmount: { amount, currency },
    eventCallback: {
      customer: userId,
    },
  };

  ctx.logger.info('makePayment', {
    serviceName: PaymentService.DPaaS,
    serviceApi: PaymentServiceAPI.CAPTURE,
    operationType: OperationType.Request,
    requestPayload,
  });

  const response: any = await requestBpPay(
    '/async/capture',
    requestPayload,
  ).catch((err) => {
    ctx.logger.error(`makePayment error`, {
      serviceName: PaymentService.DPaaS,
      serviceApi: PaymentServiceAPI.CAPTURE,
      operationType: OperationType.Response,
      requestPayload,
      err,
    });
    throw err;
  });

  ctx.logger.info('makePayment', {
    serviceName: PaymentService.DPaaS,
    serviceApi: PaymentServiceAPI.CAPTURE,
    operationType: OperationType.Response,
    serviceApiResponseCode: response.status,
    response: response.data,
  });

  return response.data.status === DataStatus.Success;
};

export const createOrder = async (
  ctx: BpPayServerContext,
  {
    paymentId: orderId,
    merchantId,
    country,
    tenant,
    correlationId,
  }: CreateOrderInterface,
) => {
  const headers = await getDPaaSHeaders(ctx, tenant, country, correlationId);
  const DPAAS_PAY_ENDPOINT =
    ctx.scope === INTEGRATION_TEST_SCOPE
      ? env.MOCK_DPAAS_PAY_ENDPOINT
      : env.DPAAS_PAY_ENDPOINT;

  const data = {
    orderIdentifier: orderId,
    merchant: {
      id: merchantId,
    },
  };

  ctx.logger.info('createOrder', {
    serviceName: PaymentService.DPaaS,
    serviceApi: PaymentServiceAPI.CREATE,
    operationType: OperationType.Request,
    requestPayload: data,
    correlationId,
    orderId,
  });

  const response: AxiosResponse = await axios
    .post(`${DPAAS_PAY_ENDPOINT}/dsp/payments/pay/v1/us/orders`, data, headers)

    .catch((err) => {
      ctx.logger.error(`createOrder error`, {
        serviceName: PaymentService.DPaaS,
        serviceApi: PaymentServiceAPI.CREATE,
        operationType: OperationType.Response,
        requestPayload: data,
        correlationId,
        orderId,
        err,
      });
      throw err;
    });

  ctx.logger.info('createOrder', {
    serviceName: PaymentService.DPaaS,
    serviceApi: PaymentServiceAPI.CREATE,
    operationType: OperationType.Response,
    serviceApiResponseCode: response.status,
    response: response.data,
    correlationId,
    orderId,
  });

  return response.data as CreateOrderResponse;
};

export const makeWalletMITPayment = async (
  ctx: BpPayServerContext,
  {
    correlationId,
    userId,
    paymentId: orderId,
    paymentMethodId,
    operationId,
    country,
    tenant,
    amount,
    currency,
    timestamp,
    threeDS,
    txnReferenceNumber,
  }: MakeWalletMITPaymentInterface,
  scope?: string,
  outstanding?: boolean,
) => {
  const headers = await getDPaaSHeaders(ctx, tenant, country, correlationId);

  const data = getMakeWalletRequestObject({
    userId,
    paymentMethodId,
    operationId,
    amount,
    currency,
    timestamp,
    threeDS,
    txnReferenceNumber,
    PAYMENT_REFERENCE_ENABLED,
  });

  const DPAAS_PAY_ENDPOINT =
    scope === INTEGRATION_TEST_SCOPE
      ? env.MOCK_DPAAS_PAY_ENDPOINT
      : env.DPAAS_PAY_ENDPOINT;

  ctx.logger.info('makeWalletMITPayment', {
    serviceName: PaymentService.DPaaS,
    serviceApi: PaymentServiceAPI.PAY,
    operationType: OperationType.Request,
    requestPayload: data,
    correlationId,
    orderId,
  });

  const response: any = await axios
    .put(
      `${DPAAS_PAY_ENDPOINT}/dsp/payments/pay/v1/us/orders/${orderId}/pay`,
      data,
      headers,
    )
    .then((response) => {
      const failureResponse = processDPaaSErrorResponses(
        response,
        userId,
        correlationId,
      );

      if (!failureResponse) {
        return processDPaaSSuccessResponses(
          ctx,
          response,
          userId,
          correlationId,
        );
      }

      return failureResponse;
    })
    .catch((err) => {
      const status = err?.response?.status;
      const data = err?.response?.data;
      ctx.logger.error('makeWalletMITPayment error', {
        serviceName: PaymentService.DPaaS,
        serviceApi: PaymentServiceAPI.PAY,
        operationType: OperationType.Response,
        serviceApiResponseCode: status,
        err,
        type: outstanding ? 'CIT payment' : 'MIT payment',
        correlationId,
        orderId,
        errorData: data,
      });

      const validErrorResponse = processDPaaSErrorResponses(
        err,
        userId,
        correlationId,
      );

      if (!validErrorResponse) {
        throw err;
      }
      return validErrorResponse;
    });

  ctx.logger.info('makeWalletMITPayment', {
    serviceName: PaymentService.DPaaS,
    serviceApi: PaymentServiceAPI.PAY,
    operationType: OperationType.Response,
    serviceApiResponseCode: response.status,
    response: response.data,
    correlationId,
    orderId,
  });

  return response;
};

export const getThreeDSData = (threeDS?: ThreeDs): IThreeDS | undefined => {
  if (!threeDS) {
    return undefined;
  }

  const rawData: Partial<ThreeDs> = {
    eciFlag: threeDS.eciFlag,
    enrolled: threeDS.enrolled,
    cavv: threeDS.cavv,
    threeDSServerTransactionId: threeDS.threeDSServerTransactionId,
    paresStatus: threeDS.paresStatus,
    threeDSVersion: threeDS.threeDSVersion,
    statusReason: threeDS.statusReason,
    dsTransactionId: threeDS.dsTransactionId,
    acsTransactionId: threeDS.acsTransactionId,
  };

  // Remove keys with undefined, null, or empty string values
  // DPaaS does not support empty or null values
  const cleaned = Object.fromEntries(
    Object.entries(rawData).filter(
      ([_, v]) => v !== undefined && v !== null && v !== '',
    ),
  );

  return Object.keys(cleaned).length > 0 ? cleaned : undefined;
};

export const authoriseWalletPayment = async (
  ctx: BpPayServerContext,
  {
    paymentId: orderId,
    operationUId,
    amount,
    currency,
    timestamp,
    txnReferenceNumber,
    paymentMethodId,
    userId,
    threeDS,
    country,
    tenant,
    correlationId,
  }: AuthoriseWalletPaymentInterface,
  scope?: string,
) => {
  const headers = await getDPaaSHeaders(ctx, tenant, country, correlationId);

  const DPAAS_PAY_ENDPOINT =
    scope === INTEGRATION_TEST_SCOPE
      ? env.MOCK_DPAAS_PAY_ENDPOINT
      : env.DPAAS_PAY_ENDPOINT;

  const data: IAuthoriseWalletPaymentParams = {
    operation: {
      operationUId,
      amount: amount ?? undefined,
      currency,
      timestamp,
      timeZoneCode: 'America/Chicago',
      txnReferenceNumber,
    },
    threeDS,
    paymentMethod: {
      bpWallet: {
        paymentMethodId,
        userId,
      },
    },
    customer: {
      id: userId,
    },
    pos: {
      id: Date.now().toString().slice(8, 12),
    },
  };

  if (!PAYMENT_REFERENCE_ENABLED) {
    delete data.operation.txnReferenceNumber;
  }

  ctx.logger.info('authoriseWalletPayment', {
    serviceName: PaymentService.DPaaS,
    serviceApi: PaymentServiceAPI.AUTHORIZE,
    operationType: OperationType.Request,
    requestPayload: data,
    orderId,
    userId,
    correlationId,
  });

  const response: AxiosResponse = await axios
    .put(
      `${DPAAS_PAY_ENDPOINT}/dsp/payments/pay/v1/us/orders/${orderId}/authorize`,
      data,
      headers,
    )
    .catch((err) => {
      const status = err?.response?.status;
      const data = err?.response?.data;

      ctx.logger.error(`authoriseWalletPayment error`, {
        serviceName: PaymentService.DPaaS,
        serviceApi: PaymentServiceAPI.AUTHORIZE,
        operationType: OperationType.Response,
        serviceApiResponseCode: status,
        orderId,
        err,
        errorData: data,
        correlationId,
      });

      throw err;
    });

  ctx.logger.info('authoriseWalletPayment', {
    serviceName: PaymentService.DPaaS,
    serviceApi: PaymentServiceAPI.AUTHORIZE,
    operationType: OperationType.Response,
    serviceApiResponseCode: response?.status,
    response: response?.data,
    orderId,
    correlationId,
  });

  return response?.data as AuthoriseWalletPaymentResponse;
};

export const capturePayment = async (
  ctx: BpPayServerContext,
  {
    paymentId: orderId,
    operationUId,
    amount,
    currency,
    timestamp,
    country,
    tenant,
    txnReferenceNumber,
    correlationId,
  }: CapturePaymentInterface,
) => {
  const headers = await getDPaaSHeaders(
    ctx,
    tenant,
    country ?? '',
    correlationId,
  );

  const DPAAS_PAY_ENDPOINT =
    ctx.scope === INTEGRATION_TEST_SCOPE
      ? env.MOCK_DPAAS_PAY_ENDPOINT
      : env.DPAAS_PAY_ENDPOINT;

  const data: ICapturePaymentParams = {
    operation: {
      operationUId,
      amount,
      currency: currency ?? '',
      timestamp,
      txnReferenceNumber,
    },
  };

  if (!PAYMENT_REFERENCE_ENABLED) {
    delete data.operation.txnReferenceNumber;
  }

  ctx.logger.info('capturePayment', {
    serviceName: PaymentService.DPaaS,
    serviceApi: PaymentServiceAPI.CAPTURE,
    operationType: OperationType.Request,
    requestPayload: data,
    orderId,
    correlationId,
  });

  const response: AxiosResponse = await axios
    .put(
      `${DPAAS_PAY_ENDPOINT}/dsp/payments/pay/v1/us/orders/${orderId}/capture`,
      data,
      headers,
    )
    .catch((err) => {
      const status = err?.response?.status;
      const data = err?.response?.data;

      ctx.logger.error(`capturePayment call to dpaas failed`, {
        serviceName: PaymentService.DPaaS,
        serviceApi: PaymentServiceAPI.CAPTURE,
        operationType: OperationType.Response,
        serviceApiResponseCode: status,
        orderId,
        err,
        errorData: data,
        correlationId,
      });

      throw err;
    });

  ctx.logger.info('capturePayment', {
    serviceName: PaymentService.DPaaS,
    serviceApi: PaymentServiceAPI.CAPTURE,
    operationType: OperationType.Response,
    serviceApiResponseCode: response?.status,
    response: response?.data,
    orderId,
    correlationId,
  });

  return response?.data as CapturePaymentResponse;
};

export const voidOrderDpaas = async ({
  ctx,
  paymentId: orderId,
  operationUId,
  timestamp,
  country,
  txnReferenceNumber,
  correlationId,
  tenant,
}: VoidOrderInterface) => {
  const headers = await getDPaaSHeaders(ctx, tenant, country, correlationId);

  const DPAAS_PAY_ENDPOINT =
    ctx.scope === INTEGRATION_TEST_SCOPE
      ? env.MOCK_DPAAS_PAY_ENDPOINT
      : env.DPAAS_PAY_ENDPOINT;

  const data: IVoidOrderParams = {
    operation: {
      operationUId,
      timestamp,
      txnReferenceNumber,
    },
  };

  if (!PAYMENT_REFERENCE_ENABLED) {
    delete data.operation.txnReferenceNumber;
  }

  ctx.logger.info('voidOrder', {
    serviceName: PaymentService.DPaaS,
    serviceApi: PaymentServiceAPI.VOID,
    operationType: OperationType.Request,
    requestPayload: data,
    orderId,
    correlationId,
  });

  const response: AxiosResponse = await axios
    .put(
      `${DPAAS_PAY_ENDPOINT}/dsp/payments/pay/v1/us/orders/${orderId}/void`,
      data,
      headers,
    )
    .catch((err) => {
      const status = err?.response?.status;
      const data = err?.response?.data;

      ctx.logger.error(`call to dpaas failed`, {
        serviceName: PaymentService.DPaaS,
        serviceApi: PaymentServiceAPI.VOID,
        operationType: OperationType.Response,
        serviceApiResponseCode: status,
        orderId,
        err,
        errorData: data,
        correlationId,
      });
      throw err;
    });

  ctx.logger.info('voidOrder', {
    serviceName: PaymentService.DPaaS,
    serviceApi: PaymentServiceAPI.VOID,
    operationType: OperationType.Response,
    serviceApiResponseCode: response?.status,
    response: response?.data,
    orderId,
    correlationId,
  });

  return response.data as VoidOrderResponse;
};

export const refundOrder = async (
  ctx: BpPayServerContext,
  {
    paymentId,
    country,
    tenant,
    correlationId,
    operationUId,
    amount,
    currency,
    timestamp,
    txnReferenceNumber,
    userId,
  }: RefundOrderInterface,
  scope?: string,
) => {
  const headers = await getDPaaSHeaders(ctx, tenant, country, correlationId);
  const DPAAS_PAY_ENDPOINT =
    scope === INTEGRATION_TEST_SCOPE
      ? env.MOCK_DPAAS_PAY_ENDPOINT
      : env.DPAAS_PAY_ENDPOINT;
  const data: IRefundOrderParams = {
    operation: {
      operationUId,
      amount,
      currency,
      timestamp,
      txnReferenceNumber,
    },
    site: {},
    customer: {
      id: userId,
    },
  };
  if (!PAYMENT_REFERENCE_ENABLED) {
    delete data.operation.txnReferenceNumber;
  }

  ctx.logger.info('refundOrder', {
    serviceName: PaymentService.DPaaS,
    serviceApi: PaymentServiceAPI.REFUND,
    operationType: OperationType.Request,
    requestPayload: data,
    orderId: paymentId,
  });

  const response: AxiosResponse = await axios
    .put(
      `${DPAAS_PAY_ENDPOINT}/dsp/payments/pay/v1/us/orders/${paymentId}/refund`,
      data,
      headers,
    )
    .catch((err) => {
      ctx.logger.error(`refundOrder error`, {
        serviceName: PaymentService.DPaaS,
        serviceApi: PaymentServiceAPI.REFUND,
        operationType: OperationType.Response,
        orderId: paymentId,
        err,
      });

      throw err;
    });

  ctx.logger.info('refundOrder', {
    serviceName: PaymentService.DPaaS,
    serviceApi: PaymentServiceAPI.REFUND,
    operationType: OperationType.Response,
    serviceApiResponseCode: response.status,
    response: response.data,
    orderId: paymentId,
  });

  return response.data;
};
