import { OrderStatus } from '../../enums'; // adjust import path
import * as utils from './utils'; // wherever formatAxiosResponse + getResponse live
import { processDPaaSErrorResponses } from './utils';

describe('processDPaaSErrorResponses', () => {
  const userId = 'user-123';
  const correlationId = 'corr-456';

  beforeEach(() => {
    jest.spyOn(utils, 'formatAxiosResponse').mockImplementation((res) => res);
    jest
      .spyOn(utils, 'getResponse')
      .mockImplementation(
        (message, correlationId, userId, status, dpaasResponse) => ({
          message,
          correlationId,
          userId,
          status,
          dpaasResponse,
        }),
      );
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('handles 409 conflict', () => {
    const response = { status: 409, data: [] };

    const result = processDPaaSErrorResponses(response, userId, correlationId);

    expect(result?.status).toBe(OrderStatus.CAPTURED);
    expect(result?.message).toContain('409 status');
  });

  it('handles gateway-invalid-data fatal init failure', () => {
    const response = {
      status: 400,
      data: [
        {
          errorCode: 'gateway-invalid-data',
          errorMessage: 'E5603 Context: Fatal initialisation failure',
        },
      ],
    };

    const result = processDPaaSErrorResponses(response, userId, correlationId);

    expect(result?.status).toBe(OrderStatus.PROCESSING);
    expect(result?.message).toContain('gateway-invalid-data');
  });

  it('handles 504 timeout', () => {
    const response = { status: 504, data: [] };

    const result = processDPaaSErrorResponses(response, userId, correlationId);

    expect(result?.status).toBe(OrderStatus.PROCESSING);
    expect(result?.message).toContain('504 status');
  });

  it('handles invalid-input-schema', () => {
    const response = {
      status: 400,
      data: [{ errorCode: 'invalid-input-schema' }],
    };

    const result = processDPaaSErrorResponses(response, userId, correlationId);

    expect(result?.status).toBe(OrderStatus.OUTSTANDING);
    expect(result?.message).toContain('invalid-input-schema');
  });

  it('handles wallet-invalid-data', () => {
    const response = {
      status: 400,
      data: [{ errorCode: 'wallet-invalid-data' }],
    };

    const result = processDPaaSErrorResponses(response, userId, correlationId);

    expect(result?.status).toBe(OrderStatus.OUTSTANDING);
    expect(result?.message).toContain('wallet-invalid-data');
  });

  it('returns null if no condition matched', () => {
    const response = { status: 418, data: [] };

    const result = processDPaaSErrorResponses(response, userId, correlationId);

    expect(result).toBeNull();
  });
});
