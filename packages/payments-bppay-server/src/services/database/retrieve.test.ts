import { DocumentClient } from 'aws-sdk/clients/dynamodb';

import { TransactionType } from '../../enums';
import { DateType, QueryGetTransactionsArgs } from '../../types/graphql';
import dynamoClient from '../../utils/dynamoClient';
import logger from '../../utils/logger';
import { contextMock } from '../../utils/mock';
import {
  retrieveTransactions,
  retrieveUserIdFromAuthIdInternal,
  retrieveUserOrder,
} from './retrieve';

const mockQueryPromise = jest.fn().mockResolvedValue({ Items: [] });
const mockScanPromise = jest.fn().mockResolvedValue({ Items: [] });

jest.mock('../../env', () => {
  return {
    PAYMENTS_DB_TABLE_NAME: 'paymentDetailsV2',
    HISTORY_DB_TABLE_NAME: 'chargeHistoryV3',
    DYNAMO_DB_REGION: 'us-east-1',
    GO_LIVE_DATE: '1679695200000',
    PAYMENT_DB_USER_TYPE_DEPLOYMENT_DATE: new Date('02/01/2023')
      .getTime()
      .toString(),
  };
});

beforeEach(() => {
  jest.clearAllMocks();

  jest.spyOn(dynamoClient, 'scan').mockReturnValue({
    promise: mockScanPromise,
  } as unknown as AWS.Request<DocumentClient.ScanOutput, AWS.AWSError>);

  jest.spyOn(dynamoClient, 'query').mockReturnValue({
    promise: mockQueryPromise,
  } as unknown as AWS.Request<DocumentClient.ScanOutput, AWS.AWSError>);
});

const mockRetrieveParams: QueryGetTransactionsArgs = {
  startDate: '03/01/2023',
  endDate: '03/23/2023',
};

const ctx = contextMock();

jest.spyOn(logger, 'error').mockImplementation(jest.fn());

describe('retrieve', () => {
  describe('retrieveUserOrder()', () => {
    it('should get the user data from the payments database', async () => {
      const rowData = { user_id: 'userId', payment_id: 'paymentId' };
      mockQueryPromise.mockResolvedValueOnce({ Items: [rowData] });

      const res = await retrieveUserOrder(ctx, { userId: 'userId' });
      expect(res).toStrictEqual(rowData);
    });

    it('should throw an error if unable to complete dynamo request', async () => {
      mockQueryPromise.mockRejectedValueOnce(new Error('dynamo error'));

      await expect(
        retrieveUserOrder(ctx, { userId: 'userId' }),
      ).rejects.toThrow('dynamo error');

      expect(logger.error).toHaveBeenCalledWith(
        'retrieveUserOrder error: dynamo error',
      );
    });
  });

  describe('retrieveUserIdFromAuthIdInternal()', () => {
    it('should return correct fields', async () => {
      const retrievedData = {
        user_id: 'mock12331',
        final_payment_status: 'Outstanding',
        void_transaction: true,
        payment_id: 'm3131242',
      };
      mockQueryPromise.mockResolvedValueOnce({ Items: [retrievedData] });

      const res = await retrieveUserIdFromAuthIdInternal({ authId: 'auth_id' });
      expect(res).toStrictEqual(retrievedData);
    });

    it('throw an error if unable to complete dynamo request ', async () => {
      const error = new Error('dynamo error');
      mockQueryPromise.mockRejectedValueOnce(error);

      await expect(
        retrieveUserIdFromAuthIdInternal({ authId: 'auth_id' }),
      ).rejects.toThrow(error);
      expect(logger.error).toHaveBeenCalledWith(
        'retrieveUserIdFromAuthIdInternal error: dynamo error',
      );
    });
  });

  describe('retrieveTransactions()', () => {
    it('should query for transactions in the payments table with filters when transactionType is REGISTERED', async () => {
      await retrieveTransactions({
        ...mockRetrieveParams,
        transactionType: TransactionType.REGISTERED,
      });

      expect(dynamoClient.query).toHaveBeenCalledWith({
        TableName: 'paymentDetailsV2',
        IndexName: 'user_type_index',
        KeyConditionExpression:
          'user_type=:user_type AND (order_started BETWEEN :start_date and :end_date)',
        FilterExpression: 'NOT begins_with(charge_session_id, :prefix)',
        ExpressionAttributeValues: {
          ':start_date': 1677628800000,
          ':end_date': 1679529600000,
          ':prefix': 'AR',
          ':user_type': 'registered',
        },
      });
    });

    it('should query and apply date filters on sales_posting_date column when transactionType is REGISTERED and dateType is SALES_POSTING', async () => {
      await retrieveTransactions({
        ...mockRetrieveParams,
        transactionType: TransactionType.REGISTERED,
        dateType: DateType.SALES_POSTING,
      });

      expect(dynamoClient.query).toHaveBeenCalledWith({
        TableName: 'paymentDetailsV2',
        IndexName: 'user_type_index',
        KeyConditionExpression: 'user_type=:user_type',
        FilterExpression:
          '(sales_posting_date BETWEEN :start_date and :end_date) and NOT begins_with(charge_session_id, :prefix)',
        ExpressionAttributeValues: {
          ':start_date': 1677628800000,
          ':end_date': 1679529600000,
          ':prefix': 'AR',
          ':user_type': 'registered',
        },
      });
    });

    it('should query for transactions in the payments table with filters when transactionType is RFID', async () => {
      await retrieveTransactions({
        ...mockRetrieveParams,
        transactionType: TransactionType.RFID,
      });

      expect(dynamoClient.query).toHaveBeenCalledWith({
        TableName: 'paymentDetailsV2',
        IndexName: 'user_type_index',
        KeyConditionExpression:
          'user_type=:user_type AND (order_started BETWEEN :start_date and :end_date)',
        FilterExpression: 'begins_with(charge_session_id, :prefix)',
        ExpressionAttributeValues: {
          ':start_date': 1677628800000,
          ':end_date': 1679529600000,
          ':prefix': 'AR',
          ':user_type': 'registered',
        },
      });
    });

    it('should query and apply date filters on sales_posting_date column when transactionType is RFID and dateType is SALES_POSTING', async () => {
      await retrieveTransactions({
        ...mockRetrieveParams,
        transactionType: TransactionType.RFID,
        dateType: DateType.SALES_POSTING,
      });

      expect(dynamoClient.query).toHaveBeenCalledWith({
        TableName: 'paymentDetailsV2',
        IndexName: 'user_type_index',
        KeyConditionExpression: 'user_type=:user_type',
        FilterExpression:
          '(sales_posting_date BETWEEN :start_date and :end_date) and begins_with(charge_session_id, :prefix)',
        ExpressionAttributeValues: {
          ':start_date': 1677628800000,
          ':end_date': 1679529600000,
          ':prefix': 'AR',
          ':user_type': 'registered',
        },
      });
    });

    it('should scan for transactions in the chargeHistory table with filters when transactionType is CONTACTLESS', async () => {
      await retrieveTransactions({
        ...mockRetrieveParams,
        transactionType: TransactionType.CONTACTLESS,
      });

      expect(dynamoClient.scan).toHaveBeenCalledWith({
        TableName: 'chargeHistoryV3',
        FilterExpression:
          '(date_start BETWEEN :start_date and :end_date) and (paymentStatus=:status)',
        ExpressionAttributeValues: {
          ':start_date': '2023-03-01T00:00:00.000Z',
          ':end_date': '2023-03-23T00:00:00.000Z',
          ':status': 'Contactless',
        },
      });
    });

    it('should scan for transactions in the payments table with filters when transactionType is GUEST', async () => {
      await retrieveTransactions({
        ...mockRetrieveParams,
        transactionType: TransactionType.GUEST,
      });

      expect(dynamoClient.scan).toHaveBeenCalledWith({
        TableName: 'paymentDetailsV2',
        FilterExpression:
          '(order_started BETWEEN :start_date and :end_date) and begins_with(charge_session_id, :prefix)',
        ExpressionAttributeValues: {
          ':start_date': 1677628800000,
          ':end_date': 1679529600000,
          ':prefix': 'AW',
        },
      });
    });

    it('should scan for transactions in the payments table with filters when transactionType is ALL', async () => {
      await retrieveTransactions({
        ...mockRetrieveParams,
        transactionType: TransactionType.ALL,
      });

      expect(dynamoClient.scan).toHaveBeenCalledWith({
        TableName: 'paymentDetailsV2',
        FilterExpression: '(order_started BETWEEN :start_date and :end_date)',
        ExpressionAttributeValues: {
          ':start_date': 1677628800000,
          ':end_date': 1679529600000,
        },
      });
    });
    it('should scan for transactions in the payments table with date filters applied on sales_posting_date when transactionType is ALL and dateType is SALES_POSTING˝', async () => {
      await retrieveTransactions({
        ...mockRetrieveParams,
        transactionType: TransactionType.ALL,
        dateType: DateType.SALES_POSTING,
      });

      expect(dynamoClient.scan).toHaveBeenCalledWith({
        TableName: 'paymentDetailsV2',
        FilterExpression:
          '(sales_posting_date BETWEEN :start_date and :end_date)',
        ExpressionAttributeValues: {
          ':start_date': 1677628800000,
          ':end_date': 1679529600000,
        },
      });
    });

    it('should assume transactionType is ALL when transactionType is not defined', async () => {
      await retrieveTransactions({
        ...mockRetrieveParams,
        transactionType: undefined,
      });

      expect(dynamoClient.scan).toHaveBeenCalledWith({
        TableName: 'paymentDetailsV2',
        FilterExpression: '(order_started BETWEEN :start_date and :end_date)',
        ExpressionAttributeValues: {
          ':start_date': 1677628800000,
          ':end_date': 1679529600000,
        },
      });
    });

    it('should not apply paymentStatus filter when dateType is SALES_POSTING_MISSING', async () => {
      await retrieveTransactions({
        ...mockRetrieveParams,
        dateType: DateType.SALES_POSTING_MISSING,
        paymentStatus: ['Refunded', 'Outstanding'],
      });

      expect(dynamoClient.scan).toHaveBeenCalledWith({
        TableName: 'paymentDetailsV2',
        FilterExpression: '(order_started BETWEEN :start_date and :end_date)',
        ExpressionAttributeValues: {
          ':start_date': 1677628800000,
          ':end_date': 1679529600000,
        },
      });
    });

    it('should apply paymentStatus filter on top of the base filters', async () => {
      await retrieveTransactions({
        ...mockRetrieveParams,
        transactionType: TransactionType.REGISTERED,
        paymentStatus: ['Refunded', 'Outstanding'],
      });

      expect(dynamoClient.query).toHaveBeenCalledWith({
        TableName: 'paymentDetailsV2',
        IndexName: 'user_type_index',
        KeyConditionExpression:
          'user_type=:user_type AND (order_started BETWEEN :start_date and :end_date)',
        FilterExpression:
          'NOT begins_with(charge_session_id, :prefix) and (final_payment_status = :paymentStatus0) or ((refunded_date BETWEEN :start_date and :end_date) and (final_payment_status = :ref))',
        ExpressionAttributeValues: {
          ':end_date': 1679529600000,
          ':paymentStatus0': 'Outstanding',
          ':prefix': 'AR',
          ':ref': 'Refunded',
          ':start_date': 1677628800000,
          ':user_type': 'registered',
        },
      });
    });

    it('should handle not defined start date when type is not CONTACTLESS', async () => {
      await retrieveTransactions({
        ...mockRetrieveParams,
        startDate: undefined,
      });

      expect(dynamoClient.scan).toHaveBeenCalledWith({
        TableName: 'paymentDetailsV2',
        FilterExpression: '(order_started BETWEEN :start_date and :end_date)',
        ExpressionAttributeValues: {
          ':start_date': 0,
          ':end_date': 1679529600000,
        },
      });
    });

    it('should handle not defined start date when type is CONTACTLESS', async () => {
      await retrieveTransactions({
        ...mockRetrieveParams,
        startDate: undefined,
        transactionType: TransactionType.CONTACTLESS,
      });

      expect(dynamoClient.scan).toHaveBeenCalledWith({
        TableName: 'chargeHistoryV3',
        FilterExpression:
          '(date_start BETWEEN :start_date and :end_date) and (paymentStatus=:status)',
        ExpressionAttributeValues: {
          ':end_date': '2023-03-23T00:00:00.000Z',
          ':start_date': '1970-01-01T00:00:00.000Z',
          ':status': 'Contactless',
        },
      });
    });

    it('should perform a legacy scan if type is Registered and startDate is before deployment date', async () => {
      await retrieveTransactions({
        ...mockRetrieveParams,
        transactionType: TransactionType.REGISTERED,
        startDate: '01/01/2023', // Before payment DB user deployment date
      });

      expect(dynamoClient.scan).toHaveBeenCalledWith({
        TableName: 'paymentDetailsV2',
        FilterExpression:
          '(order_started BETWEEN :start_date and :end_date) and begins_with(charge_session_id, :prefix)',
        ExpressionAttributeValues: {
          ':start_date': 1672531200000,
          ':end_date': 1679529600000,
          ':prefix': 'AM',
        },
      });
    });

    it('should perform a legacy scan if type is RFID and startDate is before deployment date', async () => {
      await retrieveTransactions({
        ...mockRetrieveParams,
        transactionType: TransactionType.RFID,
        startDate: '01/01/2023', // Before payment DB user deployment date
      });

      expect(dynamoClient.scan).toHaveBeenCalledWith({
        TableName: 'paymentDetailsV2',
        FilterExpression:
          '(order_started BETWEEN :start_date and :end_date) and begins_with(charge_session_id, :prefix)',
        ExpressionAttributeValues: {
          ':start_date': 1672531200000,
          ':end_date': 1679529600000,
          ':prefix': 'AR',
        },
      });
    });

    it('should throw an error if transaction type is valid', async () => {
      await expect(
        retrieveTransactions({
          ...mockRetrieveParams,
          transactionType: 'unknown-transaction-type',
        }),
      ).rejects.toThrow('Unknown transaction type');
    });
  });
});
