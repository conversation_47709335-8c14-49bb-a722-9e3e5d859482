import { DocumentClient } from 'aws-sdk/clients/dynamodb';

import { QueryGetTransactionsArgs } from '../../types/graphql';
import { editFilterParamsToFilterByPaymentStatus } from './filterTransactions';

const args: QueryGetTransactionsArgs = {
  startDate: '03/01/2023',
  endDate: '03/23/2023',
};

const expression = '(order_started BETWEEN :start_date and :end_date)';
const tableName = 'payments';
const parameters: DocumentClient.ScanInput = {
  TableName: tableName,
  FilterExpression: expression,
  ExpressionAttributeValues: {
    ':start_date': '03/01/2023',
    ':end_date': '03/23/2023',
  },
};

const resetFilterParams = () => {
  parameters.FilterExpression = expression;
  parameters.ExpressionAttributeValues = {
    ':start_date': '03/01/2023',
    ':end_date': '03/23/2023',
  };
};

beforeEach(() => resetFilterParams());

describe('edit filter params to filter payments by:', () => {
  describe('paymentStatus', () => {
    it('should update filter expression for non refunded status payments', () => {
      editFilterParamsToFilterByPaymentStatus(
        { ...args, paymentStatus: ['Outstanding', 'CaptureFailed'] },
        parameters,
      );

      expect(parameters.FilterExpression).toEqual(
        expression +
          ' and (final_payment_status = :paymentStatus0 or final_payment_status = :paymentStatus1)',
      );

      expect(parameters.ExpressionAttributeValues).toHaveProperty(
        ':paymentStatus0',
        'Outstanding',
      );

      expect(parameters.ExpressionAttributeValues).toHaveProperty(
        ':paymentStatus1',
        'CaptureFailed',
      );
    });

    it('should update filter expression for refunded status payments', () => {
      editFilterParamsToFilterByPaymentStatus(
        { ...args, paymentStatus: ['Refunded'] },
        parameters,
      );
      expect(parameters.FilterExpression).toEqual(
        expression +
          ' and ((refunded_date BETWEEN :start_date and :end_date) and (final_payment_status = :ref))',
      );
      expect(parameters.ExpressionAttributeValues).toHaveProperty(
        ':ref',
        'Refunded',
      );
    });

    it('should update filter expression for mixed (refund + non-refund) status payments', () => {
      editFilterParamsToFilterByPaymentStatus(
        { ...args, paymentStatus: ['Outstanding', 'Refunded'] },
        parameters,
      );
      expect(parameters.FilterExpression).toEqual(
        expression +
          ' and (final_payment_status = :paymentStatus0) or ((refunded_date BETWEEN :start_date and :end_date) and (final_payment_status = :ref))',
      );
      expect(parameters.ExpressionAttributeValues).toHaveProperty(
        ':paymentStatus0',
        'Outstanding',
      );
      expect(parameters.ExpressionAttributeValues).toHaveProperty(
        ':ref',
        'Refunded',
      );
    });

    it('should update filter expression for mixed (refund + multiple non-refund) status payments', () => {
      editFilterParamsToFilterByPaymentStatus(
        {
          ...args,
          paymentStatus: ['Outstanding', 'CaptureFailed', 'Refunded'],
        },
        parameters,
      );

      expect(parameters.FilterExpression).toEqual(
        expression +
          ' and (final_payment_status = :paymentStatus0 or final_payment_status = :paymentStatus1) or ((refunded_date BETWEEN :start_date and :end_date) and (final_payment_status = :ref))',
      );
      expect(parameters.ExpressionAttributeValues).toHaveProperty(
        ':paymentStatus0',
        'Outstanding',
      );
      expect(parameters.ExpressionAttributeValues).toHaveProperty(
        ':paymentStatus1',
        'CaptureFailed',
      );
      expect(parameters.ExpressionAttributeValues).toHaveProperty(
        ':ref',
        'Refunded',
      );
    });
  });
});
