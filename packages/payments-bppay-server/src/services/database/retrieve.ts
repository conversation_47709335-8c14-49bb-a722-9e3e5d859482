import { DocumentClient } from 'aws-sdk/clients/dynamodb';

import { TransactionType } from '../../enums';
import env from '../../env';
import { BpPayServerContext } from '../../types/apollo';
import { QueryGetTransactionsArgs } from '../../types/graphql';
import dynamoClient from '../../utils/dynamoClient';
import logger from '../../utils/logger';
import { buildRetrieveParams } from './buildRetrieveParams';
import { editFilterParamsToFilterByPaymentStatus } from './filterTransactions';
import {
  HistoryTransactionI,
  RetrieveHistoryTransactionI,
  RetrieveOrderI,
  RetrieveTransactionOrderI,
  RetrieveUserIdParamsI,
  RetrieveUserOrderI,
  TransactionI,
} from './interfaces';

const paymentsTableName = env.PAYMENTS_DB_TABLE_NAME as string;
const historyTableName = env.HISTORY_DB_TABLE_NAME as string;

export const retrieveUserOrder = async (
  ctx: BpPayServerContext,
  { userId }: RetrieveUserOrderI,
) => {
  return dynamoClient
    .query({
      TableName: paymentsTableName,
      IndexName: 'user_index',
      KeyConditionExpression: 'user_id = :u',
      Limit: 1,
      ScanIndexForward: false,
      ExpressionAttributeValues: {
        ':u': userId,
      },
    })
    .promise()
    .then((res) => {
      return res.Items && (res.Items[0] as TransactionI);
    })
    .catch((e) => {
      ctx.logger.error(`retrieveUserOrder error: ${(e as Error).message}`);
      throw e;
    });
};
export const retrieveUserIdFromAuthIdInternal = async ({
  authId,
}: RetrieveUserIdParamsI) => {
  return dynamoClient
    .query({
      TableName: paymentsTableName,
      IndexName: 'auth_id_index',
      KeyConditionExpression: 'auth_id = :a',
      Limit: 1,
      ScanIndexForward: false,
      ExpressionAttributeValues: {
        ':a': authId,
      },
    })
    .promise()
    .then((res) => {
      return res.Items && (res.Items[0] as TransactionI);
    })
    .catch((e) => {
      logger.error(
        `retrieveUserIdFromAuthIdInternal error: ${(e as Error).message}`,
      );

      throw e;
    });
};
export const retrieveUserTransactions = async ({
  userId,
  startDateTimestamp,
  endDateTimestamp,
  logTraceId,
}: RetrieveUserOrderI) => {
  let keyConditionExpression = 'user_id = :u';
  const expressionAttributeValues: Record<string, string | number> = {
    ':u': userId,
  };
  if (startDateTimestamp && endDateTimestamp) {
    keyConditionExpression += ' AND order_started BETWEEN :sd AND :ed';
    expressionAttributeValues[':sd'] = startDateTimestamp;
    expressionAttributeValues[':ed'] = endDateTimestamp;
  }
  return dynamoClient
    .query({
      TableName: paymentsTableName,
      IndexName: 'user_index',
      KeyConditionExpression: keyConditionExpression,
      ScanIndexForward: false,
      ExpressionAttributeValues: expressionAttributeValues,
    })
    .promise()
    .then((res) => {
      return res.Items as TransactionI[];
    })
    .catch((e) => {
      logger.error(`retrieveUserTransactions error: ${(e as Error).message}`, {
        logTraceId,
      });

      throw e;
    });
};

export const retrieveHistoryTransaction = async ({
  charge_session_id,
}: RetrieveHistoryTransactionI) => {
  return dynamoClient
    .query({
      TableName: historyTableName,
      KeyConditionExpression: 'charge_session_id = :t',
      Limit: 1,
      ScanIndexForward: false,
      ExpressionAttributeValues: {
        ':t': charge_session_id,
      },
    })
    .promise()
    .then((res: any) => {
      return res.Items && res.Items[0];
    })
    .catch((e) => {
      logger.error(`retrieveHistoryTransaction error: ${(e as Error).message}`);
      throw e;
    });
};

export const retrieveTransactionOrder = async ({
  transactionId,
}: RetrieveTransactionOrderI) => {
  return dynamoClient
    .query({
      TableName: paymentsTableName,
      IndexName: 'transaction_index',
      KeyConditionExpression: 'transaction_id = :t',
      Limit: 1,
      ScanIndexForward: false,
      ExpressionAttributeValues: {
        ':t': transactionId,
      },
    })
    .promise()
    .then((res: any) => {
      return res.Items && res.Items[0];
    })
    .catch((e) => {
      logger.error(`retrieveTransactionOrder error: ${(e as Error).message}`);

      throw e;
    });
};

export const getTransactionsFromHistory = async (
  historyTransactions: Array<HistoryTransactionI>,
) => {
  const resolvedTransactions: Array<TransactionI> = [];
  for (const transaction of historyTransactions) {
    try {
      const resolvedTransaction = await retrieveTransactionOrder(transaction);
      if (resolvedTransaction) {
        resolvedTransactions.push(resolvedTransaction);
      }
    } catch (error) {
      logger.error(
        `Failed to retrieve transaction: ${transaction.transactionId}`,
      );
    }
  }
  return { transactions: resolvedTransactions };
};

export const retrieveTransactions = async (
  args: QueryGetTransactionsArgs,
): Promise<Array<TransactionI | HistoryTransactionI>> => {
  if (args.transactionType === TransactionType.CONTACTLESS) {
    // If the transaction type is contactless, we need to search the history table for those transactions first
    return retrieveContactlessTransactions(args);
  }

  const parameters = buildRetrieveParams(args);

  editFilterParamsToFilterByPaymentStatus(args, parameters);

  if (parameters.IndexName) {
    return queryAll<TransactionI>(parameters);
  }

  return scanAll<TransactionI>(parameters);
};

const retrieveContactlessTransactions = async (
  args: QueryGetTransactionsArgs,
) => {
  const parameters = buildRetrieveParams(args);
  const historyTransactions = await scanAll<HistoryTransactionI>(parameters);

  // Search the payments table for those transactions found in history
  const contactlessPayments = await getTransactionsFromHistory(
    historyTransactions,
  );

  logger.info(
    `getTransactions: Contactless transactions in payments table: ${JSON.stringify(
      contactlessPayments,
    )}`,
  );

  return contactlessPayments.transactions;
};

async function queryAll<T>(parameters: DocumentClient.QueryInput) {
  let hasLastKey = true;
  let items: Array<T> = [];

  while (hasLastKey) {
    const result = await dynamoClient.query(parameters).promise();
    items = items.concat(result.Items as Array<T>);

    hasLastKey = !!result.LastEvaluatedKey;
    if (hasLastKey) {
      parameters.ExclusiveStartKey = result.LastEvaluatedKey;
    }
  }

  return items;
}

async function scanAll<T>(
  parameters: DocumentClient.ScanInput,
): Promise<Array<T>> {
  let hasLastKey = true;
  let items: Array<T> = [];

  while (hasLastKey) {
    const result = await dynamoClient.scan(parameters).promise();
    items = items.concat(result.Items as Array<T>);

    hasLastKey = !!result.LastEvaluatedKey;
    if (hasLastKey) {
      parameters.ExclusiveStartKey = result.LastEvaluatedKey;
    }
  }

  return items;
}

export const retrieveOrder = async ({
  paymentId,
  logTraceId,
}: RetrieveOrderI) => {
  return dynamoClient
    .get({
      TableName: paymentsTableName,
      Key: { payment_id: paymentId },
    })
    .promise()
    .then((res: any) => {
      return res.Item;
    })
    .catch((e) => {
      logger.error(`retrieveOrder error: ${(e as Error).message}`, {
        logTraceId,
      });

      throw e;
    });
};
