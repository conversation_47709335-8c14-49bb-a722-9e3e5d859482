import { DocumentClient } from 'aws-sdk/clients/dynamodb';

import { DateType, TransactionType } from '../../enums';
import env from '../../env';
import { QueryGetTransactionsArgs } from '../../types/graphql';

const paymentsTable = env.PAYMENTS_DB_TABLE_NAME as string;
const historyTable = env.HISTORY_DB_TABLE_NAME as string;
const paymentDbUserDeploymentDate = parseInt(
  env.PAYMENT_DB_USER_TYPE_DEPLOYMENT_DATE ?? '0',
);

export const buildRetrieveParams = (
  args: QueryGetTransactionsArgs,
): DocumentClient.ScanInput | DocumentClient.QueryInput => {
  const transactionType =
    (args.transactionType as TransactionType) ?? TransactionType.ALL;

  if (transactionType === TransactionType.CONTACTLESS) {
    return {
      TableName: historyTable,
      FilterExpression: `(date_start BETWEEN :start_date and :end_date) and (paymentStatus=:status)`,
      ExpressionAttributeValues: {
        // History table has dates saved as ISO
        ':start_date': new Date(
          args.startDate ?? '1970-01-01T00:00:00.000Z',
        ).toISOString(),
        ':end_date': new Date(args.endDate).toISOString(),
        ':status': 'Contactless',
      },
    };
  }

  // Dates are saved as timestamps in the payments table
  const startDate = args.startDate ? new Date(args.startDate).getTime() : 0;
  const endDate = new Date(args.endDate).getTime();

  const dateType = args.dateType || env.DATE_TYPE || DateType.ORDER_STARTED;

  const isLegacyScan = startDate < paymentDbUserDeploymentDate;

  const dateColumn =
    dateType === DateType.SALES_POSTING
      ? 'sales_posting_date'
      : 'order_started';

  if (transactionType === TransactionType.REGISTERED) {
    return getRegisteredParams(startDate, endDate, dateColumn, isLegacyScan);
  }

  if (transactionType === TransactionType.RFID) {
    return getRfidParams(startDate, endDate, dateColumn, isLegacyScan);
  }

  if (transactionType === TransactionType.GUEST) {
    return {
      TableName: paymentsTable,
      FilterExpression: `(${dateColumn} BETWEEN :start_date and :end_date) and begins_with(charge_session_id, :prefix)`,
      ExpressionAttributeValues: {
        ':start_date': startDate,
        ':end_date': endDate,
        ':prefix': 'AW',
      },
    };
  }

  if (transactionType === TransactionType.ALL) {
    return {
      TableName: paymentsTable,
      FilterExpression: `(${dateColumn} BETWEEN :start_date and :end_date)`,
      ExpressionAttributeValues: {
        ':start_date': startDate,
        ':end_date': endDate,
      },
    };
  }

  throw new Error('Unknown transaction type');
};

const getRegisteredParams = (
  startDate: number,
  endDate: number,
  dateColumn: 'sales_posting_date' | 'order_started',
  isLegacyScan: boolean,
) => {
  if (isLegacyScan) {
    // If legacy, perform a scan operation
    return {
      TableName: paymentsTable,
      FilterExpression: `(${dateColumn} BETWEEN :start_date and :end_date) and begins_with(charge_session_id, :prefix)`,
      ExpressionAttributeValues: {
        ':start_date': startDate,
        ':end_date': endDate,
        ':prefix': 'AM',
      },
    };
  }

  if (dateColumn === 'sales_posting_date') {
    return {
      TableName: paymentsTable,
      IndexName: 'user_type_index',
      KeyConditionExpression: 'user_type=:user_type',
      FilterExpression:
        '(sales_posting_date BETWEEN :start_date and :end_date) and NOT begins_with(charge_session_id, :prefix)',
      ExpressionAttributeValues: {
        ':start_date': startDate,
        ':end_date': endDate,
        ':prefix': 'AR',
        ':user_type': 'registered',
      },
    };
  }

  return {
    TableName: paymentsTable,
    IndexName: 'user_type_index',
    KeyConditionExpression:
      'user_type=:user_type AND (order_started BETWEEN :start_date and :end_date)',
    FilterExpression: 'NOT begins_with(charge_session_id, :prefix)',
    ExpressionAttributeValues: {
      ':start_date': startDate,
      ':end_date': endDate,
      ':prefix': 'AR',
      ':user_type': 'registered',
    },
  };
};

const getRfidParams = (
  startDate: number,
  endDate: number,
  dateColumn: 'sales_posting_date' | 'order_started',
  isLegacyScan: boolean,
) => {
  if (isLegacyScan) {
    // If legacy, perform a scan operation
    return {
      TableName: paymentsTable,
      FilterExpression: `(${dateColumn} BETWEEN :start_date and :end_date) and begins_with(charge_session_id, :prefix)`,
      ExpressionAttributeValues: {
        ':start_date': startDate,
        ':end_date': endDate,
        ':prefix': 'AR',
      },
    };
  }

  if (dateColumn === 'sales_posting_date') {
    return {
      TableName: paymentsTable,
      IndexName: 'user_type_index',
      KeyConditionExpression: 'user_type=:user_type',
      FilterExpression:
        '(sales_posting_date BETWEEN :start_date and :end_date) and begins_with(charge_session_id, :prefix)',
      ExpressionAttributeValues: {
        ':start_date': startDate,
        ':end_date': endDate,
        ':prefix': 'AR',
        ':user_type': 'registered',
      },
    };
  }

  return {
    TableName: paymentsTable,
    IndexName: 'user_type_index',
    KeyConditionExpression:
      'user_type=:user_type AND (order_started BETWEEN :start_date and :end_date)',
    FilterExpression: 'begins_with(charge_session_id, :prefix)',
    ExpressionAttributeValues: {
      ':start_date': startDate,
      ':end_date': endDate,
      ':prefix': 'AR',
      ':user_type': 'registered',
    },
  };
};
