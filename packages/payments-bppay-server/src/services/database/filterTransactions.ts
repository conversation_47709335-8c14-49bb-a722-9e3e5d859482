import { DocumentClient } from 'aws-sdk/clients/dynamodb';

import { DateType, QueryGetTransactionsArgs } from '../../types/graphql';
import { FilterByStatusI } from './interfaces';

const editFilterParamsToFilterByNonRefundedStatus = ({
  nonRefundedStatuses,
  parameters,
}: FilterByStatusI) => {
  const attributesMap: Record<string, string> = {};

  const statusExpression = nonRefundedStatuses
    .map((status, idx) => {
      const key = `:paymentStatus${idx}`;
      attributesMap[key] = status;
      return `final_payment_status = ${key}`;
    })
    .join(' or ');

  parameters.FilterExpression = `${parameters.FilterExpression} and (${statusExpression})`;
  parameters.ExpressionAttributeValues = {
    ...parameters.ExpressionAttributeValues,
    ...attributesMap,
  };
};

const editFilterParamsToFilterByRefundedStatus = ({
  nonRefundedStatuses,
  parameters,
}: FilterByStatusI) => {
  if (!parameters.ExpressionAttributeValues) {
    parameters.ExpressionAttributeValues = {};
  }
  const refundedExpression =
    '((refunded_date BETWEEN :start_date and :end_date) and (final_payment_status = :ref))';
  const operation = nonRefundedStatuses.length ? 'or' : 'and';

  parameters.FilterExpression = `${parameters.FilterExpression} ${operation} ${refundedExpression}`;
  parameters.ExpressionAttributeValues[`:ref`] = 'Refunded';
};

export const editFilterParamsToFilterByPaymentStatus = (
  args: QueryGetTransactionsArgs,
  parameters: DocumentClient.ScanInput | DocumentClient.QueryInput,
) => {
  if (args.dateType === DateType.SALES_POSTING_MISSING) {
    return;
  }
  const paymentStatus = (args.paymentStatus ?? []).filter(
    (el): el is string => typeof el === 'string' && !!el.trim().length,
  );

  if (!paymentStatus.length) {
    return;
  }

  const nonRefundedStatuses = paymentStatus.filter(
    (status) => status !== 'Refunded',
  );

  const isRefunded = paymentStatus.length > nonRefundedStatuses.length;

  if (nonRefundedStatuses.length > 0) {
    editFilterParamsToFilterByNonRefundedStatus({
      nonRefundedStatuses,
      parameters,
    });
  }

  if (isRefunded) {
    editFilterParamsToFilterByRefundedStatus({
      nonRefundedStatuses,
      parameters,
    });
  }
};
