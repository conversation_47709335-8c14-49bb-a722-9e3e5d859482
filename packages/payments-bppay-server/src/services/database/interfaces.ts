import { DocumentClient } from 'aws-sdk/clients/dynamodb';

import { OrderStatus, PaymentStatus, UserTypeDynamo } from '../../enums';
import { ChargeStatus } from '../../types';

type ChargeProvider = 'hasToBe' | 'BPCM' | 'dcs';

interface BlockingFee {
  duration: number | null;
  price: number | null;
}

interface ProviderChargeId {
  type: string;
  value: string;
}

interface ChargeMetaData {
  value: number | null;
  units: string | null;
}

interface ChargeOverStayDynamo {
  fine_unit_cost: number | null;
  fine_gross: number | null;
  fine_net: number | null;
  fine_tax: number | null;
  fine_tax_percentage: number | null;
  duration: string | number | null;
}
export interface CreateJourneyI {
  paymentId: string;
  userId: string;
  journeyId: string;
  roundedAmount: number;
  preAuthAmount: number;
  preAuthCurrency: string;
  startDate: number;
  paymentMethodType: string;
  sessionId: string;
  chargeSessionId?: string;
  registered?: boolean;
}

export interface StoreAuthOrderI {
  paymentId: string;
  userId: string;
  amount?: number | null;
  currency: string;
  orderStatus: OrderStatus;
  appCountry: string;
  cardNumber?: string;
  cardScheme?: string;
  fundingMethod?: string;
}

export interface StorePaymentStatusI {
  paymentId: string;
  amount: number;
  currency: string;
  status: string;
  chargeSessionId?: string;
  retrievalReferenceNumber?: string;
}

export interface StoreCardDetailsI {
  cardNumber: string;
  fundingMethod: string;
  cardScheme: string;
  paymentId: string;
}

export interface StorePaymentStatusAndCardDetailsI {
  cardNumber?: string;
  fundingMethod?: string;
  cardScheme?: string;
  paymentId: string;
  amount: number;
  currency: string;
  status: string;
  chargeSessionId?: string;
  retrievalReferenceNumber?: string;
}

export interface StorePreAuthI {
  status: boolean;
  paymentId: string;
}

export interface RetrieveHistoryTransactionI {
  charge_session_id: string;
}
export interface RetrieveUserOrderI {
  userId: string;
  startDateTimestamp?: number;
  endDateTimestamp?: number;
  logTraceId?: string;
}

export interface RetrieveTransactionOrderI {
  transactionId: string;
}

export interface RetrieveTransactionsResponseI {
  transactions: Array<any>;
  LastEvaluatedKey: string | null;
}

export interface RetrieveUserIdParamsI {
  authId: string;
}
export interface RetrieveTransactionsParams {
  TableName: string;
  ExpressionAttributeValues: any;
  FilterExpression?: string;
  ExclusiveStartKey?: any;
  IndexName?: string;
  KeyConditionExpression?: string;
}

export interface StoreVoidTransactionI {
  paymentId: string;
  voidStatus: boolean;
}

export interface StoreChargeStatusI {
  paymentId: string;
  chargeStatus: string;
  connectorId: string;
  chargeSessionId?: string;
}

export interface CreateInternalPaymentI {
  chargeSessionId: string;
  amount: number;
  currency: string;
  userId: string;
  paymentId: string;
  startDate: number;
  orderStatus: OrderStatus;
  finalPaymentStatus: PaymentStatus;
  merchantId: string;
  registered?: boolean;
}

export interface CreateOrderI {
  paymentId: string;
  userId: string;
  merchantId: string;
  startDate: number;
  orderStatus: OrderStatus;
  correlationId: string;
  chargeSessionId?: string;
  registered?: boolean;
}

export interface UpdateRefundI {
  paymentId: string;
  referenceChargeSessionId: string;
  paymentStatus: string;
  refundedDate?: number;
}

export interface RetrieveOrderI {
  paymentId: string;
  logTraceId?: string;
}

export interface TransactionI {
  payment_id: string;
  order_started: number;
  user_id: string;
  user_type?: UserTypeDynamo; // missing for legacy transactions
  correlation_id?: string;
  country?: string;
  sales_posting_date?: number;

  // Only included in Guest transactions
  journey_id?: string;
  session_id?: string;

  // Only included in PreAuth transactions
  charge_status?: ChargeStatus; // previously also legacy value in pre-v7 transactions
  connector_internal_id?: string;
  preauth?: boolean | null;
  pre_authorization?: {
    amount: number;
    rounded_amount: number;
    currency?: string;
  };

  // Only included in HTB charges
  transaction_id?: string;
  transaction_number?: string;

  // Only included in DCS charges
  charge_session_start?: string;

  // Legacy values in pre-v7 transactions
  order_id?: string;

  // The following will be included after making payment (preauth before a charge or MIT after a charge)
  charge_session_id?: string;
  payment_method_type?: string;
  card_details?: {
    card_number: string;
    card_scheme: string;
    funding_method: string;
  };
  order_status?: string;

  // The following will be included if the charge has been completed and the payment taken
  final_payment_status?: string;
  final_payment?: {
    amount: number;
    currency: string;
  };
  merchant_id?: string;

  // Other fields
  refunded_date?: number; // if refunded
  void_transaction?: boolean; // if not processed and additionally voided
}

export interface HistoryTransactionI {
  transactionId: string;
  transactionNumber: string;
  paymentId: string | undefined;
  cdr_filename: string | undefined;
  cdr_type: string | undefined;
  reference_charge_session_id: string | undefined;
  receiptId?: string | undefined | null;
  sessionId: string;
  paymentStatus: string;
  paybackId: string;
  sessionType: string;
  chargepoint: {
    cpo: string | null;
    provider: ChargeProvider;
    address: string;
    display_address: string | null;
    public_name: string | null;
    country: string | null;
    city: string | null;
    postcode: string | null;
    apollo_internal_id: string;
    apollo_external_id: string;
    provider_external_id: string;
    currency: string | undefined;
    unit: string | undefined;
    grossUnitCost: number | undefined;
    blockingFee: BlockingFee;
    connector: {
      connector_internal_id: string;
      connector_external_id: string;
      type: string;
    };
  };
  date_start: string;
  date_end: string;
  charge_details: {
    gross_amount_local_currency: number | null;
    meter_start: string | null;
    meter_end: string | null;
    auth_type: string | null;
    rate_name: string | null;
    status: string | null;
    auth_id: string | null;
    total_gross: number | null;
    provider_charge_ids: Array<ProviderChargeId>;
    charge_tax_percentage: number | null;
    charge_tax: number | null;
    charge_gross: number | null;
    min_cost: number | null;
    paybackId: string | null;
    credit_card_uuid: string | null;
    credit_card_pan: string | null;
    credit_card_type: string | null;
    tag_id: string | null;
    charge_net: number | null;
    unit_cost: number | null;
    unit_of_measure: string;
    currency: string;
    energy_consumed: ChargeMetaData;
    state_of_charge: ChargeMetaData;
    power: ChargeMetaData;
    overstay: ChargeOverStayDynamo | null;
    co2_saving: number | null;
    chargeBaseFee: number | null;
    chargeAdditionalFees: number | null;
    charge_base_fee_gross: number | null;
    charge_additional_fees_gross: number | null;
    roundedChargeBaseFee: number | null;
    roundedChargeAdditionalFees: number | null;
    roundedChargeNet: number | null;
    roundedChargeTax: number | null;
    roundedTotalGross: number | null;
    isStandardInvoice: boolean;
  };
  charge_session_id: string;
  transaction_search_id: string;
  user_id: string;
  event_time: string;
}
export interface UpdateOrderParamsI {
  TableName: string;
  Key: any;
  ExpressionAttributeValues: any;
  UpdateExpression: string;
  ReturnValues?: string;
}

export interface FilterByStatusI {
  nonRefundedStatuses: Array<string>;
  parameters: DocumentClient.QueryInput | DocumentClient.ScanInput;
}

export interface UpdateTransactionInternalIdI {
  paymentId: string;
  authId?: string | null;
  connectorId?: string | null;
  transactionId?: string | null;
  transactionNumber?: string | null;
  chargeStatus?: string | null;
  chargeSessionStart?: string | null;
  chargeSessionId?: string | null;
}

export interface ExpressionAttrStoreTransactionId {
  ':a'?: string;
  ':t'?: string;
  ':n'?: string;
  ':i'?: string;
  ':s'?: string;
  ':r'?: string;
  ':b'?: string;
}

export interface ExpressionAttrStorePaymentStatusI {
  ':s'?: string;
  ':c'?: string;
  ':p'?: FinalPaymentI;
  ':r'?: string;
}

interface FinalPaymentI {
  amount: number;
  currency: string;
}

export interface UpdateTransactionIdExpressionI {
  authId?: string | null;
  connectorId?: string | null;
  transactionId?: string | null;
  transactionNumber?: string | null;
  chargeStatus?: string | null;
  expressionAttributeValues: ExpressionAttrStoreTransactionId;
  chargeSessionStart?: string | null;
  chargeSessionId?: string | null;
}

export interface TransactionInternalIdResponseI {
  status: number;
  message: string;
  paymentId?: string;
}

export interface PutTransactionInternalIdI
  extends UpdateTransactionInternalIdI {
  userId: string;
  registered?: boolean;
}

export interface StoreOperationIdDynamoInput {
  paymentId: string;
  operationId: string;
}

export interface StoreOperationIdDynamoResponse {
  status: number;
  message: string;
}
