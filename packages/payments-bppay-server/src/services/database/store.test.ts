import { Currency, OrderStatus } from '../../enums';
import { ChargeStatus } from '../../types';
import logger from '../../utils/logger';
import { contextMock } from '../../utils/mock';
import {
  putTransactionInternalID,
  storeAuthOrder,
  storeChargeStatus,
  storeInternalPaymentRecord,
  storeOperationIdDynamo,
  storePaymentStatus,
  updateTransactionInternalID,
} from './store';

jest.spyOn(logger, 'error').mockImplementation(jest.fn());
const ctx = contextMock();

const mockUpdate = jest.fn();
const mockPut = jest.fn();
const errorMessage = 'Failed to update record';

jest.mock('../../utils/dynamoClient', () => {
  return {
    update: () => ({
      promise: mockUpdate,
    }),
    put: () => ({
      promise: mockPut,
    }),
  };
});

beforeEach(() => {
  jest.clearAllMocks();
});

describe('Database Store', () => {
  describe('updateTransactionInternalID', () => {
    const successResponse = {
      Attributes: {
        transaction_id: '456',
        user_id: '789',
      },
    };

    const successMessage = {
      message: 'Successfully updated record',
      status: 200,
    };

    it('should return early if no expression params were provided only paymentId', async () => {
      await expect(
        updateTransactionInternalID(ctx, { paymentId: '123' }),
      ).rejects.toThrowError(
        'Error: no authId or transactionId or transactionNumber or connectorId or chargeStatus provided',
      );
    });

    describe('Successfull update', () => {
      it('should update record if only transaction id is provided', async () => {
        mockUpdate.mockResolvedValue(successResponse);

        const dbResponse = await updateTransactionInternalID(ctx, {
          paymentId: '123',
          transactionId: '456',
        });

        expect(logger.error).not.toHaveBeenCalled();
        expect(dbResponse).toEqual(successMessage);
      });

      it('should update record if only transaction number is provided', async () => {
        mockUpdate.mockResolvedValue(successResponse);

        const dbResponse = await updateTransactionInternalID(ctx, {
          paymentId: '123',
          transactionNumber: '456',
        });

        expect(logger.error).not.toHaveBeenCalled();
        expect(dbResponse).toEqual(successMessage);
      });

      it('should update record if only auth id is provided', async () => {
        mockUpdate.mockResolvedValue(successResponse);

        const dbResponse = await updateTransactionInternalID(ctx, {
          paymentId: '123',
          authId: '456',
        });

        expect(logger.error).not.toHaveBeenCalled();
        expect(dbResponse).toEqual(successMessage);
      });

      it('should update record for chargeStatus', async () => {
        mockUpdate.mockResolvedValue({
          Attributes: {
            charge_status: 'success',
          },
        });

        const dbResponse = await updateTransactionInternalID(ctx, {
          paymentId: '123',
          chargeStatus: '456',
        });

        expect(logger.error).not.toHaveBeenCalled();
        expect(dbResponse).toEqual(successMessage);
      });

      it('should update record for connectorId', async () => {
        mockUpdate.mockResolvedValue({
          Attributes: {
            connector_internal_id: 'success',
          },
        });

        const dbResponse = await updateTransactionInternalID(ctx, {
          paymentId: '123',
          connectorId: '456',
        });

        expect(logger.error).not.toHaveBeenCalled();
        expect(dbResponse).toEqual(successMessage);
      });
    });

    describe('Failed update', () => {
      it('should return if failed to update record', async () => {
        mockUpdate.mockRejectedValue(new Error(errorMessage));

        const dbResponse = await updateTransactionInternalID(ctx, {
          paymentId: '123',
          transactionId: '456',
          transactionNumber: '789',
          authId: '101112',
        });

        expect(logger.error).toHaveBeenCalledWith(
          'updateTransactionInternalID error: Failed to update record',
        );
        expect(dbResponse).toEqual({
          message: errorMessage,
          status: 500,
        });
      });

      it('should return 400 if record with given payment_id does not exists', async () => {
        mockUpdate.mockRejectedValue({
          code: 'ConditionalCheckFailedException',
          message: 'The conditional request failed',
        });

        const dbResponse = await updateTransactionInternalID(ctx, {
          paymentId: '123',
          transactionId: '456',
          transactionNumber: '789',
          authId: '101112',
        });

        expect(logger.error).toHaveBeenCalledWith(
          'updateTransactionInternalID error: The conditional request failed',
        );
        expect(dbResponse).toEqual({
          message:
            "Failed to update: record with given payment_id doesn't exist",
          status: 400,
        });
      });
    });
  });

  describe('putTransactionInternalID', () => {
    it('should return status 200 if new record was created', async () => {
      mockPut.mockResolvedValue({});

      const data = putTransactionInternalID(ctx, {
        userId: '123',
        paymentId: '456',
      });

      expect(data).resolves.toEqual({
        message: 'Successfully created new record',
        status: 200,
      });
    });

    it('should return status 500 if record creation failed', async () => {
      mockPut.mockRejectedValue({
        message: 'Failed to create record',
      });

      const data = putTransactionInternalID(ctx, {
        userId: '123',
        paymentId: '456',
      });

      expect(data).resolves.toEqual({
        message: 'Failed to create record',
        status: 500,
      });
    });

    it('should return status 500 if there is a return value', async () => {
      mockPut.mockReturnValue({
        message: 'Updated',
      });

      const data = putTransactionInternalID(ctx, {
        userId: '123',
        paymentId: '456',
      });

      expect(data).resolves.toEqual({
        message: 'Failed to create new record in database',
        status: 500,
      });
    });
  });

  describe('storePaymentStatus', () => {
    it('should return 201 if the payment data has been stored', async () => {
      mockUpdate.mockResolvedValue({
        Attributes: {
          operationId: '123',
        },
      });

      const dbResponse = await storePaymentStatus(ctx, {
        paymentId: '456',
        amount: 123,
        currency: 'EUR',
        status: '200',
        chargeSessionId: 'AMDE-1234-1234-1234',
        retrievalReferenceNumber: 'utrn1336321',
      });

      expect(dbResponse).toEqual({ status: 201 });
    });

    it('should return 400 if error is present', async () => {
      mockUpdate.mockRejectedValue(new Error(errorMessage));

      const dbResponse = await storePaymentStatus(ctx, {
        paymentId: '123',
        amount: 42,
        currency: 'EUR',
        status: '400',
        chargeSessionId: 'AMDE-1234-1234-1134',
        retrievalReferenceNumber: 'utrn1333321',
      });

      expect(logger.error).toHaveBeenCalledWith(
        'storePaymentStatus error: Failed to update record',
      );
      expect(dbResponse).toEqual({ status: 400 });
    });
  });

  describe('storeInternalPaymentRecord', () => {
    it('should return 201 if the recored was created', async () => {
      mockPut.mockResolvedValue({});
      const dbResponse = await storeInternalPaymentRecord(ctx, {} as any);

      expect(dbResponse).toEqual({
        status: 201,
        message: 'Successfully created new record',
      });
    });

    it('should return status 500 if record creation failed', async () => {
      mockPut.mockRejectedValue(new Error('Failed to create record'));

      const dbResponse = await storeInternalPaymentRecord(ctx, {} as any);

      expect(logger.error).toHaveBeenCalledWith(
        'storeInternalPaymentRecord error: Failed to create record',
      );
      expect(dbResponse).toEqual({
        status: 500,
        message: 'Failed to create record',
      });
    });
  });

  describe('storeOperationIdDynamo', () => {
    it('should return error if paymentId or operationId is missing', async () => {
      const storeResponse = await storeOperationIdDynamo(ctx, {
        paymentId: '456',
        operationId: '',
      });

      expect(storeResponse).toEqual({
        status: 500,
        message: 'No paymentId or operationId provided',
      });
    });

    it('should return error if DynamoDB update failed', async () => {
      mockUpdate.mockRejectedValue(new Error(errorMessage));

      const storeResponse = await storeOperationIdDynamo(ctx, {
        paymentId: '456',
        operationId: '123',
      });

      expect(storeResponse).toEqual({
        status: 500,
        message: errorMessage,
      });
    });

    it(`should return error if DynamoDB doesn't return stored operationId in response`, async () => {
      mockUpdate.mockResolvedValueOnce({
        Attributes: {
          operationId: '',
        },
      });

      const storeResponse = await storeOperationIdDynamo(ctx, {
        paymentId: '456',
        operationId: '123',
      });

      expect(storeResponse).toEqual({
        status: 500,
        message: 'Failed to store operationId in database',
      });
    });

    it(`should return specific error message if the payment_id record is not found in DB`, async () => {
      mockUpdate.mockRejectedValueOnce({
        code: 'ConditionalCheckFailedException',
      });

      const storeResponse = await storeOperationIdDynamo(ctx, {
        paymentId: '456',
        operationId: '123',
      });

      expect(storeResponse).toEqual({
        status: 500,
        message: `Failed to store operationId: record with given payment_id doesn't exist`,
      });
    });

    it(`should return success response if operationId is stored in DB`, async () => {
      mockUpdate.mockResolvedValueOnce({
        Attributes: {
          operationId: '123',
        },
      });

      const paymentId = '456';
      const storeResponse = await storeOperationIdDynamo(ctx, {
        paymentId,
        operationId: '123',
      });

      expect(storeResponse).toEqual({
        status: 200,
        message: `Successfully stored operationId for paymentId ${paymentId}`,
      });
    });
  });

  describe('storeAuthOrder', () => {
    it('should return 201 when update is successful', async () => {
      mockUpdate.mockResolvedValueOnce({
        Attributes: { user_data: 'some data' },
      });

      const result = await storeAuthOrder(ctx, {
        paymentId: 'payment-id',
        currency: Currency.GBP,
        orderStatus: OrderStatus.AUTHORIZED,
        userId: 'user-id',
        amount: 20,
        appCountry: 'UK',
      });

      expect(result).toEqual({ status: 201 });
    });

    it('should return 400 when update response is not successful', async () => {
      mockUpdate.mockResolvedValueOnce('invalid response'); // simulate missing Attributes or empty response

      const result = await storeAuthOrder(ctx, {
        paymentId: 'payment-id',
        currency: Currency.GBP,
        orderStatus: OrderStatus.AUTHORIZED,
        userId: 'user-id',
        amount: 20,
        appCountry: 'UK',
      });

      expect(result).toEqual({ status: 400 });
      expect(mockUpdate).toHaveBeenCalledTimes(1);
    });

    it('should log and throw error when update throws', async () => {
      const error = new Error('DynamoDB failure');
      mockUpdate.mockRejectedValueOnce(error);

      await expect(
        storeAuthOrder(ctx, {
          paymentId: 'payment-id',
          currency: Currency.GBP,
          orderStatus: OrderStatus.AUTHORIZED,
          userId: 'user-id',
          amount: 20,
          appCountry: 'UK',
        }),
      ).rejects.toThrow('DynamoDB failure');

      expect(mockUpdate).toHaveBeenCalledTimes(1);
      expect(logger.error).toHaveBeenCalledWith(
        'storeAuthOrder error: DynamoDB failure',
      );
    });
  });
  describe('storeChargeStatus', () => {
    it('should return 201 when function is called', async () => {
      mockUpdate.mockResolvedValueOnce({});

      const result = await storeChargeStatus(ctx, {
        paymentId: 'payment-id',
        chargeStatus: ChargeStatus.STARTED,
        connectorId: 'connector-123',
      });

      expect(result).toEqual({ status: 201 });
      expect(mockUpdate).toHaveBeenCalled();
    });

    it('should return 201 when function is called with chargeSessionId', async () => {
      mockUpdate.mockResolvedValueOnce({});

      const result = await storeChargeStatus(ctx, {
        paymentId: 'payment-id',
        chargeStatus: ChargeStatus.STARTED,
        connectorId: 'connector-1234',
        chargeSessionId: 'abc123',
      });

      expect(result).toEqual({ status: 201 });
      expect(mockUpdate).toHaveBeenCalled();
    });

    it('should return 400 when update returns non-empty response', async () => {
      mockUpdate.mockResolvedValueOnce({ Attributes: { some: 'data' } });

      const result = await storeChargeStatus(ctx, {
        paymentId: 'paymentid',
        chargeStatus: ChargeStatus.STARTED,
        connectorId: 'connector-123',
      });

      expect(result).toEqual({ status: 400 });
      expect(mockUpdate).toHaveBeenCalled();
    });

    it('should log error when DynamoDB throws an error', async () => {
      mockUpdate.mockRejectedValueOnce(new Error('Dynamo error'));

      const result = await storeChargeStatus(ctx, {
        paymentId: 'paymentid',
        chargeStatus: ChargeStatus.STARTED,
        connectorId: 'connector-12',
      });

      expect(result).toEqual({ status: 400 });
      expect(logger.error).toHaveBeenCalledWith(
        'storeChargeStatus error: Dynamo error',
      );
    });
  });
});
