import { OrderStatus, UserTypeDynamo } from '../../enums';
import env from '../../env';
import { BpPayServerContext } from '../../types/apollo';
import dynamoClient from '../../utils/dynamoClient';
import {
  CreateInternalPaymentI,
  CreateJourneyI,
  CreateOrderI,
  ExpressionAttrStorePaymentStatusI,
  ExpressionAttrStoreTransactionId,
  PutTransactionInternalIdI,
  StoreAuthOrderI,
  StoreCardDetailsI,
  StoreChargeStatusI,
  StoreOperationIdDynamoInput,
  StoreOperationIdDynamoResponse,
  StorePaymentStatusAndCardDetailsI,
  StorePaymentStatusI,
  StorePreAuthI,
  StoreVoidTransactionI,
  TransactionInternalIdResponseI,
  UpdateOrderParamsI,
  UpdateRefundI,
  UpdateTransactionInternalIdI,
} from './interfaces';
import { retrieveOrder } from './retrieve';
import {
  getTransactionInternalIdItems,
  getTransactionInternalUpdateIdExpression,
} from './utils';
const paymentsTableName = env.PAYMENTS_DB_TABLE_NAME;

const isSuccessful = (data: any) => JSON.stringify(data) === JSON.stringify({});

const RESPONSE_STORAGE_FAILURE = {
  status: 'FAILURE',
  message: 'storage failure',
};

const CHARGE_SESSION_ID_EXPRESSION = ', charge_session_id = :c';
const FAILED_TO_CREATE_NEW_RECORD = 'Failed to create new record in database';

export const storeJourney = async (
  ctx: BpPayServerContext,
  {
    paymentId,
    chargeSessionId,
    userId,
    preAuthAmount,
    roundedAmount,
    preAuthCurrency,
    journeyId,
    startDate,
    paymentMethodType,
    sessionId,
    registered = false,
  }: CreateJourneyI,
) => {
  try {
    const data = await dynamoClient
      .put({
        TableName: paymentsTableName as string,
        Item: {
          order_started: startDate,
          payment_id: paymentId,
          user_id: userId,
          journey_id: journeyId,
          mem_group: 'a',
          pre_authorization: {
            amount: preAuthAmount,
            rounded_amount: roundedAmount,
            currency: preAuthCurrency,
          },
          payment_method_type: paymentMethodType,
          session_id: sessionId,
          charge_session_id: chargeSessionId,
          user_type: registered
            ? UserTypeDynamo.REGISTERED_USER
            : UserTypeDynamo.GUEST_USER,
        },
      })
      .promise();

    if (isSuccessful(data)) {
      return { status: 201 };
    }
    return { status: 400 };
  } catch (e) {
    ctx.logger.error(`storeJourney error: ${(e as Error).message}`);
    throw e;
  }
};

export const storeAuthOrder = async (
  ctx: BpPayServerContext,
  {
    paymentId,
    userId,
    amount,
    currency,
    orderStatus,
    appCountry,
    cardNumber,
    cardScheme,
    fundingMethod,
  }: StoreAuthOrderI,
) => {
  try {
    let updateExpression =
      'set user_id = :u, pre_authorization = :a, preauth = :p, order_status = :s, country = :c';
    const expressionAttributeValues: Record<string, any> = {
      ':u': userId,
      ':a': { amount, currency },
      ':p': true,
      ':s': orderStatus,
      ':c': appCountry,
    };
    if (cardNumber) {
      updateExpression += ', card_details = :cd';
      expressionAttributeValues[':cd'] = {
        card_number: cardNumber,
        card_scheme: cardScheme,
        funding_method: fundingMethod,
      };
    }
    const data = await dynamoClient
      .update({
        TableName: paymentsTableName as string,
        Key: {
          payment_id: paymentId,
        },
        UpdateExpression: updateExpression,
        ExpressionAttributeValues: expressionAttributeValues,
        ReturnValues: 'UPDATED_NEW',
      })
      .promise();

    if (data && data.Attributes && Object.keys(data.Attributes).length > 0) {
      return { status: 201 };
    }
    return { status: 400 };
  } catch (e) {
    ctx.logger.error(`storeAuthOrder error: ${(e as Error).message}`);
    throw e;
  }
};

export const storePaymentStatus = async (
  ctx: BpPayServerContext,
  {
    paymentId,
    amount,
    currency,
    status,
    chargeSessionId,
    retrievalReferenceNumber,
  }: StorePaymentStatusI,
) => {
  const expressionAttributeValues: ExpressionAttrStorePaymentStatusI = {
    ':s': status,
    ':c': chargeSessionId,
    ':p': { amount, currency },
    ':r': retrievalReferenceNumber,
  };

  const updateExpression = `set final_payment = :p${
    retrievalReferenceNumber ? ', retrieval_reference_number = :r' : ''
  }, final_payment_status = :s${
    chargeSessionId ? CHARGE_SESSION_ID_EXPRESSION : ''
  }`;

  const data = await dynamoClient
    .update({
      TableName: paymentsTableName as string,
      Key: {
        payment_id: paymentId,
      },
      UpdateExpression: updateExpression,
      ExpressionAttributeValues: expressionAttributeValues,
      ReturnValues: 'UPDATED_NEW',
    })
    .promise()
    .catch((e: Error) => {
      ctx.logger.error(`storePaymentStatus error: ${e.message}`);
    });

  if (data?.Attributes) {
    return { status: 201 };
  }
  return { status: 400 };
};

export const storeCardDetails = async (
  ctx: BpPayServerContext,
  { cardNumber, fundingMethod, cardScheme, paymentId }: StoreCardDetailsI,
): Promise<{ status: number | 'FAILURE' }> => {
  const data = await dynamoClient
    .update({
      TableName: paymentsTableName as string,
      Key: {
        payment_id: paymentId,
      },
      UpdateExpression: 'set card_details = :c',
      ExpressionAttributeValues: {
        ':c': {
          card_number: cardNumber,
          funding_method: fundingMethod,
          card_scheme: cardScheme,
        },
      },
      ReturnValues: 'UPDATED_NEW',
    })
    .promise()
    .catch((e: Error) => {
      ctx.logger.error(`storeCardDetails error: ${e.message}`);
      return RESPONSE_STORAGE_FAILURE;
    });

  if (isSuccessful(data)) {
    return { status: 201 };
  }
  return { status: 400 };
};

export const storePaymentStatusAndCardDetails = async (
  ctx: BpPayServerContext,
  {
    cardNumber,
    fundingMethod,
    cardScheme,
    paymentId,
    amount,
    currency,
    status,
    chargeSessionId,
    retrievalReferenceNumber,
  }: StorePaymentStatusAndCardDetailsI,
): Promise<{
  status: number | 'FAILURE';
}> => {
  const expressionAttributeValues: any = {
    ':s': status,
    ':p': { amount, currency },
  };

  let updateExpression = 'SET final_payment = :p, final_payment_status = :s';

  if (retrievalReferenceNumber) {
    updateExpression += ', retrieval_reference_number = :r';
    expressionAttributeValues[':r'] = retrievalReferenceNumber;
  }

  if (chargeSessionId) {
    updateExpression += CHARGE_SESSION_ID_EXPRESSION;
    expressionAttributeValues[':c'] = chargeSessionId;
  }

  if (cardNumber) {
    updateExpression += ', card_details = :cd';
    expressionAttributeValues[':cd'] = {
      card_number: cardNumber,
      funding_method: fundingMethod,
      card_scheme: cardScheme,
    };
  }

  const data = await dynamoClient
    .update({
      TableName: paymentsTableName as string,
      Key: {
        payment_id: paymentId,
      },
      UpdateExpression: updateExpression,
      ExpressionAttributeValues: expressionAttributeValues,
      ReturnValues: 'UPDATED_NEW',
    })
    .promise()
    .catch((e: Error) => {
      ctx.logger.error(`storePaymentMethodAndCardDetails error: ${e.message}`);
      throw e;
    });

  if (data.Attributes) {
    return { status: 201 };
  }
  return { status: 400 };
};

export const storeTransactionID = async (
  ctx: BpPayServerContext,
  transactionId: string,
  transactionNumber: string,
  paymentId: string,
  registered = false,
) => {
  const userType = registered
    ? UserTypeDynamo.REGISTERED_USER
    : UserTypeDynamo.GUEST_USER;

  const data = await dynamoClient
    .update({
      TableName: paymentsTableName as string,
      Key: {
        payment_id: paymentId,
      },
      UpdateExpression:
        'set transaction_id = :t, transaction_number = :n, user_type = :u',
      ExpressionAttributeValues: {
        ':t': transactionId,
        ':n': transactionNumber,
        ':u': userType,
      },
      ReturnValues: 'ALL_NEW',
    })
    .promise()
    .then((resultData: any) => {
      if (
        !resultData.Attributes.transaction_id &&
        !resultData.Attributes.user_id
      ) {
        throw new Error('Failed to update dynamoDB with transactionId');
      }

      return true;
    })
    .catch((e: Error) => {
      ctx.logger.error(`storeTransactionID error: ${e.message}`);
      return RESPONSE_STORAGE_FAILURE;
    });
  if (isSuccessful(data)) {
    return { status: 201 };
  }
  return { status: 400 };
};

export const storePreAuthStatus = async (
  ctx: BpPayServerContext,
  { status, paymentId }: StorePreAuthI,
) => {
  const data = await dynamoClient
    .update({
      TableName: paymentsTableName as string,
      Key: {
        payment_id: paymentId,
      },
      UpdateExpression: 'set preauth = :s',
      ExpressionAttributeValues: {
        ':s': status,
      },
      ReturnValues: 'UPDATED_NEW',
    })
    .promise()
    .catch((e: Error) => {
      ctx.logger.error(`storePreAuthStatus error: ${e.message}`);
      return RESPONSE_STORAGE_FAILURE;
    });

  if (isSuccessful(data)) {
    return { status: 201 };
  }
  return { status: 400 };
};

export const storeVoidTransaction = async (
  ctx: BpPayServerContext,
  transactions: Array<StoreVoidTransactionI>,
) => {
  console.log('Storing successfully voided transactions in Dynamo.');
  const data = await dynamoClient
    .transactWrite({
      TransactItems: transactions.map(({ paymentId, voidStatus }) => ({
        Update: {
          TableName: paymentsTableName as string,
          Key: {
            payment_id: paymentId,
          },
          UpdateExpression: 'set void_transaction = :v',
          ExpressionAttributeValues: {
            ':v': voidStatus,
          },
          ReturnValues: 'UPDATED_NEW',
        },
      })),
    })
    .promise()
    .catch((e: Error) => {
      ctx.logger.error(`storeVoidTransaction error: ${e.message}`);
      return RESPONSE_STORAGE_FAILURE;
    });

  if (isSuccessful(data)) {
    return { status: 201 };
  }
  return { status: 400 };
};

export const storeChargeStatus = async (
  ctx: BpPayServerContext,
  { chargeStatus, paymentId, connectorId, chargeSessionId }: StoreChargeStatusI,
) => {
  console.log('Storing successfully started charge statuses in dynamo.');

  const expressionAttributeValues: Record<string, string> = {
    ':s': chargeStatus,
    ':i': connectorId,
  };
  const updateExpressions = [
    'charge_status = :s',
    'connector_internal_id = :i',
  ];

  if (chargeSessionId) {
    updateExpressions.push('charge_session_id = :c');
    expressionAttributeValues[':c'] = chargeSessionId;
  }

  const updateExpression = `set ${updateExpressions.join(', ')}`;

  const data = await dynamoClient
    .update({
      TableName: paymentsTableName as string,
      Key: {
        payment_id: paymentId,
      },
      UpdateExpression: updateExpression,
      ExpressionAttributeValues: expressionAttributeValues,
      ReturnValues: 'UPDATED_NEW',
    })
    .promise()
    .catch((e: Error) => {
      ctx.logger.error(`storeChargeStatus error: ${e.message}`);
      return RESPONSE_STORAGE_FAILURE;
    });

  if (isSuccessful(data)) {
    return { status: 201 };
  }
  return { status: 400 };
};

export const storeOrder = async (
  ctx: BpPayServerContext,
  {
    paymentId,
    userId,
    startDate,
    orderStatus,
    merchantId,
    correlationId,
    chargeSessionId,
    registered = false,
  }: CreateOrderI,
) => {
  try {
    const data = await dynamoClient
      .put({
        TableName: paymentsTableName as string,
        Item: {
          order_started: startDate,
          payment_id: paymentId,
          order_status: orderStatus,
          user_id: userId,
          merchant_id: merchantId,
          correlation_id: correlationId,
          charge_session_id: chargeSessionId,
          user_type: registered
            ? UserTypeDynamo.REGISTERED_USER
            : UserTypeDynamo.GUEST_USER,
        },
      })
      .promise();

    if (isSuccessful(data)) {
      return { status: 201 };
    }
    return { status: 400 };
  } catch (e) {
    ctx.logger.error(`storeOrder error: ${(e as Error).message}`);
    throw e;
  }
};

export const updateOrStoreOrder = async (
  ctx: BpPayServerContext,
  {
    paymentId,
    userId,
    startDate,
    orderStatus,
    merchantId,
    correlationId,
    chargeSessionId,
    registered = false,
  }: CreateOrderI,
) => {
  const paymentData = await retrieveOrder({ paymentId });
  if (paymentData) {
    ctx.logger.info(
      `Existing transaction found for paymentId: ${paymentId}. Updating...`,
    );
    // if a transaction is found then no need to create a new entry in the payments table
    // instead update the existing order with new fields
    return updateOrder(
      ctx,
      paymentId,
      orderStatus,
      merchantId,
      correlationId,
      startDate,
      chargeSessionId,
    );
  } else {
    ctx.logger.info(
      `No transaction found for paymentId: ${paymentId}. Creating...`,
    );
    return storeOrder(ctx, {
      paymentId,
      userId,
      startDate,
      orderStatus,
      merchantId,
      correlationId,
      chargeSessionId,
      registered,
    });
  }
};

export const updateOrder = async (
  ctx: BpPayServerContext,
  paymentId: string,
  orderStatus: OrderStatus,
  merchantId: string,
  correlationId: string,
  startDate: number,
  chargeSessionId?: string,
) => {
  const data = await dynamoClient
    .update({
      TableName: paymentsTableName as string,
      Key: {
        payment_id: paymentId,
      },
      UpdateExpression:
        'set charge_session_id=:chargeSession, correlation_id=:correlation, merchant_id=:merchant, order_status=:status, order_started=:startdate',
      ExpressionAttributeValues: {
        ':chargeSession': chargeSessionId,
        ':correlation': correlationId,
        ':merchant': merchantId,
        ':status': orderStatus,
        ':startdate': startDate,
      },
      ReturnValues: 'ALL_NEW',
    })
    .promise()
    .then((resultData: any) => {
      if (
        !resultData.Attributes.correlation_id &&
        !resultData.Attributes.charge_session_id
      ) {
        throw new Error('Failed to update dynamoDB with correlation ID');
      }

      return { status: 201 };
    })
    .catch((e: Error) => {
      ctx.logger.error(`updateOrder error: ${e.message}`);
      return RESPONSE_STORAGE_FAILURE;
    });
  if (isSuccessful(data)) {
    return { status: 201 };
  }
  return { status: 400 };
};

export const updateRefundedOrder = async (
  ctx: BpPayServerContext,
  {
    paymentId,
    referenceChargeSessionId,
    paymentStatus,
    refundedDate,
  }: UpdateRefundI,
) => {
  const parameters: UpdateOrderParamsI = {
    TableName: paymentsTableName as string,
    Key: {
      payment_id: paymentId,
    },
    UpdateExpression: `set reference_charge_session_id=:referenceId, final_payment_status=:paymentStatus`,
    ExpressionAttributeValues: {
      ':referenceId': referenceChargeSessionId,
      ':paymentStatus': paymentStatus,
    },
    ReturnValues: 'ALL_NEW',
  };

  if (refundedDate) {
    parameters.UpdateExpression += `${
      refundedDate ? ', refunded_date=:refundedDate' : ''
    }`;
    parameters.ExpressionAttributeValues[':refundedDate'] = refundedDate;
  }

  const data = await dynamoClient
    .update(parameters)
    .promise()
    .then((resultData: any) => {
      if (!resultData.Attributes.reference_charge_session_id) {
        throw new Error(
          'Failed to update dynamoDB with reference charge session id',
        );
      }
      return { status: 201 };
    })
    .catch((e: Error) => {
      ctx.logger.error(`updateRefundedOrder error: ${e.message}`);
      return RESPONSE_STORAGE_FAILURE;
    });
  if (isSuccessful(data)) {
    return { status: 201 };
  }
  return { status: 400 };
};

export const updateTransactionInternalID = async (
  ctx: BpPayServerContext,
  {
    paymentId,
    authId,
    connectorId,
    transactionId,
    transactionNumber,
    chargeStatus,
    chargeSessionStart,
    chargeSessionId,
  }: UpdateTransactionInternalIdI,
): Promise<TransactionInternalIdResponseI> => {
  if (
    !authId &&
    !transactionId &&
    !transactionNumber &&
    !connectorId &&
    !chargeStatus
  ) {
    throw new Error(
      'Error: no authId or transactionId or transactionNumber or connectorId or chargeStatus provided',
    );
  }

  // Dynamically create the expressionAttributeValues object
  const expressionAttributeValues: ExpressionAttrStoreTransactionId = {};
  const updateExpression = getTransactionInternalUpdateIdExpression({
    authId,
    connectorId,
    chargeStatus,
    transactionId,
    transactionNumber,
    expressionAttributeValues,
    chargeSessionStart,
    chargeSessionId,
  });

  return await dynamoClient
    .update({
      TableName: paymentsTableName as string,
      Key: {
        payment_id: paymentId,
      },
      UpdateExpression: updateExpression,
      ExpressionAttributeValues: expressionAttributeValues,
      ConditionExpression: `attribute_exists(payment_id)`,
      ReturnValues: 'UPDATED_NEW',
    })
    .promise()
    .then((res: any) => {
      if (
        !res.Attributes?.transaction_id &&
        !res.Attributes?.transaction_number &&
        !res.Attributes?.connector_internal_id &&
        !res.Attributes?.charge_status &&
        !res.Attributes?.auth_id
      ) {
        throw new Error('Failed to update record in database');
      }

      return {
        status: 200,
        message: 'Successfully updated record',
      };
    })
    .catch((error) => {
      ctx.logger.error(`updateTransactionInternalID error: ${error?.message}`);

      if (error?.code === 'ConditionalCheckFailedException') {
        return {
          status: 400,
          message: `Failed to update: record with given payment_id doesn't exist`,
        };
      }

      return {
        status: 500,
        message: error?.message,
      };
    });
};

export const putTransactionInternalID = async (
  ctx: BpPayServerContext,
  {
    userId,
    paymentId,
    authId,
    connectorId,
    chargeStatus,
    transactionId,
    transactionNumber,
    registered = false,
  }: PutTransactionInternalIdI,
): Promise<TransactionInternalIdResponseI> => {
  try {
    const transactionItem = getTransactionInternalIdItems({
      userId,
      paymentId,
      authId,
      connectorId,
      chargeStatus,
      transactionId,
      transactionNumber,
    });

    const data = await dynamoClient
      .put({
        TableName: paymentsTableName as string,
        Item: {
          ...transactionItem,
          user_type: registered
            ? UserTypeDynamo.REGISTERED_USER
            : UserTypeDynamo.GUEST_USER,
        },
        ConditionExpression: 'attribute_not_exists(payment_id)',
      })
      .promise();

    if (isSuccessful(data)) {
      return {
        status: 200,
        message: 'Successfully created new record',
      };
    }

    return {
      status: 500,
      message: FAILED_TO_CREATE_NEW_RECORD,
    };
  } catch (error) {
    const errorMessage =
      (error as Error)?.message || FAILED_TO_CREATE_NEW_RECORD;
    ctx.logger.error(`putTransactionInternalID error: ${errorMessage}`);
    return {
      status: 500,
      message: errorMessage,
    };
  }
};

export const storeInternalPaymentRecord = async (
  ctx: BpPayServerContext,
  {
    chargeSessionId,
    amount,
    currency,
    userId,
    paymentId,
    startDate,
    orderStatus,
    finalPaymentStatus,
    merchantId,
    registered = false,
  }: CreateInternalPaymentI,
) => {
  try {
    const data = await dynamoClient
      .put({
        TableName: paymentsTableName as string,
        Item: {
          charge_session_id: chargeSessionId,
          final_payment: {
            amount,
            currency,
          },
          final_payment_status: finalPaymentStatus,
          order_started: startDate,
          payment_id: paymentId,
          user_id: userId,
          order_status: orderStatus,
          merchant_id: merchantId,
          user_type: registered
            ? UserTypeDynamo.REGISTERED_USER
            : UserTypeDynamo.GUEST_USER,
        },
      })
      .promise();

    if (!isSuccessful(data)) {
      throw new Error(FAILED_TO_CREATE_NEW_RECORD);
    }

    return { status: 201, message: 'Successfully created new record' };
  } catch (error) {
    const errorMessage =
      (error as Error)?.message || FAILED_TO_CREATE_NEW_RECORD;
    ctx.logger.error(`storeInternalPaymentRecord error: ${errorMessage}`);
    return {
      status: 500,
      message: errorMessage,
    };
  }
};

export const storeOperationIdDynamo = async (
  ctx: BpPayServerContext,
  { paymentId, operationId }: StoreOperationIdDynamoInput,
): Promise<StoreOperationIdDynamoResponse> => {
  try {
    if (!paymentId || !operationId) {
      throw new Error('No paymentId or operationId provided');
    }

    const res = await dynamoClient
      .update({
        TableName: paymentsTableName as string,
        Key: {
          payment_id: paymentId,
        },
        UpdateExpression: 'set operationId = :o',
        ExpressionAttributeValues: { ':o': operationId },
        ConditionExpression: `attribute_exists(payment_id)`,
        ReturnValues: 'UPDATED_NEW',
      })
      .promise();

    if (!res.Attributes?.operationId) {
      throw new Error('Failed to store operationId in database');
    }

    return {
      status: 200,
      message: `Successfully stored operationId for paymentId ${paymentId}`,
    };
  } catch (error: any) {
    ctx.logger.error(`storeOperationIdDynamo error: ${error?.message}`);

    if (error?.code === 'ConditionalCheckFailedException') {
      return {
        status: 500,
        message: `Failed to store operationId: record with given payment_id doesn't exist`,
      };
    }

    return {
      status: 500,
      message: error?.message,
    };
  }
};
