import { formatDates, formatUTCDate, isoDateToUnix } from './date';

const dateString =
  'Tue Mar 07 2023 11:53:06 GMT+0200 (Eastern European Standard Time)';
const IsoDateString = '2022-02-01T02:00:00Z';

describe('isoDateToUnix', () => {
  it('should return Unix date', () => {
    expect(isoDateToUnix(dateString)).toEqual(1678182786000);
  });
});

describe('formatDates', () => {
  it('should return Unix dates when unix is passed as true', () => {
    expect(formatDates([dateString], true)).toEqual([1678182786000]);
  });

  it('should return ISO string dates when unix is passed as false', () => {
    expect(formatDates([dateString], false)).toEqual([
      '2023-03-07T09:53:06.000Z',
    ]);
  });
});

describe('Function: formatUTCDate', () => {
  it('should format UTC date as a string', async () => {
    const formattedDateString = '220201';
    const formattedIsoDateUTC = formatUTCDate(IsoDateString);
    expect(formattedIsoDateUTC).toEqual(formattedDateString.toString());
  });
});
