import { IFormattedPaymentDetails } from '../common/interfaces';
import { TransactionI } from '../services/database/interfaces';
import { ChargeStatus } from '../types';

const formatPaymentDetails = (transaction: TransactionI) => {
  const {
    preauth: preAuthStatus,
    pre_authorization: preAuth,
    transaction_id: transactionId,
    transaction_number: transactionNumber,
    final_payment_status: finalPaymentStatus,
    final_payment: finalPayment,
    payment_method_type: paymentMethodType,
    card_details: cardDetails,
    charge_status: chargeStatus,
    void_transaction: voidTransaction,
    charge_session_id: chargeSessionId,
    payment_id: paymentId,
    order_id: orderId,
    order_started: orderStarted,
    refunded_date: refundedDate,
    user_id: userId,
  } = transaction;

  const preAuthAmount = preAuth?.amount;
  const preAuthRoundedAmount = preAuth?.rounded_amount;
  const paymentData: IFormattedPaymentDetails = {
    orderId,
    paymentId,
    chargeSessionId,
    userId,
    preAuthAmount,
    preAuthRoundedAmount,
    transactionId,
    transactionNumber,
    finalPaymentStatus,
    finalPayment,
    paymentMethodType,
    orderStarted,
    refundedDate,
    voidTransaction: !!voidTransaction,
    chargeStarted: chargeStatus === ChargeStatus.STARTED,
    preAuthStatus: preAuthStatus ?? null,
  };
  if (cardDetails) {
    const {
      funding_method: fundingMethod,
      card_number: cardNumber,
      card_scheme: cardScheme,
    } = cardDetails;
    paymentData.cardDetails = { fundingMethod, cardNumber, cardScheme };
  }
  return paymentData;
};

export default formatPaymentDetails;
