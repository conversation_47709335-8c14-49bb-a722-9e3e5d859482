import { attempt } from './attempt';

describe('attempt()', () => {
  it('should return error on failure', async () => {
    const fn = jest.fn().mockRejectedValue(new Error('error'));

    const [success, error] = await attempt(fn());
    expect(success).toBeNull();
    expect(error?.message).toEqual('error');
  });

  it('should return result of argument function', async () => {
    const fn = jest.fn().mockResolvedValue('result');

    const [success, error] = await attempt(fn());
    expect(success).toEqual('result');
    expect(error).toBeNull();
  });
});
