export const isoDateToUnix = (iso: string) => new Date(iso).getTime();

export const formatDates = (dates: Array<string>, unix = true) =>
  dates.map((dateString) => {
    const date = new Date(dateString);
    return unix ? date.getTime() : date.toISOString();
  });

export const formatUTCDate = (dateString: string | number) => {
  const date = new Date(dateString);

  const year = date.getUTCFullYear().toString().slice(-2);
  const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
  const day = date.getUTCDate().toString().padStart(2, '0');

  return `${year}${month}${day}`;
};

export const isIsoDateWithinRange = (
  isoQuery: string,
  isoTarget: string,
  msTargetRange: number,
) => {
  const targetDate = new Date(isoTarget);
  const queryDate = new Date(isoQuery);

  const startDate = new Date(targetDate.getTime() - msTargetRange);
  const endDate = new Date(targetDate.getTime() + msTargetRange);
  return startDate <= queryDate && queryDate <= endDate;
};
