[{"name": "NODE_ENV", "source": "ado-variable-group", "sourceName": "NODE_ENV"}, {"name": "PREAUTH_AMOUNT_DE", "source": "ado-variable-group", "sourceName": "PREAUTH_AMOUNT_DE"}, {"name": "PREAUTH_AMOUNT_UK", "source": "ado-variable-group", "sourceName": "PREAUTH_AMOUNT_UK"}, {"name": "PREAUTH_AMOUNT_US", "source": "ado-variable-group", "sourceName": "PREAUTH_AMOUNT_US"}, {"name": "PREAUTH_AMOUNT_NL", "source": "ado-variable-group", "sourceName": "PREAUTH_AMOUNT_NL"}, {"name": "GO_LIVE_DATE", "source": "ado-variable-group", "sourceName": "GO_LIVE_DATE"}, {"name": "PAYMENT_DB_USER_TYPE_DEPLOYMENT_DATE", "source": "ado-variable-group", "sourceName": "PAYMENT_DB_USER_TYPE_DEPLOYMENT_DATE"}, {"name": "BP_PAY_ENDPOINT", "source": "ado-variable-group", "sourceName": "BP_PAY_ENDPOINT"}, {"name": "DPAAS_PAY_ENDPOINT", "source": "ado-variable-group", "sourceName": "DPAAS_PAY_ENDPOINT"}, {"name": "PAYMENTS_DB_TABLE_NAME", "source": "ado-variable-group", "sourceName": "PAYMENTS_DB_TABLE_NAME"}, {"name": "PROCESSING_PAYMENT_FEATURE_FLAG", "source": "ado-variable-group", "sourceName": "PROCESSING_PAYMENT_FEATURE_FLAG"}, {"name": "PAYMENT_REFERENCE_NUMBER", "source": "ado-variable-group", "sourceName": "PAYMENT_REFERENCE_NUMBER"}, {"name": "HISTORY_DB_TABLE_NAME", "source": "ado-variable-group", "sourceName": "HISTORY_DB_TABLE_NAME"}, {"name": "DYNAMO_DB_REGION", "source": "ado-variable-group", "sourceName": "DYNAMO_DB_REGION"}, {"name": "DYNAMO_DB_URL", "source": "ado-variable-group", "sourceName": "DYNAMO_DB_URL"}, {"name": "DYNAMO_DB_ACCESS_KEY", "source": "local", "sourceName": "DYNAMO_DB_ACCESS_KEY"}, {"name": "DYNAMO_DB_ACCESS_KEY_ID", "source": "local", "sourceName": "DYNAMO_DB_ACCESS_KEY_ID"}, {"name": "SIGNED_REQUEST_LAMBDA_NAME", "source": "ado-variable-group", "sourceName": "SIGNED_REQUEST_LAMBDA_NAME"}, {"name": "APOLLO_INTERNAL_USER_ID", "source": "ado-variable-group", "sourceName": "APOLLO_INTERNAL_USER_ID"}, {"name": "CARDINAL_ENDPOINT", "source": "ado-variable-group", "sourceName": "CARDINAL_ENDPOINT"}, {"name": "ELASTICACHE_HOST", "source": "ado-variable-group", "sourceName": "ELASTICACHE_HOST"}, {"name": "ELASTICACHE_PORT", "source": "ado-variable-group", "sourceName": "ELASTICACHE_PORT"}, {"name": "MOCK_DPAAS_PAY_ENDPOINT", "source": "ado-variable-group", "sourceName": "MOCK_DPAAS_PAY_ENDPOINT"}, {"name": "DPAAS_AZURE_AD_TOKEN_URL", "source": "ado-variable-group", "sourceName": "DPAAS_AZURE_AD_TOKEN_URL"}, {"name": "DPAAS_AZURE_AD_CLIENT_ID", "source": "ado-variable-group", "sourceName": "DPAAS_AZURE_AD_CLIENT_ID"}, {"name": "DPAAS_WALLET_SCOPE", "source": "ado-variable-group", "sourceName": "DPAAS_WALLET_SCOPE"}, {"name": "DPAAS_PAY_SCOPE", "source": "ado-variable-group", "sourceName": "DPAAS_PAY_SCOPE"}, {"name": "DPAAS_3DS_SCOPE", "source": "ado-variable-group", "sourceName": "DPAAS_3DS_SCOPE"}, {"name": "PRIVATE_GATEWAY_CLUSTER_URL", "source": "ado-variable-group", "sourceName": "PRIVATE_GATEWAY_CLUSTER_URL"}, {"name": "APP_ENV", "source": "ado-variable-group", "sourceName": "APP_ENV"}, {"name": "AMDE_MERCHANT_ID", "source": "ado-variable-group", "sourceName": "AMDE_MERCHANT_ID"}, {"name": "AMNL_MERCHANT_ID", "source": "ado-variable-group", "sourceName": "AMNL_MERCHANT_ID"}, {"name": "AMUK_MERCHANT_ID", "source": "ado-variable-group", "sourceName": "AMUK_MERCHANT_ID"}, {"name": "AMUS_MERCHANT_ID", "source": "ado-variable-group", "sourceName": "AMUS_MERCHANT_ID"}, {"name": "AMES_MERCHANT_ID", "source": "ado-variable-group", "sourceName": "AMES_MERCHANT_ID"}, {"name": "IDP_PULSE_ISSUER", "source": "ado-variable-group", "sourceName": "IDP_PULSE_ISSUER"}, {"name": "IDP_ARAL_ISSUER", "source": "ado-variable-group", "sourceName": "IDP_ARAL_ISSUER"}, {"name": "CIP_TOKEN_ISSUER", "source": "ado-variable-group", "sourceName": "CIP_TOKEN_ISSUER"}, {"name": "ANON_TOKEN_ISSUER", "source": "ado-variable-group", "sourceName": "ANON_TOKEN_ISSUER"}, {"name": "DPAAS_TENANT_ENV", "source": "ado-variable-group", "sourceName": "DPAAS_TENANT_ENV"}, {"name": "APOLLO_INTERNAL_SECRET", "source": "aws-secrets-manager", "sourceName": "Payments-ApolloInternal-Secret"}, {"name": "DPAAS_AZURE_AD_CLIENT_SECRET", "source": "aws-secrets-manager", "sourceName": "Payments-AzureDpaasAdClient-Secret"}, {"name": "DATE_TYPE", "source": "ado-variable-group", "sourceName": "DATE_TYPE"}, {"name": "OPEN_ID_ACCESS_ROLE", "source": "ado-variable-group", "sourceName": "OPEN_ID_ACCESS_ROLE"}]