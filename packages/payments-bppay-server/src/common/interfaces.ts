import { PaymentDBStatus } from '../enums';

export interface IPaymentMethods {
  paymentMethodId: string;
  lastFour: string;
  cardType: string;
}
export interface IUpdateRecords {
  paymentId: string;
  chargeSessionId: string;
  referenceChargeSessionId: string;
  status: string;
  refundedDate: boolean;
}

export interface IStorePaymentStatusAndMethod {
  userId: string;
  paymentMethodId: string;
  paymentId: string;
  paymentDetails?: {
    lastFour?: string;
    cardType?: string;
  };
  amount: number;
  currency: string;
  status: PaymentDBStatus;
  chargeSessionId?: string;
  retrievalReferenceNumber?: string;
  outstanding?: boolean;
}

export interface IFormattedPaymentDetails {
  orderId?: string;
  paymentId?: string;
  chargeSessionId?: string;
  userId: string;
  preAuthAmount?: number;
  preAuthRoundedAmount?: number;
  transactionId?: string;
  transactionNumber?: string;
  finalPaymentStatus?: string;
  finalPayment?: {
    amount?: number;
    currency: string;
  };
  paymentMethodType?: string;
  orderStarted?: number;
  refundedDate?: number | undefined;
  voidTransaction: boolean;
  chargeStarted: boolean;
  preAuthStatus: boolean | null;
  cardDetails?: {
    fundingMethod: string;
    cardNumber: string;
    cardScheme: string;
  };
}
