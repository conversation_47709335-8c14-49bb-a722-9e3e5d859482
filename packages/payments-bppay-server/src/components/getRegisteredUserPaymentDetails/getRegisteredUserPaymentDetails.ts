import { IFormattedPaymentDetails } from '../../common/interfaces';
import { retrieveUserOrder } from '../../services/services';
import { BpPayServerContext } from '../../types/apollo';

const getRegisteredUserPaymentDetails = async (
  ctx: BpPayServerContext,
  userId: string,
) => {
  const transaction = await retrieveUserOrder(ctx, {
    userId,
  }).catch((e) => {
    ctx.logger.error(
      `🚨 Error encountered: getRegisteredUserPaymentDetails failed, details: `,
      e,
    );
    throw e;
  });

  if (!transaction || !transaction.payment_id || !transaction.user_id) {
    ctx.logger.error(
      'getRegisteredUserPaymentDetails failed, Payment data for transaction not available in store',
    );
    return {
      status: 412,
      message: 'Payment data for transaction not available in store',
    };
  }

  const {
    preauth: preAuthStatus,
    pre_authorization: preAuth,
    payment_id: paymentId,
    transaction_id: transactionId,
    transaction_number: transactionNumber,
    final_payment_status: finalPaymentStatus,
    final_payment: finalPayment,
    payment_method_type: paymentMethodType,
    card_details: cardDetails,
    charge_status: chargeStatus,
    void_transaction: voidTransaction,
    order_id: orderId,
    order_started: orderStarted,
    refunded_date: refundedDate,
    charge_session_id: chargeSessionId,
  } = transaction;

  const preAuthAmount = preAuth?.amount;
  const preAuthRoundedAmount = preAuth?.rounded_amount;

  const paymentData: IFormattedPaymentDetails = {
    orderId,
    paymentId,
    chargeSessionId,
    userId,
    preAuthAmount,
    preAuthRoundedAmount,
    transactionId,
    transactionNumber,
    finalPaymentStatus,
    finalPayment,
    paymentMethodType,
    orderStarted,
    refundedDate,
    voidTransaction: !!voidTransaction,
    chargeStarted: chargeStatus === 'started',
    preAuthStatus: preAuthStatus ?? null,
  };

  if (cardDetails) {
    const {
      funding_method: fundingMethod,
      card_number: cardNumber,
      card_scheme: cardScheme,
    } = cardDetails;
    paymentData.cardDetails = { fundingMethod, cardNumber, cardScheme };
  }
  return {
    status: 200,
    ...paymentData,
    message: 'ok',
  };
};

export default getRegisteredUserPaymentDetails;
