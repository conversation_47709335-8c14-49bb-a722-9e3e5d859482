import logger from '../../utils/logger';
import { contextMock } from '../../utils/mock';
import getUserPaymentDetails from './getUserPaymentDetails';

jest.mock('../../env', () => ({}));

const mockRetrieveUserOrder = jest.fn();
jest.mock('../../services/services', () => ({
  retrieveUserOrder: () => mockRetrieveUserOrder(),
}));
jest.spyOn(logger, 'error').mockImplementation(jest.fn());
const ctx = contextMock();

describe('getUserPaymentDetails', () => {
  const mockBaseRawPaymentData = {
    payment_id: 'paymentId',
    user_id: 'userId',
    pre_authorization: { amount: 20, rounded_amount: 20 },
    transaction_id: 'transactionId',
    transaction_number: 'transactionNumber',
    final_payment_status: 'finalPaymentStatus',
    final_payment: 'finalPayment',
    payment_method_type: 'paymentMethodType',
    order_started: '1677067654',
    refunded_date: '1677067800',
  };

  it('should return a status 412 if it cannot find user', async () => {
    mockRetrieveUserOrder.mockResolvedValue({});
    const res = await getUserPaymentDetails('userId');
    expect(res).toStrictEqual({
      status: '412',
      message: 'Payment data for transaction not available in store',
    });
  });

  it('should get the payment status in dynamo', async () => {
    mockRetrieveUserOrder.mockResolvedValue({
      ...mockBaseRawPaymentData,
      preauth: true,
      card_details: {
        funding_method: 'fundingMethod',
        card_number: 'cardNumber',
        card_scheme: 'cardScheme',
      },
    });

    const res = await getUserPaymentDetails('userId');
    expect(res).toStrictEqual({
      status: '200',
      paymentId: 'paymentId',
      userId: 'userId',
      preAuthAmount: 20,
      preAuthRoundedAmount: 20,
      transactionId: 'transactionId',
      transactionNumber: 'transactionNumber',
      finalPaymentStatus: 'finalPaymentStatus',
      finalPayment: 'finalPayment',
      paymentMethodType: 'paymentMethodType',
      chargeStarted: false,
      voidTransaction: false,
      preAuthStatus: true,
      cardDetails: {
        fundingMethod: 'fundingMethod',
        cardNumber: 'cardNumber',
        cardScheme: 'cardScheme',
      },
      message: 'ok',
      orderId: undefined,
      refundedDate: '1677067800',
      chargeSessionId: undefined,
      orderStarted: '1677067654',
    });
  });

  it('should return preauth status as null if its are missing from database', async () => {
    mockRetrieveUserOrder.mockResolvedValue(mockBaseRawPaymentData);

    const res = await getUserPaymentDetails('userId');
    expect(res).toStrictEqual({
      status: '200',
      paymentId: 'paymentId',
      userId: 'userId',
      preAuthAmount: 20,
      preAuthRoundedAmount: 20,
      transactionId: 'transactionId',
      transactionNumber: 'transactionNumber',
      finalPaymentStatus: 'finalPaymentStatus',
      finalPayment: 'finalPayment',
      preAuthStatus: null,
      paymentMethodType: 'paymentMethodType',
      chargeStarted: false,
      voidTransaction: false,
      message: 'ok',
      orderId: undefined,
      refundedDate: '1677067800',
      chargeSessionId: undefined,
      orderStarted: '1677067654',
    });
  });

  it('should return true if the transaction has been voided', async () => {
    mockRetrieveUserOrder.mockResolvedValue({
      ...mockBaseRawPaymentData,
      void_transaction: true,
    });

    const res = await getUserPaymentDetails('userId');
    expect(res).toStrictEqual({
      status: '200',
      paymentId: 'paymentId',
      userId: 'userId',
      preAuthAmount: 20,
      preAuthRoundedAmount: 20,
      transactionId: 'transactionId',
      transactionNumber: 'transactionNumber',
      preAuthStatus: null,
      finalPaymentStatus: 'finalPaymentStatus',
      finalPayment: 'finalPayment',
      paymentMethodType: 'paymentMethodType',
      voidTransaction: true,
      chargeStarted: false,
      message: 'ok',
      orderId: undefined,
      refundedDate: '1677067800',
      chargeSessionId: undefined,
      orderStarted: '1677067654',
    });
  });

  it('should return chargeStarted as true if the charge_status is "started"', async () => {
    mockRetrieveUserOrder.mockResolvedValue({
      ...mockBaseRawPaymentData,
      charge_status: 'started',
      charge_session_id: 'chargeSessionId',
    });

    const res = await getUserPaymentDetails('userId');
    expect(res).toStrictEqual({
      status: '200',
      paymentId: 'paymentId',
      userId: 'userId',
      preAuthAmount: 20,
      preAuthRoundedAmount: 20,
      transactionId: 'transactionId',
      preAuthStatus: null,
      transactionNumber: 'transactionNumber',
      finalPaymentStatus: 'finalPaymentStatus',
      finalPayment: 'finalPayment',
      paymentMethodType: 'paymentMethodType',
      chargeStarted: true,
      voidTransaction: false,
      message: 'ok',
      orderId: undefined,
      refundedDate: '1677067800',
      chargeSessionId: 'chargeSessionId',
      orderStarted: '1677067654',
    });
  });

  it('should throw an error and log the error if fails dynamo call', async () => {
    const error = new Error('dynamo error');
    mockRetrieveUserOrder.mockRejectedValue(error);
    await expect(getUserPaymentDetails(ctx, 'userId')).rejects.toThrow(error);
    expect(ctx.logger.error).toHaveBeenCalledWith(
      '🚨 Error encountered: getUserPaymentDetails failed, details: ',
      error,
    );
  });
});
