import { retrieveUserOrder } from '../../services/services';
import { BpPayServerContext } from '../../types/apollo';
import formatPaymentDetails from '../../utils/formatPaymentDetails';

const getUserPaymentDetails = async (
  ctx: BpPayServerContext,
  userId: string,
) => {
  const order = await retrieveUserOrder(ctx, {
    userId,
  }).catch((e) => {
    ctx.logger.error(
      `🚨 Error encountered: getUserPaymentDetails failed, details: `,
      e,
    );
    throw e;
  });

  if (
    !order ||
    !order.payment_id ||
    !order.user_id ||
    !order.pre_authorization ||
    !order.pre_authorization.amount
  ) {
    return {
      status: '412',
      message: 'Payment data for transaction not available in store',
    };
  }
  const paymentData = formatPaymentDetails(order);

  return {
    status: '200',
    ...paymentData,
    message: 'ok',
  };
};

export default getUserPaymentDetails;
