import { OrderStatus, PaymentStatus } from '../../enums';
import * as dateNow from '../../utils/dateNow';
import * as dateUtils from '../../utils/date';
import logger from '../../utils/logger';
import { contextMock } from '../../utils/mock';
import updateRefundStatus from './updateRefundStatus';
const mockGenerateReceipt = jest.fn();
const mockGetHistoryRecord = jest.fn();
const mockUpdateHistoryRecord = jest.fn();
const mockUpdateRefundedOrder = jest.fn();

jest.mock('../../env', () => ({}));

jest.mock('../../services/services', () => ({
  generateReceipt: () => mockGenerateReceipt(),
  getHistoryRecord: () => mockGetHistoryRecord(),
  updateHistoryRecord: () => mockUpdateHistoryRecord(),
  updateRefundedOrder: (ctx, updateRefundArgs) =>
    mockUpdateRefundedOrder(ctx, updateRefundArgs),
}));
jest.spyOn(logger, 'error').mockImplementation(jest.fn());

jest
  .spyOn(dateNow, 'default')
  .mockReturnValue(
    'Tue Mar 07 2023 11:53:06 GMT+0200 (Eastern European Standard Time)',
  );

jest.spyOn(dateUtils, 'isoDateToUnix').mockReturnValue(1678182664000);

let mockStatus = PaymentStatus.REFUNDED;
const updateRefundArgs = {
  paymentId: 'paymentId',
  referenceChargeSessionId: 'AMDE-REF',
  paymentStatus: mockStatus,
  refundedDate: 1678182664000,
};
const argsSuccessful = {
  refundStatus: OrderStatus.REFUNDED,
  paymentId: 'paymentId',
  chargeSessionId: 'charge-session-id',
  userId: 'userId',
  type: 'userType',
};
const argsFailed = {
  refundStatus: OrderStatus.REFUND_FAILED,
  paymentId: 'paymentId',
  chargeSessionId: 'charge-session-id',
  userId: 'userId',
  type: 'userType',
};

beforeEach(() => {
  jest.clearAllMocks();
});

const ctx = contextMock();

describe('updateRefundStatus', () => {
  it('should update history and payment records succesfully when the refund status is refunded', async () => {
    mockGetHistoryRecord.mockResolvedValue({
      getHistoryRecord: {
        referenceChargeSessionId: 'AMDE-REF',
        chargeDetails: { isStandardInvoice: false },
      },
    });
    mockUpdateHistoryRecord.mockResolvedValue({});
    mockUpdateRefundedOrder.mockResolvedValue({});
    mockGenerateReceipt.mockResolvedValue({});
    await updateRefundStatus(ctx, argsSuccessful);
    expect(mockUpdateRefundedOrder).toHaveBeenCalledWith(ctx, {
      paymentId: 'paymentId',
      referenceChargeSessionId: 'AMDE-REF',
      paymentStatus: PaymentStatus.REFUNDED,
      refundedDate: 1678182664000,
    });
    expect(mockGenerateReceipt).toHaveBeenCalledTimes(1);
  });
  it('should update history and payment records succesfully and generate receipt when the refund status is failed', async () => {
    mockStatus = PaymentStatus.REFUND_FAILED;
    mockGetHistoryRecord.mockResolvedValue({
      getHistoryRecord: { referenceChargeSessionId: 'AMDE-REF' },
    });
    mockUpdateHistoryRecord.mockResolvedValue({});
    mockUpdateRefundedOrder.mockResolvedValue({});
    mockGenerateReceipt.mockResolvedValue({});
    await updateRefundStatus(ctx, argsFailed);

    expect(mockUpdateRefundedOrder).toHaveBeenCalledWith(ctx, {
      paymentId: 'paymentId',
      referenceChargeSessionId: 'AMDE-REF',
      paymentStatus: PaymentStatus.REFUND_FAILED,
      refundedDate: 0,
    });
  });

  describe('error scenarios', () => {
    it('should throw error when getHistoryRecord is not successful', async () => {
      mockGetHistoryRecord.mockRejectedValue(new Error('error'));
      await expect(updateRefundStatus(ctx, argsSuccessful)).rejects.toThrow(
        new Error('getHistoryRecord error'),
      );
      expect(logger.error).toHaveBeenCalled();
    });
    it('should throw error when updateHistoryRecord is not successful', async () => {
      mockGetHistoryRecord.mockResolvedValue({
        getHistoryRecord: { referenceChargeSessionId: 'AMDE-REF' },
      });
      mockUpdateHistoryRecord.mockRejectedValue(
        new Error('update history error'),
      );
      await expect(updateRefundStatus(ctx, argsSuccessful)).rejects.toThrow(
        new Error('update history error'),
      );
      expect(logger.error).toHaveBeenCalled();
    });
    it('should throw error when updateRefundedOrder is not successful', async () => {
      mockUpdateHistoryRecord.mockResolvedValue({});
      mockUpdateRefundedOrder.mockRejectedValue(
        new Error('update payment error'),
      );
      await expect(updateRefundStatus(ctx, argsSuccessful)).rejects.toThrow(
        new Error('update payment error'),
      );
      expect(logger.error).toHaveBeenCalled();
    });
    it('should throw error when generateReceipt is not successful', async () => {
      mockUpdateRefundedOrder.mockResolvedValue({});
      mockGenerateReceipt.mockRejectedValue(new Error('generate error'));
      await expect(updateRefundStatus(ctx, argsSuccessful)).rejects.toThrow(
        new Error('generate error'),
      );
      expect(logger.error).toHaveBeenCalled();
    });
  });
});
