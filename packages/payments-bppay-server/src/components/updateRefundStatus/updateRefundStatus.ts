import { IUpdateRecords } from '../../common/interfaces';
import { OrderStatus, PaymentStatus } from '../../enums';
import {
  generateReceipt,
  getHistoryRecord,
  updateHistoryRecord,
  updateRefundedOrder,
} from '../../services/services';
import { BpPayServerContext } from '../../types/apollo';
import { isoDateToUnix } from '../../utils/date';
import dateNow from '../../utils/dateNow';

interface IUpdateRefund {
  refundStatus: string;
  paymentId: string;
  chargeSessionId: string;
  userId: string;
  type: string;
}
const updateHistoryAndPaymentRecords = async (
  ctx: BpPayServerContext,
  {
    paymentId,
    chargeSessionId,
    referenceChargeSessionId,
    status,
    refundedDate,
  }: IUpdateRecords,
) => {
  const currentTime = dateNow();

  await updateHistoryRecord({
    paymentId,
    chargeSessionId,
    paymentStatus: status,
  }).catch((e: Error) => {
    ctx.logger.error(
      `updateHistoryAndPaymentRecords: Could not update charge history record with chargeSessionId ${chargeSessionId}`,
      { e },
    );
    throw e;
  });

  await updateHistoryRecord({
    paymentId,
    chargeSessionId: referenceChargeSessionId,
    paymentStatus: status,
  }).catch((e: Error) => {
    ctx.logger.error(
      `updateHistoryAndPaymentRecords: Could not update credit history record with referenceChargeSessionId ${referenceChargeSessionId}`,
      { e },
    );
    throw e;
  });

  //updating the payments table
  await updateRefundedOrder(ctx, {
    paymentId,
    referenceChargeSessionId,
    paymentStatus: status,
    refundedDate: refundedDate ? isoDateToUnix(currentTime) : 0,
  }).catch((e: Error) => {
    ctx.logger.error('updateOrder error', { e });
    throw e;
  });
};

const updateRefundStatus = async (
  ctx: BpPayServerContext,
  { refundStatus, paymentId, chargeSessionId, userId, type }: IUpdateRefund,
) => {
  const historyRecord = await getHistoryRecord({
    userId,
    chargeSessionId,
  }).catch((e: Error) => {
    ctx.logger.error('updateRefundStatus: getHistoryRecord error', { e });
    throw new Error('getHistoryRecord error');
  });
  const { referenceChargeSessionId } = historyRecord.getHistoryRecord;

  if (refundStatus === OrderStatus.REFUNDED) {
    await updateHistoryAndPaymentRecords(ctx, {
      paymentId,
      chargeSessionId,
      referenceChargeSessionId,
      status: PaymentStatus.REFUNDED,
      refundedDate: true,
    });
    if (!historyRecord.getHistoryRecord?.chargeDetails?.isStandardInvoice) {
      await generateReceipt({
        chargeSessionId: referenceChargeSessionId,
        userId,
        userType: type,
      })
        .then((res) => {
          ctx.logger.info('generateReceipt info:', res);
          return res;
        })
        .catch((e: Error) => {
          ctx.logger.error('generate pdf receipt error', { e });
          throw e;
        });
    }
  } else {
    await updateHistoryAndPaymentRecords(ctx, {
      paymentId,
      chargeSessionId,
      referenceChargeSessionId,
      status: PaymentStatus.REFUND_FAILED,
      refundedDate: false,
    });
  }
};
export default updateRefundStatus;
