import { PaymentService } from '../../enums';
import {
  preAuth,
  retrieveUserOrder,
  storePreAuthStatus,
} from '../../services/services';
import { BpPayServerContext } from '../../types/apollo';
import { MutationPreAuthArgs } from '../../types/graphql';
import { attempt } from '../../utils/attempt';

const serviceName = PaymentService.PreAuthResolver;

const validateOrderAttributes = (ctx: BpPayServerContext, order: any) => {
  if (!order || !order.journey_id || !order.payment_id) {
    ctx.logger.error('Invalid order attributes', {
      serviceName,
      order,
      journey_id: order.journey_id,
      payment_id: order.payment_id,
    });
    throw new Error('No journey id for user in store');
  }
};

export default async (ctx: BpPayServerContext, args: MutationPreAuthArgs) => {
  const [order, retrieveError] = await attempt(
    retrieveUserOrder(ctx, {
      userId: ctx.userId ?? '',
    }),
  );
  if (retrieveError || !order) {
    ctx.logger.error('retrieveUserOrder error', {
      serviceName,
      err: retrieveError,
    });
    throw new Error('retrieveUserOrder error');
  }

  validateOrderAttributes(ctx, order);

  const {
    journey_id: journeyId,
    payment_id: paymentId,
    payment_method_type: paymentMethodType,
    session_id: sessionId,
  } = order;

  // make request to verify card
  // using non-null assertion on params since they are always present in preauth flow
  const result = await preAuth(ctx, {
    journeyId: journeyId!,
    token: args?.token || sessionId!,
    paymentMethodType: paymentMethodType!,
  }).catch((err: any) => {
    ctx.logger.error('preAuth error', { serviceName, err });
    throw new Error('preAuth error');
  });

  await storePreAuthStatus(ctx, { status: result, paymentId }).catch(
    (err: any) => {
      ctx.logger.error('storePreAuthStatus error', { serviceName, err });
      throw new Error('storePreAuthStatus error');
    },
  );

  ctx.logger.info('Mutation successful', {
    serviceName,
    response: result,
    paymentId,
  });

  // return successful status
  return result;
};
