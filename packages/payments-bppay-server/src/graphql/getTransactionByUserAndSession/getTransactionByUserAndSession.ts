import { PaymentService } from '../../enums';
import { retrieveUserTransactions } from '../../services/services';
import { BpPayServerContext, ChargeStatus } from '../../types';
import {
  MatchedTransaction,
  QueryGetTransactionByUserAndSessionArgs,
} from '../../types/graphql';
import { attempt } from '../../utils/attempt';
import { isIsoDateWithinRange, isoDateToUnix } from '../../utils/date';

const MILLISECONDS_1_HOUR = 3600000;
const MILLISECONDS_5_MINUTES = 300000;

const serviceName = PaymentService.GetTransactionByUserAndSession;

export default async (
  ctx: BpPayServerContext,
  args: QueryGetTransactionByUserAndSessionArgs,
): Promise<MatchedTransaction | null> => {
  const { userId, chargeSessionStart, connectorExternalId } = args;
  const chargeSessionStartTimestamp = isoDateToUnix(chargeSessionStart);

  const [transactions, retrieveError] = await attempt(
    retrieveUserTransactions({
      userId,
      startDateTimestamp: chargeSessionStartTimestamp - MILLISECONDS_1_HOUR,
      endDateTimestamp: chargeSessionStartTimestamp + MILLISECONDS_1_HOUR,
    }),
  );

  if (retrieveError) {
    ctx.logger.error(
      `Failed to get transactions for userId: ${userId}, chargeSessionStart: ${chargeSessionStart}, connectorExternalId: ${connectorExternalId}`,
      { serviceName, serviceApiResponseCode: 500, err: retrieveError },
    );
    return null;
  }

  const matchedTransaction = transactions?.find((transaction) => {
    if (!transaction.charge_session_start) {
      return false;
    }
    return (
      isIsoDateWithinRange(
        transaction.charge_session_start,
        chargeSessionStart,
        MILLISECONDS_5_MINUTES,
      ) &&
      transaction.connector_internal_id === connectorExternalId &&
      transaction.preauth &&
      transaction.charge_status === ChargeStatus.STARTED &&
      !transaction.void_transaction &&
      !transaction.final_payment_status
    );
  });

  if (!matchedTransaction) {
    ctx.logger.error(
      `No matching transaction found for userId: ${userId}, chargeSessionStart: ${chargeSessionStart}, connectorExternalId: ${connectorExternalId}`,
      { serviceName, serviceApiResponseCode: 404 },
    );
    return null;
  }

  ctx.logger.info(
    `Matched transaction for userId: ${userId}, chargeSessionStart: ${chargeSessionStart}, connectorExternalId: ${connectorExternalId}`,
    {
      serviceName,
      serviceApiResponseCode: 200,
      paymentId: matchedTransaction.payment_id,
    },
  );
  return {
    paymentId: matchedTransaction.payment_id,
    chargeSessionId: matchedTransaction.charge_session_id,
  };
};
