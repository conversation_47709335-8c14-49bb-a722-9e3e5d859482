import { BpPayServerContext } from '../../types';
import {
  MatchedTransaction,
  QueryGetTransactionByUserAndSessionArgs,
} from '../../types/graphql';
import getTransactionByUserAndSessionFn from './getTransactionByUserAndSession';

const getTransactionByUserAndSessionRevolver = {
  Query: {
    getTransactionByUserAndSession: (
      _: undefined,
      args: QueryGetTransactionByUserAndSessionArgs,
      ctx: BpPayServerContext,
    ): Promise<MatchedTransaction | null> =>
      getTransactionByUserAndSessionFn(ctx, args),
  },
};

exports.resolver = getTransactionByUserAndSessionRevolver;
