import { PaymentStatus } from '../../enums';
import { TransactionI } from '../../services/database/interfaces';
import { ChargeStatus } from '../../types';
import logger from '../../utils/logger';
import { contextMock } from '../../utils/mock';
import getTransactionByUserAndSessionFn from './getTransactionByUserAndSession';

const ctx = contextMock(undefined, undefined, 'scope');

jest.spyOn(logger, 'error').mockImplementation(jest.fn());
jest.spyOn(logger, 'info').mockImplementation(jest.fn());

jest.mock('../../env', () => {
  return {
    NODE_ENV: 'development',
  };
});

const mockTransaction: Partial<TransactionI> = {
  payment_id: 'AMNL-fcdd2517-8f56-4643-b389-61a0e854521',
  preauth: true,
  charge_session_id: '3a66867f-1c37-4b37-b1ec-9b9de8775b4a',
  charge_session_start: '2025-08-14T13:04:00.000Z',
  user_id: 'user-1',
  connector_internal_id: 'connector-id',
  charge_status: ChargeStatus.STARTED,
  void_transaction: false,
};
const mockTransactions = [mockTransaction];
const mockRetrieveUserTransactions = jest.fn();
jest.mock('../../services/services', () => ({
  retrieveUserTransactions: () => mockRetrieveUserTransactions(),
}));

const defaultArgs = {
  chargeSessionStart: '2025-08-14T13:00:00.000Z',
  connectorExternalId: 'connector-id',
  userId: 'user-1',
};

describe('getTransactionByUserAndSession', () => {
  beforeEach(() => jest.clearAllMocks());
  it('should return paymentId and chargeSessionId of a matched transaction', async () => {
    mockRetrieveUserTransactions.mockResolvedValueOnce(mockTransactions);
    const result = await getTransactionByUserAndSessionFn(ctx, defaultArgs);
    expect(logger.info).toHaveBeenCalledWith(
      'Matched transaction for userId: user-1, chargeSessionStart: 2025-08-14T13:00:00.000Z, connectorExternalId: connector-id',
      {
        serviceApiResponseCode: 200,
        serviceName: 'getTransactionByUserAndSession',
        paymentId: 'AMNL-fcdd2517-8f56-4643-b389-61a0e854521',
      },
    );
    expect(result).toStrictEqual({
      chargeSessionId: '3a66867f-1c37-4b37-b1ec-9b9de8775b4a',
      paymentId: 'AMNL-fcdd2517-8f56-4643-b389-61a0e854521',
    });
  });

  it('should log error and return null if it fails to retrieve transactions', async () => {
    const error = new Error('retrieve error');
    mockRetrieveUserTransactions.mockRejectedValueOnce(error);
    const result = await getTransactionByUserAndSessionFn(ctx, defaultArgs);
    expect(logger.error).toHaveBeenCalledWith(
      'Failed to get transactions for userId: user-1, chargeSessionStart: 2025-08-14T13:00:00.000Z, connectorExternalId: connector-id',
      {
        err: error,
        serviceApiResponseCode: 500,
        serviceName: 'getTransactionByUserAndSession',
      },
    );
    expect(result).toBeNull();
  });

  it('should log error and return null if none transactions are retrieved', async () => {
    mockRetrieveUserTransactions.mockResolvedValueOnce([]);
    const result = await getTransactionByUserAndSessionFn(ctx, defaultArgs);
    expect(logger.error).toHaveBeenCalledWith(
      'No matching transaction found for userId: user-1, chargeSessionStart: 2025-08-14T13:00:00.000Z, connectorExternalId: connector-id',
      {
        serviceApiResponseCode: 404,
        serviceName: 'getTransactionByUserAndSession',
      },
    );
    expect(result).toBeNull();
  });

  it('should log error and return null if none of the retrieved transactions match', async () => {
    mockRetrieveUserTransactions.mockResolvedValueOnce([
      { ...mockTransaction, charge_session_start: undefined }, // missing charge_session_start
      { ...mockTransaction, charge_session_start: '2025-08-14T13:05:01.000Z' }, // not in date range
      { ...mockTransaction, connector_internal_id: 'different-connector-id' }, // no matching connectorId
      { ...mockTransaction, preauth: undefined }, // no preauth
      { ...mockTransaction, charge_status: undefined }, // charge_status not started
      { ...mockTransaction, void_transaction: true }, // voided transaction
      { ...mockTransaction, final_payment_status: PaymentStatus.CAPTURED }, // already paid
    ]);
    const result = await getTransactionByUserAndSessionFn(ctx, defaultArgs);
    expect(logger.error).toHaveBeenCalledWith(
      'No matching transaction found for userId: user-1, chargeSessionStart: 2025-08-14T13:00:00.000Z, connectorExternalId: connector-id',
      {
        serviceApiResponseCode: 404,
        serviceName: 'getTransactionByUserAndSession',
      },
    );
    expect(result).toBeNull();
  });
});
