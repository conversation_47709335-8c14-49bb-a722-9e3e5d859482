import { v4 as uuidv4 } from 'uuid';

import { PaymentService, PaymentType } from '../../enums';
import AppTenantConfig from '../../services/bpPay/appTenantConfig';
import { storeVoidTransaction, voidOrderDpaas } from '../../services/services';
import { BpPayServerContext } from '../../types/apollo';
import { MutationVoidOrderArgs, VoidOrderResult } from '../../types/graphql';
import { formatUTCDate } from '../../utils/date';
import dateNow from '../../utils/dateNow';
import { requestWithRetry } from '../../utils/retry';

const serviceName = PaymentService.VoidOrderResolver;

export async function voidOrderService(
  ctx: BpPayServerContext,
  args: MutationVoidOrderArgs,
): Promise<VoidOrderResult> {
  const { paymentData, appType } = args;

  if (!paymentData.length) {
    return {
      message: 'No transactions to be voided',
      status: 400,
      transactions: [],
    };
  }

  const transactions: VoidOrderResult['transactions'] = [];

  for (const { paymentId, country } of paymentData) {
    const correlationId = uuidv4();
    const operationUId = uuidv4();
    const now = dateNow();

    const { tenantConfig } = AppTenantConfig({
      paymentType: PaymentType.WALLET,
      appType: appType ?? undefined,
      appCountry: country ?? undefined,
    });

    const params = {
      ctx,
      paymentId,
      operationUId,
      timestamp: now,
      country,
      txnReferenceNumber: `000CG${formatUTCDate(now)}`,
      correlationId,
      tenant: tenantConfig,
    };

    const response = await requestWithRetry(voidOrderDpaas, params).catch(
      (err) => {
        transactions.push({ isVoided: false, paymentId });

        ctx.logger.error(`voidOrderDpaas error`, {
          serviceName,
          serviceApiResponseCode: 400,
          err,
          paymentId,
        });
        return undefined;
      },
    );

    if (!response) {
      continue;
    }

    ctx.logger.info(`voidOrderDpaas success`, {
      serviceName,
      serviceApiResponseCode: 200,
      paymentId,
    });

    transactions.push({ isVoided: true, paymentId });
  }

  await storeVoidTransaction(
    ctx,
    transactions.map((transaction) => ({
      paymentId: transaction.paymentId,
      voidStatus: transaction.isVoided,
    })),
  ).catch((err) => {
    ctx.logger.error(`storeVoidTransaction error`, {
      serviceName,
      serviceApiResponseCode: 500,
      err,
    });
    throw new Error('voidOrder: storeVoidTransaction error');
  });

  const hasFailedTransaction = transactions.some((e) => !e.isVoided);

  if (hasFailedTransaction) {
    ctx.logger.error(`Mutation failed. Some transactions failed to be voided`, {
      serviceName,
      serviceApiResponseCode: 400,
      transactions,
    });

    return {
      message: 'Some transactions failed to be voided',
      status: 400,
      transactions,
    };
  }

  ctx.logger.info(`Mutation successful: All transactions successfully voided`, {
    serviceName,
    serviceApiResponseCode: 200,
    transactions,
  });

  return {
    message: 'All transactions successfully voided',
    status: 200,
    transactions,
  };
}
