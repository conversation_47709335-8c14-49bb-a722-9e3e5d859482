import { v4 as uuidv4 } from 'uuid';

import getRefundOrder from '../../components/getRefundOrder/getRefundOrder';
import updateRefundStatus from '../../components/updateRefundStatus/updateRefundStatus';
import { OrderStatus, PaymentService, PaymentType } from '../../enums';
import appTenantConfig from '../../services/bpPay/appTenantConfig';
import { refundOrder } from '../../services/services';
import { BpPayServerContext } from '../../types/apollo';
import { MutationRefundOrderArgs } from '../../types/graphql';
import { formatUTCDate } from '../../utils/date';
import dateNow from '../../utils/dateNow';

const serviceName = PaymentService.RefundOrderResolver;

export default async (
  ctx: BpPayServerContext,
  args: MutationRefundOrderArgs,
) => {
  const { amount, currency } = args;
  const userId = args.userId ?? '';
  const paymentId = args.paymentId ?? '';

  const getRefundOrderResult = await getRefundOrder({
    userId: userId ?? '',
    paymentId: paymentId ?? '',
  }).catch((e) => {
    ctx.logger.error('getRefundOrder error', {
      serviceName,
      e,
    });
    throw new Error('getRefundOrder error');
  });
  const { order, appCountry, type } = getRefundOrderResult;

  const { tenantConfig } = appTenantConfig({
    appCountry,
    paymentType: PaymentType.WALLET,
  });

  if (!tenantConfig) {
    ctx.logger.error('Could not get tenant config', {
      serviceName,
    });
    throw new Error('Could not get tenant config');
  }

  const {
    correlation_id: correlationId,
    charge_session_id: chargeSessionId,
    final_payment_status: paymentStatus,
  } = order;

  if (paymentStatus.toLowerCase() !== OrderStatus.CAPTURED) {
    ctx.logger.error('The payment is not captured', {
      serviceName,
    });
    throw new Error('Payment is not captured');
  }

  const operationId = uuidv4();
  const currentTime = dateNow();

  const refundResponse: any = await refundOrder(
    ctx,
    {
      paymentId,
      country: appCountry.toLowerCase(),
      tenant: tenantConfig,
      correlationId,
      operationUId: operationId,
      amount: amount as number,
      currency: currency ?? '',
      timestamp: currentTime,
      userId,
      txnReferenceNumber: `000CG${formatUTCDate(dateNow())}`,
    },
    ctx.scope ?? undefined,
  ).catch((e) => {
    ctx.logger.error('dpaas refundOrder error', { serviceName, e });
    return {
      status: OrderStatus.REFUND_FAILED,
    };
  });

  await updateRefundStatus(ctx, {
    refundStatus: refundResponse.status,
    paymentId,
    chargeSessionId,
    userId,
    type,
  });

  if (refundResponse.status === OrderStatus.REFUND_FAILED) {
    throw new Error('refundOrder has failed');
  }

  ctx.logger.info('Mutation successful', {
    serviceName,
    response: refundResponse,
  });

  return {
    paymentId,
    status: refundResponse.status,
  };
};
