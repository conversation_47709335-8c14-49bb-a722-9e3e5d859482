import { v4 as uuidv4 } from 'uuid';

import { OrderStatus, PaymentService, PaymentType } from '../../enums';
import appTenantConfig from '../../services/bpPay/appTenantConfig';
import { AuthoriseWalletPaymentResponse } from '../../services/bpPay/interfaces';
import {
  authoriseWalletPayment,
  getThreeDSData,
  storeAuthOrder,
} from '../../services/services';
import { BpPayServerContext } from '../../types/apollo';
import { MutationAuthoriseWalletPaymentArgs } from '../../types/graphql';
import { formatUTCDate } from '../../utils/date';
import dateNow from '../../utils/dateNow';

const serviceName = PaymentService.AuthoriseWalletPaymentResolver;

export default async (
  ctx: BpPayServerContext,
  args: MutationAuthoriseWalletPaymentArgs,
): Promise<boolean> => {
  const {
    paymentId,
    amount,
    currency,
    userId,
    paymentMethodId,
    threeDS,
    appType,
    appCountry,
  } = args;

  const currencyValue = currency ?? '';

  const { tenantConfig } = appTenantConfig({
    paymentType: PaymentType.WALLET,
    appType: appType ?? undefined,
    appCountry: appCountry ?? undefined,
  });
  const country = appCountry?.toUpperCase();
  const operationUId = uuidv4();
  const currentTime = dateNow();
  const txnReferenceNumber = `000CG${formatUTCDate(dateNow())}`;

  const response: AuthoriseWalletPaymentResponse = await authoriseWalletPayment(
    ctx,
    {
      paymentId: paymentId ?? '',
      operationUId,
      amount: amount ?? undefined,
      currency: currencyValue,
      timestamp: currentTime,
      paymentMethodId: paymentMethodId ?? '',
      userId: userId ?? '',
      threeDS: getThreeDSData(threeDS || undefined),
      country: country ?? '',
      tenant: tenantConfig,
      txnReferenceNumber: txnReferenceNumber,
    },
    ctx.scope ?? undefined,
  ).catch((err) => {
    ctx.logger.error('authoriseWalletPayment error', {
      serviceName,
      err,
    });
    throw new Error('authoriseWalletPayment error');
  });

  if (response?.status === OrderStatus.AUTHORIZED) {
    await storeAuthOrder(ctx, {
      paymentId: paymentId ?? '',
      userId: userId ?? '',
      amount: amount ?? undefined,
      currency: currencyValue,
      orderStatus: OrderStatus.AUTHORIZED,
      appCountry: appCountry ?? '',
    }).catch((err: any) => {
      ctx.logger.error('storePaymentStatus error', {
        serviceName,
        err,
      });
      throw new Error('storePaymentStatus error');
    });

    ctx.logger.info('Mutation successful', {
      serviceName,
      response,
      paymentId,
    });

    return true;
  }

  ctx.logger.info('Mutation failed', {
    serviceName,
    response,
    paymentId,
  });

  return false;
};
