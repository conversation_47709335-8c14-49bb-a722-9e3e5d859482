import axios from 'axios';

import logger from '../../utils/logger';
import { contextMock } from '../../utils/mock';
import startJourneyFn from './startJourney';

// remove the following line to see debug logger logs
jest.spyOn(logger, 'info').mockImplementation(jest.fn());
jest.spyOn(logger, 'error').mockImplementation(jest.fn());

const dynamoClient = require('../../utils/dynamoClient').default;
jest.mock('axios');
jest.mock('@bp/pulse-common', () => ({
  getDPaaSAuthToken: jest.fn().mockReturnValue(Promise.resolve('12345')),
}));
jest.mock('../../env', () => {
  return {
    APP_ENV: 'development',
  };
});
const mockAppTenantConfig = jest.fn();
jest.mock('../../services/bpPay/appTenantConfig', () => ({
  __esModule: true,
  default: () => mockAppTenantConfig(),
}));

const mockRetrieveUserTransactions = jest.fn();
jest.mock('../../services/database/retrieve', () => ({
  retrieveUserTransactions: () => mockRetrieveUserTransactions(),
}));

Date.now = jest.fn(() => new Date(Date.UTC(2021, 1, 14, 1, 6, 32)).valueOf());

dynamoClient.put = jest.fn(() => ({
  promise: jest.fn(() => {
    return new Promise(function (resolve) {
      resolve({});
    });
  }),
}));

const randomIdName = 'AMNL-RandomChargeId';
const ctx = contextMock();

describe('Start journey resolver for card payment', () => {
  beforeAll(() => {
    mockAppTenantConfig.mockReturnValue({
      paymentId: '12345',
      preAuthAmount: 60,
      preAuthCurrency: 'EUR',
      tenantConfig: 'AWDE-De',
      chargeSessionId: 'AWDE-RandomChargeId',
    });
  });
  // As the tenant config is tested in its own test this can be set to anything
  const args = {
    paymentMethodType: 'card',
    appCountry: 'DE',
    appType: 'web',
    userId: 'abc',
  };

  const startJourneyForCardPayment = {
    journeyId: '1342345',
    dropInSession: {
      dropInType: 'MasterCard',
      sessionType: 'CardEntry',
      merchant: 'TESTAPPTEST',
      session: {
        id: 'SESSION0002500420756M9690426M84',
        updateStatus: 'SUCCESS',
        version: 'bb919e2201',
      },
      redirectUrl: '',
    },
  };

  it('should make a start journey request to BPPay', async () => {
    axios.post.mockImplementationOnce(() =>
      Promise.resolve({
        data: { status: 'JourneyStarted', result: startJourneyForCardPayment },
      }),
    );

    const result = await startJourneyFn(ctx, args);
    expect(dynamoClient.put).toHaveBeenCalledWith({
      Item: {
        charge_session_id: 'AWDE-RandomChargeId',
        journey_id: '1342345',
        mem_group: 'a',
        payment_id: '12345',
        order_started: 1613264792000,
        payment_method_type: 'card',
        pre_authorization: { amount: 60, currency: 'EUR', rounded_amount: 60 },
        user_id: 'abc',
        session_id: 'SESSION0002500420756M9690426M84',
        user_type: 'guest',
      },
      TableName: undefined,
    });
    expect(result).toEqual(startJourneyForCardPayment);
  });

  it('should handle error when start Journey fails.', async () => {
    const error = new Error('startJourney error');
    axios.post.mockImplementationOnce(() =>
      Promise.resolve({
        data: { status: 'JourneyFailed', result: startJourneyForCardPayment },
      }),
    );
    try {
      await startJourneyFn(ctx, args);
    } catch (e) {
      expect(e).toEqual(error);
    }
    expect(logger.error).toHaveBeenCalledWith('Status not JourneyStarted', {
      orderId: '12345',
      serviceApi: 'startJourney',
      serviceName: 'DPaaS',
      status: 'JourneyFailed',
    });
  });

  it('should throw an error and swallow the error if storing the journey fails', async () => {
    const err = new Error([
      'startJourney: Journey not started. orderId: 12345',
      'logTraceId: Unspecified',
    ]);
    axios.post.mockResolvedValueOnce({
      data: { status: 'JourneyStarted', result: startJourneyForCardPayment },
    });
    dynamoClient.put = jest.fn(() => ({
      promise: jest.fn(() => {
        return new Promise(function (_, reject) {
          reject(err);
        });
      }),
    }));
    const result = await startJourneyFn(ctx, args);
    expect(logger.error).toHaveBeenCalledWith(
      'storeJourney error: startJourney: Journey not started. orderId: 12345,logTraceId: Unspecified',
    );
    expect(result).toEqual(startJourneyForCardPayment);
  });
});

describe('Start journey resolver for paypal payment', () => {
  mockRetrieveUserTransactions.mockImplementation().mockResolvedValue(null);

  beforeAll(() => {
    mockAppTenantConfig.mockReturnValue({
      paymentId: '12345',
      preAuthAmount: 45,
      preAuthCurrency: 'EUR',
      tenantConfig: 'bppulsenl',
      chargeSessionId: randomIdName,
    });
  });

  const args = {
    paymentMethodType: 'paypal',
    appCountry: 'NL',
    appType: 'mobile',
    userId: 'def',
  };

  const startJourneyForPaypalPayment = {
    journeyId: '1342345',
    dropInSession: {
      dropInType: 'PayPal',
      sessionType: 'BrowserEntry',
      merchant: 'TESTAPPTEST',
      session: { id: 'sessionId' },
      redirectUrl:
        'https://test-gateway.mastercard.com/bpui/pp/out/BP-36cc84b4d125c6adfa2575b574377502',
    },
  };

  it('should make a start journey request to BPPay', async () => {
    axios.post.mockImplementationOnce(() =>
      Promise.resolve({
        data: {
          status: 'JourneyStarted',
          result: startJourneyForPaypalPayment,
        },
      }),
    );

    const result = await startJourneyFn(ctx, args);
    expect(dynamoClient.put).toHaveBeenCalledWith({
      Item: {
        charge_session_id: randomIdName,
        journey_id: '1342345',
        mem_group: 'a',
        payment_id: '12345',
        order_started: 1613264792000,
        payment_method_type: 'paypal',
        pre_authorization: { amount: 45, currency: 'EUR', rounded_amount: 45 },
        session_id: 'sessionId',
        user_id: 'def',
        user_type: 'guest',
      },
      TableName: undefined,
    });
    expect(result).toEqual(startJourneyForPaypalPayment);
  });

  describe('@checkGuestUserPaymentRecords', () => {
    it('should rethrow error if @checkGuestUserPaymentRecords retrieval fails', async () => {
      mockRetrieveUserTransactions
        .mockImplementation()
        .mockRejectedValueOnce(new Error('Failed to fetch user order'));

      await expect(async () => await startJourneyFn(ctx, args)).rejects.toThrow(
        new Error('startJourney: cannot retrieve user order'),
      );
    });

    it('should rethrow error if user already has a record with preauth = true', async () => {
      mockRetrieveUserTransactions.mockResolvedValue([
        {
          payment_id: 'paymentId',
          user_id: 'userId',
          pre_authorization: { amount: 20, rounded_amount: 20 },
          transaction_id: 'transactionId',
          transaction_number: 'transactionNumber',
          final_payment_status: 'finalPaymentStatus',
          final_payment: 'finalPayment',
          payment_method_type: 'paymentMethodType',
          order_started: '1677067654',
          refunded_date: '1677067800',
          card_details: {
            funding_method: 'fundingMethod',
            card_number: 'cardNumber',
            card_scheme: 'cardScheme',
          },
        },
        {
          payment_id: 'paymentId',
          user_id: 'userId',
          pre_authorization: { amount: 20, rounded_amount: 20 },
          transaction_id: 'transactionId',
          transaction_number: 'transactionNumber',
          final_payment_status: 'finalPaymentStatus',
          final_payment: 'finalPayment',
          payment_method_type: 'paymentMethodType',
          order_started: '1677067654',
          refunded_date: '1677067800',
          preauth: true,
          card_details: {
            funding_method: 'fundingMethod',
            card_number: 'cardNumber',
            card_scheme: 'cardScheme',
          },
        },
      ]);

      await expect(async () => await startJourneyFn(ctx, args)).rejects.toThrow(
        new Error('startJourney: guest user exists'),
      );
    });

    it('should rethrow error if user already has a record with charge_status = started', async () => {
      mockRetrieveUserTransactions.mockResolvedValue([
        {
          payment_id: 'paymentId',
          user_id: 'userId',
          pre_authorization: { amount: 20, rounded_amount: 20 },
          transaction_id: 'transactionId',
          transaction_number: 'transactionNumber',
          final_payment_status: 'finalPaymentStatus',
          final_payment: 'finalPayment',
          payment_method_type: 'paymentMethodType',
          order_started: '1677067654',
          refunded_date: '1677067800',
          charge_status: 'started',
          card_details: {
            funding_method: 'fundingMethod',
            card_number: 'cardNumber',
            card_scheme: 'cardScheme',
          },
        },
      ]);

      await expect(async () => await startJourneyFn(ctx, args)).rejects.toThrow(
        new Error('startJourney: guest user exists'),
      );
    });

    it('should not rethrow error if user already has a record but charge_status and preauth are empty', async () => {
      axios.post.mockImplementationOnce(() =>
        Promise.resolve({
          data: {
            status: 'JourneyStarted',
            result: startJourneyForCardPayment,
          },
        }),
      );

      mockRetrieveUserTransactions.mockResolvedValue({
        payment_id: 'paymentId',
        user_id: 'userId',
        pre_authorization: { amount: 20, rounded_amount: 20 },
        transaction_id: 'transactionId',
        transaction_number: 'transactionNumber',
        final_payment_status: 'finalPaymentStatus',
        final_payment: 'finalPayment',
        payment_method_type: 'paymentMethodType',
        order_started: '1677067654',
        refunded_date: '1677067800',
        card_details: {
          funding_method: 'fundingMethod',
          card_number: 'cardNumber',
          card_scheme: 'cardScheme',
        },
      });

      expect(dynamoClient.put).toHaveBeenCalledWith({
        Item: {
          charge_session_id: randomIdName,
          journey_id: '1342345',
          mem_group: 'a',
          payment_id: '12345',
          order_started: 1613264792000,
          payment_method_type: 'paypal',
          pre_authorization: {
            amount: 45,
            currency: 'EUR',
            rounded_amount: 45,
          },
          session_id: 'sessionId',
          user_id: 'def',
          user_type: 'guest',
        },
        TableName: undefined,
      });
    });
  });
});
