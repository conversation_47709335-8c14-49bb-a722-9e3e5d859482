import { PaymentService } from '../../enums';
import appTenantConfig from '../../services/bpPay/appTenantConfig';
import {
  retrieveUserTransactions,
  startJourney,
  storeJourney,
} from '../../services/services';
import { BpPayServerContext } from '../../types/apollo';
import { MutationStartJourneyArgs } from '../../types/graphql';

const serviceName = PaymentService.StartJourneyResolver;

const checkGuestUserPaymentRecords = async (
  ctx: BpPayServerContext,
  userId: string,
) => {
  const orders = await retrieveUserTransactions({
    userId: userId,
  }).catch((err) => {
    ctx.logger.error(`retrieveUserOrder error`, {
      serviceName,
      err,
    });
    throw new Error('startJourney: cannot retrieve user order');
  });

  const preAuth = orders?.some((order) => order?.preauth === true);
  const chargeStarted = orders?.some(
    (order) => order?.charge_status === 'started',
  );

  if (preAuth || chargeStarted) {
    ctx.logger.error('Payment record already exists for guest user', {
      serviceName,
    });
    throw new Error('startJourney: guest user exists');
  }
};

export default async (
  ctx: BpPayServerContext,
  args: MutationStartJourneyArgs,
) => {
  const { userId, appType, appCountry } = args;

  ctx.logger.info('Mutation started', {
    serviceName,
    appType,
    appCountry,
  });

  await checkGuestUserPaymentRecords(ctx, userId ?? '').catch((err: Error) => {
    throw err;
  });

  const {
    paymentId,
    preAuthAmount,
    preAuthCurrency,
    tenantConfig,
    chargeSessionId,
  } = appTenantConfig({ appType: appType ?? '', appCountry: appCountry ?? '' });

  ctx.logger.info('App tenant config', {
    serviceName,
    paymentId,
    preAuthAmount,
    preAuthCurrency,
    tenantConfig,
    chargeSessionId,
  });

  const paymentMethodType = (args && args.paymentMethodType) || 'card';
  const { journeyId, dropInSession } = await startJourney(ctx, {
    paymentId,
    preAuthAmount,
    preAuthCurrency,
    paymentMethodType,
    tenantConfig,
  }).catch((err) => {
    ctx.logger.error('startJourney error', {
      serviceName,
      err,
      paymentId,
    });
    throw new Error('startJourney error');
  });

  // store in our mock dynamodb
  await storeJourney(ctx, {
    paymentId,
    chargeSessionId,
    userId: userId ?? '',
    preAuthAmount,
    roundedAmount: preAuthAmount,
    preAuthCurrency,
    journeyId,
    startDate: Date.now(),
    paymentMethodType,
    sessionId: dropInSession.session.id,
  }).catch((err: any) => {
    ctx.logger.error('storeJourney error', {
      serviceName,
      err,
    });
  });

  ctx.logger.info('Mutation successful', {
    serviceName,
    journeyId,
  });
  // return to user
  return {
    journeyId,
    dropInSession,
  };
};
