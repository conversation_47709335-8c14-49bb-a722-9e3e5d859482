import { retrieveUserIdFromAuthIdInternal } from '../../services/services';
import logger from '../../utils/logger';

export default async (_: any, args: any) => {
  const { authId } = args;

  try {
    const transaction = await retrieveUserIdFromAuthIdInternal({
      authId,
    });

    if (!transaction) {
      logger.error(
        `getUserIdFromAuthIdInternal: Payment data for authId ${authId} not available in store`,
      );
      return {
        message: `Payment data for authId ${authId} not available in store`,
        status: 412,
      };
    }

    return {
      userId: transaction.user_id,
      paymentId: transaction.payment_id,
      finalPaymentStatus: transaction.final_payment_status,
      voidTransaction: transaction.void_transaction,
      message: 'Success',
      status: 200,
    };
  } catch (error) {
    logger.error('getUserIdFromAuthIdInternal: Error retrieving data: ', {
      err: error,
    });
    throw new Error('getUserIdFromAuthIdInternal internal error');
  }
};
