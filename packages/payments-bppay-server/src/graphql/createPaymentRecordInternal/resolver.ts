import { BpPayServerContext } from '../../types/apollo';
import { MutationCreatePaymentRecordInternalArgs } from '../../types/graphql';
import createPaymentRecordInternalFn from './createPaymentRecordInternal';
const createPaymentRecordInternalResolver = {
  Mutation: {
    createPaymentRecordInternal: (
      _: undefined,
      args: MutationCreatePaymentRecordInternalArgs,
      ctx: BpPayServerContext,
    ): Promise<boolean> => createPaymentRecordInternalFn(ctx, args),
  },
};
exports.resolver = createPaymentRecordInternalResolver;
