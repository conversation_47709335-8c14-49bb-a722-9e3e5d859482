import { OrderStatus, PaymentService, PaymentStatus } from '../../enums';
import { storeInternalPaymentRecord } from '../../services/database/store';
import {
  generateReceipt,
  getUserInfo,
  updateHistoryRecord,
} from '../../services/services';
import { BpPayServerContext } from '../../types/apollo';
import { MutationCreatePaymentRecordInternalArgs } from '../../types/graphql';
import logger from '../../utils/logger';
import { requestWithRetry } from '../../utils/retry';

const serviceName = PaymentService.CreatePaymentRecordInternalResolver;

export default async (
  ctx: BpPayServerContext,
  args: MutationCreatePaymentRecordInternalArgs,
): Promise<boolean> => {
  const {
    chargeSessionId,
    amount,
    currency,
    userId,
    paymentId,
    finalPaymentStatus: finalPaymentStatusArg,
    skipStoringPaymentRecord,
    registered,
  } = args;

  const finalPaymentStatus = finalPaymentStatusArg
    ? (finalPaymentStatusArg as PaymentStatus)
    : PaymentStatus.CAPTURED;

  const orderStatus = OrderStatus.INITIALIZED;

  const userInfo = await getUserInfo(userId);

  const storePaymentRecordResult = skipStoringPaymentRecord
    ? { status: 200, message: 'Skipped storing payment record' }
    : await storeInternalPaymentRecord(ctx, {
        chargeSessionId,
        amount: amount ?? 0,
        currency,
        userId,
        paymentId,
        startDate: Date.now(),
        orderStatus,
        finalPaymentStatus,
        merchantId: '',
        registered: registered ?? false,
      });

  try {
    const response = await requestWithRetry(updateHistoryRecord, {
      chargeSessionId,
      paymentId,
      paymentStatus: finalPaymentStatus,
    });
    if (response.status === '200') {
      logger.info(
        `updateExternalPaymentStatus: history record status updated to ${finalPaymentStatus}`,
      );
    }
  } catch (err) {
    logger.error(
      `updateExternalPaymentStatus: UpdateHistoryRecord error. Failed to update internal payment record for payment: ${paymentId}, chargeSessionId: ${chargeSessionId}.`,
      { err },
    );
    throw err;
  }

  if (
    storePaymentRecordResult.status !== 200 &&
    storePaymentRecordResult.status !== 201
  ) {
    throw Error(storePaymentRecordResult.message);
  }

  ctx.logger.info('Mutation Successful. Generating Receipt...', {
    serviceName,
    storePaymentRecordResult,
  });

  await generateReceipt({ userId, userType: userInfo.type, chargeSessionId });

  return true;
};
