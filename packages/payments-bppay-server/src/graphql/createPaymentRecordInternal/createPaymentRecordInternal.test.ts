import { storeInternalPaymentRecord } from '../../services/database/store';
import * as StoreServices from '../../services/database/store';
import { generateReceipt, getUserInfo } from '../../services/services';
import { contextMock } from '../../utils/mock';
import createPaymentRecordInternal from './createPaymentRecordInternal';

jest.mock('../../services/database/store', () => ({
  storeInternalPaymentRecord: jest.fn(),
}));

const mockUpdateHistoryRecord = jest.fn();

const storeInternalPaymentRecordSpy = jest.spyOn(
  StoreServices,
  'storeInternalPaymentRecord',
);
const ctx = contextMock();

jest.mock('../../services/services', () => ({
  generateReceipt: jest.fn(),
  getUserInfo: jest.fn().mockResolvedValue({ type: 'user-type', balance: 100 }),
  updateHistoryRecord: () => mockUpdateHistoryRecord(),
}));

describe('createPaymentRecordInternal()', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });
  beforeAll(() => {
    jest.useFakeTimers('modern');
    jest.setSystemTime(new Date(2024, 0, 1));
  });

  it('should call storeInternalPaymentRecord and generateReceipt with correct arguments', async () => {
    const args = {
      chargeSessionId: 'charge-session-id',
      currency: 'USD',
      userId: 'user-id',
      paymentId: 'payment-id',
      finalPaymentStatus: 'CAPTURED',
      registered: true,
    };

    mockUpdateHistoryRecord.mockResolvedValueOnce({
      status: 200,
      message: '',
    });

    storeInternalPaymentRecordSpy.mockResolvedValueOnce({
      status: 201,
      message: '',
    });

    await createPaymentRecordInternal(ctx, args);

    expect(storeInternalPaymentRecord).toHaveBeenCalledWith(ctx, {
      chargeSessionId: args.chargeSessionId,
      amount: 0,
      currency: args.currency,
      userId: args.userId,
      paymentId: args.paymentId,
      startDate: new Date(2024, 0, 1).getTime(),
      orderStatus: 'initialized',
      finalPaymentStatus: 'CAPTURED',
      merchantId: '',
      registered: true,
    });

    mockUpdateHistoryRecord.mockResolvedValue({
      chargeSessionId: 'charge-session-id',
      paymentId: 'payment-id',
      paymentStatus: 'CAPTURED',
    });

    expect(getUserInfo).toHaveBeenCalledWith(args.userId);
    expect(mockUpdateHistoryRecord).toBeCalled();

    expect(generateReceipt).toHaveBeenCalledWith({
      userId: args.userId,
      userType: 'user-type',
      chargeSessionId: args.chargeSessionId,
    });
  });

  it('should not call generateReceipt if storeInternalPaymentRecord fails', async () => {
    storeInternalPaymentRecordSpy.mockResolvedValueOnce({
      status: 500,
      message: 'Error',
    });

    mockUpdateHistoryRecord.mockResolvedValueOnce({
      status: 500,
      message: 'Error',
    });

    const result = createPaymentRecordInternal(ctx, {} as any);

    await expect(result).rejects.toThrow('Error');
    expect(getUserInfo).toHaveBeenCalled();
    expect(generateReceipt).not.toHaveBeenCalled();
  });

  it('should not call storeInternalPaymentRecord if voidedPreAuth arg is true', async () => {
    const args = {
      chargeSessionId: 'charge-session-id',
      currency: 'USD',
      userId: 'user-id',
      paymentId: 'payment-id',
      finalPaymentStatus: 'CAPTURED',
      skipStoringPaymentRecord: true,
    };

    const result = await createPaymentRecordInternal(ctx, args);

    expect(storeInternalPaymentRecordSpy).not.toHaveBeenCalled();
    expect(getUserInfo).toHaveBeenCalled();
    expect(generateReceipt).toHaveBeenCalled();
    expect(result).toBe(true);
  });
});
