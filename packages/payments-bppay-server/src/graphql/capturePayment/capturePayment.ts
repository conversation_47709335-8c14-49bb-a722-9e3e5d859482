import { v4 as uuidv4 } from 'uuid';

import { OrderStatus, PaymentService, PaymentType } from '../../enums';
import appTenantConfig from '../../services/bpPay/appTenantConfig';
import {
  CapturePaymentInterface,
  CapturePaymentResponse,
} from '../../services/bpPay/interfaces';
import {
  capturePayment,
  generateReceipt,
  getUserInfo,
  storePaymentStatus,
  updateHistoryRecord,
  updateUserInternal,
} from '../../services/services';
import { BpPayServerContext } from '../../types/apollo';
import { MutationCapturePaymentArgs } from '../../types/graphql';
import { formatUTCDate } from '../../utils/date';
import dateNow from '../../utils/dateNow';
import { delay, requestWithRetry } from '../../utils/retry';

const serviceName = PaymentService.CapturePaymentResolver;

export default async (
  ctx: BpPayServerContext,
  args: MutationCapturePaymentArgs,
) => {
  const {
    paymentId,
    amount,
    currency,
    appType,
    appCountry,
    userId,
    userType,
    chargeSessionId,
  } = args;

  const country = appCountry?.toUpperCase();

  const { preAuthAmount, tenantConfig } = appTenantConfig({
    appType: appType ?? undefined,
    appCountry: country ?? undefined,
    paymentType: PaymentType.WALLET,
  });

  const correlationId = uuidv4();
  const operationUId = uuidv4();
  const currentTime = dateNow();

  if (preAuthAmount == null) {
    throw new Error('capturePayment: preAuthAmount is missing');
  }

  if (!userType) {
    throw new Error('capturePayment: userType is missing');
  }

  const captureAmount = amount < preAuthAmount ? amount : preAuthAmount;

  // retry capture if it fails or if the status is processing
  const response: CapturePaymentResponse = await retryCapture(ctx, {
    paymentId,
    operationUId,
    amount: captureAmount,
    currency,
    timestamp: currentTime,
    country,
    tenant: tenantConfig,
    txnReferenceNumber: `000CG${formatUTCDate(dateNow())}`,
    correlationId,
  }).catch((err) => {
    ctx.logger.error(`capturePayment error`, {
      serviceName,
      serviceApiResponseCode: 400,
      paymentId,
      err,
    });
    throw new Error('capturePayment error');
  });

  ctx.logger.info(`capturePayment success`, {
    serviceName,
    response,
    paymentId,
  });

  if (response.status === OrderStatus.CAPTURED) {
    const result = await updatePaymentAndHistoryRecord({
      ctx,
      captureAmount,
      captureResponse: response,
      chargeSessionId,
      currency,
      paymentId,
    });
    if (result.status === 201) {
      ctx.logger.info('Generating a receipt.', {
        serviceName,
        paymentId,
      });
      await generateReceipt({
        userId: userId as string,
        userType,
        chargeSessionId,
      });
      ctx.logger.info('Mutation successful', {
        serviceName,
        serviceApiResponseCode: 200,
        response,
        paymentId,
      });
      return true;
    }
  }

  if (response.status === OrderStatus.OUTSTANDING) {
    await updateUserBalance({
      ctx,
      amount,
      country,
      userId,
      chargeSessionId,
      paymentId,
    });
    await updatePaymentAndHistoryRecord({
      ctx,
      captureAmount,
      captureResponse: response,
      chargeSessionId,
      currency,
      paymentId,
    });
  }

  ctx.logger.info('Mutation failed', {
    serviceName,
    serviceApiResponseCode: 400,
    response,
    paymentId,
  });

  return false;
};

/* ----------------------
--- HELPER FUNCTIONS: ---
-----------------------*/

type UpdateUserBalanceArgs = {
  ctx: BpPayServerContext;
  userId: string;
  amount: number;
  country: string | null | undefined;
  paymentId: string | null | undefined;
  chargeSessionId: string;
};
async function updateUserBalance({
  ctx,
  amount,
  country,
  paymentId,
  chargeSessionId,
  userId,
}: UpdateUserBalanceArgs) {
  // getUserInfo
  const { balance } = await requestWithRetry(getUserInfo, userId).catch(
    (err) => {
      ctx.logger.error(`updateUserBalance: Unable to get the user info.`, {
        err,
        serviceName,
        paymentId,
      });
      return { balance: undefined };
    },
  );
  if (typeof balance !== 'number' || !country) {
    ctx.logger.error(
      `updateUserBalance: Missing user balance or country; will skip updating user balance.`,
      {
        serviceName,
        paymentId,
      },
    );
    return;
  }
  const newUserBalance = balance - amount;
  ctx.logger.info(
    `updateUserBalance: updating user balance from ${balance} to ${newUserBalance} `,
    {
      serviceName,
      paymentId,
    },
  );
  await requestWithRetry(updateUserInternal, {
    userId,
    userBalance: newUserBalance,
    country,
  })
    .then(() =>
      ctx.logger.info(
        `updateUserBalance: user balance updated to ${newUserBalance}`,
        {
          serviceName,
          paymentId,
        },
      ),
    )
    .catch((err: any) => {
      ctx.logger.error(
        `updateUserBalance: Failed to update user balance for chargeSessionId: ${chargeSessionId}.`,
        {
          serviceName,
          err,
          paymentId,
        },
      );
    });
}

type UpdatePaymentAndHistoryRecordArgs = {
  ctx: BpPayServerContext;
  captureAmount: number;
  captureResponse: CapturePaymentResponse;
  chargeSessionId: string;
  currency: string;
  paymentId: string;
};
async function updatePaymentAndHistoryRecord({
  ctx,
  captureAmount,
  captureResponse,
  chargeSessionId,
  currency,
  paymentId,
}: UpdatePaymentAndHistoryRecordArgs): Promise<{ status: number }> {
  // Failure to storePaymentStatus doesn't throw but returns status 500
  // -> making mutation resolve to false
  const storePaymentStatusResult = await storePaymentStatus(ctx, {
    paymentId: paymentId ?? '',
    amount: captureAmount,
    currency: currency ?? '',
    status: captureResponse.status,
  })
    .then((response) => {
      ctx.logger.info(`Stored payment status as: ${captureResponse.status}`, {
        serviceName,
        status: response.status,
        response: response,
        paymentId,
      });
      return response;
    })
    .catch((err) => {
      ctx.logger.error(`storePaymentStatus failed`, {
        serviceName,
        serviceApiResponseCode: 400,
        err,
        paymentId,
      });
      return { status: 500 };
    });

  // Errors are logged and swallowed
  await requestWithRetry(updateHistoryRecord, {
    chargeSessionId,
    paymentId,
    paymentStatus: captureResponse.status,
  })
    .then((response) =>
      ctx.logger.info(
        `history record status updated to ${captureResponse.status} and paymentID updated to ${paymentId}`,
        {
          response,
          serviceName,
          paymentId,
        },
      ),
    )
    .catch((err: any) => {
      ctx.logger.error(
        `updateHistoryRecord failed. Failed to update charge history record for chargeSessionId: ${chargeSessionId}.`,
        {
          serviceName,
          serviceApiResponseCode: 400,
          err,
          paymentId,
        },
      );
    });
  return storePaymentStatusResult;
}

async function retryCapture(
  ctx: BpPayServerContext,
  params: CapturePaymentInterface,
  retries = 2,
  msDelay = 1000,
): Promise<CapturePaymentResponse> {
  try {
    const response: CapturePaymentResponse = await capturePayment(ctx, params);
    // if there are still retries remaining, throw error and call capturePayment again
    // if this is the last attempt, return actual response with status PROCESSING
    if (retries > 1 && response.status === OrderStatus.PROCESSING) {
      throw new Error('capturePayment error: Status processing');
    }
    return response;
  } catch (err) {
    const updatedRetries = retries - 1;
    if (updatedRetries <= 0) {
      throw err;
    }
    ctx.logger.warn('Retrying capturePayment...', {
      err,
      serviceName,
      paymentId: params.paymentId,
    });
    delay(msDelay);
    return retryCapture(ctx, params, updatedRetries, msDelay);
  }
}
