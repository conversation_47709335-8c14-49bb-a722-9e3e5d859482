import { ApolloError } from 'apollo-server-errors';
import { v4 as uuidv4 } from 'uuid';

import {
  OrderStatus,
  PaymentDBStatus,
  PaymentStatus,
  PaymentType,
} from '../../enums';
import appTenantConfig from '../../services/bpPay/appTenantConfig';
import {
  makeWalletMITPayment,
  storePaymentStatus,
  updateHistoryRecord,
} from '../../services/services';
import { BpPayServerContext } from '../../types/apollo';
import { formatUTCDate } from '../../utils/date';
import dateNow from '../../utils/dateNow';
import { sleep } from '../../utils/helper';
import logger from '../../utils/logger';
import { requestWithRetry } from '../../utils/retry';
import {
  IMakeMITPayment,
  IMITResponse,
  InitialUpdateHistoryRecordInput,
  IStoreMITPaymentStatus,
} from './interfaces';

export const validateRequestAmount = (amount: number) => {
  if (amount <= 0) {
    logger.error('makeWalletMITPayment: amount value error');
    throw new ApolloError('Invalid amount value', 'BAD_REQUEST');
  }
};

const isValidFailureStatus = (status: string) => {
  return (
    status === OrderStatus.ERROR ||
    status === OrderStatus.INITIALIZED ||
    status === OrderStatus.AUTHORIZED ||
    status === OrderStatus.REFUNDED ||
    status === OrderStatus.REFUND_FAILED ||
    status === OrderStatus.OUTSTANDING
  );
};

const isProcessingStatus = (status: string) => {
  return status === OrderStatus.PROCESSING;
};

export const makeMITPayment = async (
  ctx: BpPayServerContext,
  params: IMakeMITPayment,
) => {
  const { tenantConfig } = appTenantConfig({
    appCountry: params.country,
    paymentType: PaymentType.SIMPLE_PAYMENT,
  });

  const currentTime = dateNow();
  const makeWalletMITPaymentPayload = {
    correlationId: params.correlationId,
    userId: params.userId,
    paymentId: params.paymentId,
    paymentMethodId: params.paymentMethodId,
    operationId: params.operationId || uuidv4(),
    country: params.country,
    tenant: tenantConfig,
    amount: params.amount,
    currency: params.currency,
    timestamp: currentTime,
    threeDS: params.threeDS,
    txnReferenceNumber: `000CG${formatUTCDate(dateNow())}`,
    logTraceId: params.logTraceId,
  };

  let response = await makeWalletMITPayment(
    ctx,
    makeWalletMITPaymentPayload,
    params.scope,
    params.outstanding,
  ).catch((err) => {
    logger.error(
      `makeWalletMITPayment: makeWalletMITPayment error for ${
        params.outstanding ? 'CIT payment' : 'MIT payment'
      }`,
      { err },
    );
    throw new Error('makeWalletMITPayment error');
  });

  if (response?.status === OrderStatus.PROCESSING) {
    logger.info(
      `makeWalletMITPayment: Status is processing for ${
        params.outstanding ? 'CIT payment' : 'MIT payment'
      }. Retrying in 9 seconds...`,
    );

    await sleep(9000);

    response = await makeWalletMITPayment(
      ctx,
      makeWalletMITPaymentPayload,
      params.scope,
    ).catch((err) => {
      logger.error(
        'makeWalletMITPayment: makeWalletMITPayment error while retrying',
        {
          err,
        },
      );
      throw new Error('makeWalletMITPayment error while retrying');
    });
    if (response?.status === OrderStatus.PROCESSING) {
      logger.info(
        'makeWalletMITPayment: Status is still processing. Continuing...',
      );
    }
  }

  return {
    status: response?.status,
    message: response?.message,
    rrn: response?.operationOutcome?.gateway?.rrn,
    paymentDetails: {
      lastFour: response?.payment?.source?.card?.lastFour,
      cardType: response?.payment?.source?.card?.cardType,
    },
  };
};

export const validateResponseStatus = (
  response: IMITResponse,
): PaymentDBStatus | Error => {
  if (isValidSuccessStatus(response.status)) {
    if (!response?.rrn || response?.rrn === '') {
      logger.info(
        'makeWalletMITPayment: rrn missing from transaction. Allowing the transaction to proceed',
      );
    }
    return PaymentDBStatus.CAPTURED;
  } else if (isValidFailureStatus(response.status)) {
    logger.error(
      `correlationId: ${response.correlationId}, userId: ${response.userId} makeWalletMITPayment: dpaas make walletMITPayment error with response status ${response.status}: ${response?.message}`,
    );
    return PaymentDBStatus.OUTSTANDING;
  } else if (isProcessingStatus(response.status)) {
    logger.info(
      'makeWalletMITPaymentError: processing status received. Continuing...',
    );
    return PaymentDBStatus.PROCESSING;
  } else {
    const err = `dpaas makeWalletMITPayment error with returned unrecognised response status: ${response?.status}`;
    logger.error(err);
    throw err;
  }
};

const isValidSuccessStatus = (status: string) => {
  return (
    status === OrderStatus.CAPTURED ||
    status === OrderStatus.VOIDED ||
    status === OrderStatus.CAPTURED_ELSEWHERE
  );
};

export const storeMITPaymentStatus = async (
  ctx: BpPayServerContext,
  params: IStoreMITPaymentStatus,
) => {
  return await storePaymentStatus(ctx, {
    paymentId: params.paymentId,
    amount: params.amount,
    currency: params.currency,
    status: params.status,
    chargeSessionId: params.chargeSessionId,
    retrievalReferenceNumber: params.retrievalReferenceNumber,
  })
    .then((response) => {
      logger.info(
        `makeWalletMITPayment: payment status stored as ${params.status}`,
      );
      if (response.status !== 201) {
        return { status: 'Failure' };
      }
      return { status: 'Success' };
    })
    .catch((err: any) => {
      logger.error('makeWalletMITPaymentError: storePaymentStatus error', {
        err,
      });
      return { status: 'Failure' };
    });
};

export const initialUpdateHistoryRecord = async ({
  chargeSessionId,
  paymentId,
}: InitialUpdateHistoryRecordInput) => {
  await requestWithRetry(updateHistoryRecord, {
    chargeSessionId,
    paymentId,
    paymentStatus: PaymentStatus.PROCESSING,
  })
    .then(() =>
      logger.info(
        `makeWalletMITPayment: history record status updated initially to ${PaymentStatus.PROCESSING}`,
      ),
    )
    .catch((err: any) => {
      const errorMessage = `makeWalletMITPayment: UpdateHistoryRecord error. Failed to update charge history record to ${PaymentStatus.PROCESSING} status for payment: ${paymentId}, chargeSessionId: ${chargeSessionId}. makeWalletMITPaymentError`;
      logger.error(errorMessage, { err });
      throw new Error(errorMessage);
    });
};
