import { BpPayServerContext } from '../../types';
import { QueryGetTransactionsArgs } from '../../types/graphql';
import getTransactionsFn from './getTransactions';

const getTransactionsResolver = {
  Query: {
    getTransactions: (
      _: undefined,
      args: QueryGetTransactionsArgs,
      ctx: BpPayServerContext,
    ) => getTransactionsFn(ctx, args),
  },
};

exports.resolver = getTransactionsResolver;
