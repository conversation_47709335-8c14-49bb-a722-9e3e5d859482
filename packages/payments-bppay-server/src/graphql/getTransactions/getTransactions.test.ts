import logger from '../../utils/logger';
import { contextMock } from '../../utils/mock';
import getTransactionFn from './getTransactions';
const loggerSpy = jest.spyOn(logger, 'error').mockImplementation(jest.fn());
jest.spyOn(logger, 'info').mockImplementation(jest.fn());

jest.mock('../../env', () => {
  return {
    NODE_ENV: 'development',
  };
});

const ctx = contextMock();

const mockTransactions: any[] = [
  {
    payment_id: 'AMNL-fcdd2517-8f56-4643-b389-61a0e854521',
    preauth: true,
    session_id: '3a66867f-1c37-4b37-b1ec-9b9de8775b4a',
    transaction_id: '0c9aae38-54f7-4f69-9179-cf13a9f417a2',
    transaction_number: '6725556123',
    user_id: 'gab',
  },
];

const mockRetrieveTransactions = jest.fn();
jest.mock('../../services/services', () => ({
  retrieveTransactions: (...args: any[]) => mockRetrieveTransactions(...args),
}));

const paymentStatus = ['Captured'];
const args = {
  startDate: '12/20/2022',
  endDate: '02/02/2023',
  paymentStatus,
};

beforeEach(() => jest.clearAllMocks());

describe('get transactions', () => {
  describe('captured and no refunded payment', () => {
    it('should get transactions', async () => {
      mockRetrieveTransactions.mockResolvedValue(mockTransactions);
      const result = await getTransactionFn(ctx, args);
      expect(result).toStrictEqual(mockTransactions);
    });
    it('should return the transaction with card details if provided', async () => {
      mockTransactions.push({
        payment_id: 'AMNL-fcdd2517-8f56-4643-b389-61a0e854523',
        preauth: true,
        session_id: '3a66867f-1c37-4b37-b1ec-9b9de8775b4c',
        transaction_id: '0c9aae38-54f7-4f69-9179-cf13a9f417a4',
        transaction_number: '6725556126',
        user_id: 'gab',
        card_details: {
          funding_method: 'idk',
          card_number: '3034',
          card_scheme: 'visa',
        },
      });
      mockRetrieveTransactions.mockResolvedValue(mockTransactions);
      const result = await getTransactionFn(ctx, args);
      const returnedObject = {
        payment_id: 'AMNL-fcdd2517-8f56-4643-b389-61a0e854523',
        preauth: true,
        session_id: '3a66867f-1c37-4b37-b1ec-9b9de8775b4c',
        transaction_id: '0c9aae38-54f7-4f69-9179-cf13a9f417a4',
        transaction_number: '6725556126',
        user_id: 'gab',
        funding_method: 'idk',
        card_number: '3034',
        card_scheme: 'visa',
      };
      expect(result[1]).toStrictEqual(returnedObject);
    });
  });
  describe('error scenario', () => {
    it('should throw an error if no transactions found', async () => {
      mockRetrieveTransactions.mockResolvedValue(undefined);
      await expect(() => getTransactionFn(ctx, args)).rejects.toThrow(
        'getTransactions error',
      );

      expect(loggerSpy).toHaveBeenCalledTimes(2);
    });
    it('should throw an error if retrieveTransactions fails', async () => {
      mockRetrieveTransactions.mockRejectedValue('error');
      await expect(() => getTransactionFn(ctx, args)).rejects.toThrow(
        'getTransactions error',
      );
      expect(loggerSpy).toHaveBeenCalledTimes(1);
    });
  });
});
