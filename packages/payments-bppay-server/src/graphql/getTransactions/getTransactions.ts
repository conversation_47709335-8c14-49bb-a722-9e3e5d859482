import { DateType } from '../../enums';
import ENV from '../../env';
import { TransactionI } from '../../services/database/interfaces';
import { filterTransactionsByOrderStarted } from '../../services/database/utils';
import { retrieveTransactions } from '../../services/services';
import { BpPayServerContext } from '../../types';
import { QueryGetTransactionsArgs, Transaction } from '../../types/graphql';
const goLiveDate = ENV.GO_LIVE_DATE
  ? parseInt(ENV.GO_LIVE_DATE)
  : new Date().getTime();

export default async (
  ctx: BpPayServerContext,
  args: QueryGetTransactionsArgs,
): Promise<Transaction[]> => {
  try {
    ctx.logger.info('Getting transactions with args: ', args);

    const transactions = await retrieveTransactions(args);
    let filteredTransactions = transactions;

    if (args.dateType === DateType.SALES_POSTING_MISSING) {
      filteredTransactions = await filterTransactionsByOrderStarted(
        transactions as TransactionI[],
        goLiveDate,
      );
    }

    if (ENV.APP_ENV === 'development') {
      console.log(`All transactions: ${transactions}`);
      console.log(`filtered transactions: ${filteredTransactions}`);
    }

    if (!filteredTransactions) {
      ctx.logger.error(
        'getTransactions: No transactions found with given parameters',
      );
      throw new Error('getTransactions: No transactions found');
    }

    return filteredTransactions.map((transaction: any) => {
      if (transaction.card_details) {
        //eslint-disable-next-line
        const { funding_method, card_number, card_scheme } =
          transaction.card_details;
        delete transaction.card_details;
        return { ...transaction, funding_method, card_number, card_scheme };
      } else {
        return transaction;
      }
    });
  } catch (error) {
    ctx.logger.error('getTransactions: Error retrieving transactions: ', {
      err: error,
      ...args,
    });
    throw new Error('getTransactions error');
  }
};
