import { allow, deny, or, shield } from 'graphql-shield';

import {
  isAuthorised,
  isGuestAuthorised,
  isInternal,
  isUserRolePermitted,
} from './rules';

const permissions = shield(
  {
    Query: {
      '*': deny,
      _service: allow,
      getPaymentRecord: isInternal,
      getTransactionByUserAndSession: isInternal,
      getTransactions: isInternal,
      getUserPaymentDetails: isGuestAuthorised,
      getUserIdFromAuthIdInternal: isInternal,
      getActivePreAuth: isAuthorised,
    },
    Mutation: {
      '*': deny,
      authoriseWalletPayment: isAuthorised,
      capturePayment: isAuthorised,
      getToken3DS: isAuthorised,
      authLookup3DS: isAuthorised,
      authenticate3DS: isAuthorised,
      createOrder: or(isAuthorised, isUserRolePermitted),
      makeSimplePayment: or(isAuthorised, isUserRolePermitted),
      preAuth: allow,
      refundOrder: isInternal,
      registeredPreAuth: isAuthorised,
      startJourney: isGuestAuthorised,
      voidOrder: isInternal,
      saveTransactionIdInternal: isInternal,
      createPaymentRecordInternal: isInternal,
      storeOperationId: isInternal,
      updateSalesPostingDate: isInternal,
    },
  },
  {
    allowExternalErrors: true,
  },
);

export default permissions;
