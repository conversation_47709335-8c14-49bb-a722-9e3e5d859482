import 'dotenv/config';

import variables from './variables.json';

const missingVars = variables.filter(
  (variable) => process.env[variable.name] === undefined,
);

if (missingVars.length > 0) {
  console.log('Aborting environment variables missing: ', missingVars);
  if (typeof jest === 'undefined') {
    process.exit();
  }
}

const {
  NODE_ENV,
  APP_ENV,
  PREAUTH_AMOUNT_DE,
  PREAUTH_AMOUNT_UK,
  PREAUTH_AMOUNT_NL,
  PREAUTH_AMOUNT_US,
  REGISTERED_PREAUTH_AMOUNT_UK,
  REGISTERED_PREAUTH_AMOUNT_NL,
  REGISTERED_PREAUTH_AMOUNT_ES,
  REGISTERED_PREAUTH_AMOUNT_DE,
  GO_LIVE_DATE,
  PAYMENT_DB_USER_TYPE_DEPLOYMENT_DATE,
  BP_PAY_ENDPOINT,
  SIGNED_REQUEST_LAMBDA_NAME,
  PAYMENTS_DB_TABLE_NAME,
  HISTORY_DB_TABLE_NAME,
  DYNAMO_DB_REGION,
  DYNAMO_DB_ACCESS_KEY_ID,
  DYNAMO_DB_ACCESS_KEY,
  DYNAMO_DB_URL,
  APOLLO_INTERNAL_USER_ID,
  APOLLO_INTERNAL_SECRET,
  CARDINAL_ENDPOINT,
  DPAAS_PAY_ENDPOINT,
  ELASTICACHE_HOST,
  ELASTICACHE_PORT,
  DPAAS_AZURE_AD_TOKEN_URL,
  DPAAS_AZURE_AD_CLIENT_SECRET,
  DPAAS_AZURE_AD_CLIENT_ID,
  DPAAS_WALLET_SCOPE,
  DPAAS_PAY_SCOPE,
  DPAAS_3DS_SCOPE,
  PRIVATE_GATEWAY_CLUSTER_URL,
  PROCESSING_PAYMENT_FEATURE_FLAG,
  MOCK_DPAAS_PAY_ENDPOINT,
  AMDE_MERCHANT_ID,
  AMNL_MERCHANT_ID,
  AMUS_MERCHANT_ID,
  AMES_MERCHANT_ID,
  IDP_PULSE_ISSUER,
  IDP_ARAL_ISSUER,
  CIP_TOKEN_ISSUER,
  ANON_TOKEN_ISSUER,
  AMUK_MERCHANT_ID,
  DPAAS_TENANT_ENV,
  DATE_TYPE,
  PAYMENT_REFERENCE_NUMBER,
  OPEN_ID_ACCESS_ROLE,
} = process.env;

export default {
  NODE_ENV,
  APP_ENV,
  PREAUTH_AMOUNT_DE,
  PREAUTH_AMOUNT_UK,
  PREAUTH_AMOUNT_NL,
  PREAUTH_AMOUNT_US,
  REGISTERED_PREAUTH_AMOUNT_UK,
  REGISTERED_PREAUTH_AMOUNT_NL,
  REGISTERED_PREAUTH_AMOUNT_ES,
  REGISTERED_PREAUTH_AMOUNT_DE,
  GO_LIVE_DATE,
  PAYMENT_DB_USER_TYPE_DEPLOYMENT_DATE,
  BP_PAY_ENDPOINT,
  SIGNED_REQUEST_LAMBDA_NAME,
  PAYMENTS_DB_TABLE_NAME,
  HISTORY_DB_TABLE_NAME,
  DYNAMO_DB_REGION,
  DYNAMO_DB_ACCESS_KEY_ID,
  DYNAMO_DB_ACCESS_KEY,
  DYNAMO_DB_URL,
  APOLLO_INTERNAL_USER_ID,
  APOLLO_INTERNAL_SECRET,
  CARDINAL_ENDPOINT,
  DPAAS_PAY_ENDPOINT,
  ELASTICACHE_HOST,
  ELASTICACHE_PORT,
  DPAAS_AZURE_AD_TOKEN_URL,
  DPAAS_AZURE_AD_CLIENT_SECRET,
  DPAAS_AZURE_AD_CLIENT_ID,
  DPAAS_WALLET_SCOPE,
  DPAAS_PAY_SCOPE,
  DPAAS_3DS_SCOPE,
  PRIVATE_GATEWAY_CLUSTER_URL,
  PROCESSING_PAYMENT_FEATURE_FLAG,
  MOCK_DPAAS_PAY_ENDPOINT,
  AMDE_MERCHANT_ID,
  AMNL_MERCHANT_ID,
  AMUS_MERCHANT_ID,
  AMES_MERCHANT_ID,
  IDP_PULSE_ISSUER,
  IDP_ARAL_ISSUER,
  CIP_TOKEN_ISSUER,
  ANON_TOKEN_ISSUER,
  AMUK_MERCHANT_ID,
  DPAAS_TENANT_ENV,
  DATE_TYPE,
  PAYMENT_REFERENCE_NUMBER,
  OPEN_ID_ACCESS_ROLE,
};
