import axios from 'axios';

import logger from '../../utils/logger';
import payments from './payments';
import { UserTypeDynamo } from '../../enums';

const dynamoClient = require('../../utils/dynamoClient').default;

jest.mock('axios');
jest.mock('@bp/pulse-common', () => ({
  getDPaaSAuthToken: jest.fn().mockReturnValue(Promise.resolve('12345')),
}));
jest.mock('../../env', () => {
  return {
    NODE_ENV: 'development',
    PAYMENTS_DB_TABLE_NAME: 'paymentsTable',
  };
});
const mockGetUserPaymentDetails = jest.fn();
const {
  makeGuestPayment,
  paymentData,
  paymentCallbackStatus,
  userData,
  voidTransaction,
  storeVoidTransaction,
  storeChargeStatus,
  storeTransactionId,
  getSessionId,
  registeredPaymentData,
} = payments;
jest.mock(
  '../../components/getUserPaymentDetails/getUserPaymentDetails',
  () => ({
    __esModule: true,
    default: (...args) => mockGetUserPaymentDetails(...args),
  }),
);
const mockRequestBpPay = jest.fn();
jest.mock('../../services/bpPay/request.ts', () => ({
  requestBpPay: (...args) => mockRequestBpPay(...args),
}));
// remove the following lines to see debug logs
jest.spyOn(logger, 'error').mockImplementation(jest.fn());
jest.spyOn(logger, 'info').mockImplementation(jest.fn());
jest.spyOn(global.console, 'log').mockImplementation(jest.fn());

const dynamoUpdateMockStoreTransactionId = (updateAttributes) =>
  jest.fn(() => ({
    promise: jest.fn(() => {
      return new Promise(function (resolve) {
        resolve(updateAttributes);
      });
    }),
  }));

const dynamoReturnGetItem = (order) =>
  jest.fn(() => ({
    promise: jest.fn(() => {
      return new Promise(function (resolve) {
        resolve({
          Item: order,
        });
      });
    }),
  }));

const dynamoReturnOrder = (order) =>
  jest.fn(() => ({
    promise: jest.fn(() => {
      return new Promise(function (resolve) {
        resolve({
          Items: [order],
        });
      });
    }),
  }));

const mockDynamoFn = jest.fn(() => ({
  promise: jest.fn(() => {
    return new Promise(function (resolve) {
      resolve({});
    });
  }),
}));

dynamoClient.put = mockDynamoFn;
dynamoClient.transactWrite = mockDynamoFn;

const mockAttributes = {
  Attributes: {
    transaction_id: 'test',
    user_id: 'user test',
  },
};
const mockOrder = {
  payment_id: 'orderId12345',
  user_id: '1234',
  pre_authorization: { amount: 20 },
  user_type: UserTypeDynamo.REGISTERED_USER,
};

const returnedErrorMessageWhenNoOrder = 'No journey id for user in store';

const mockRequest = {
  body: {
    userId: '123',
    amount: 12,
    currency: 'EUR',
  },
};

const testBody = {
  transactionId: '123',
  transactionNumber: '456',
  userId: '1234',
  chargeSessionId: 'charge1234',
};

const mockReq = {
  params: {
    transactionId: '12345',
  },
};

const res = {
  status: jest.fn(() => res),
  json: jest.fn(),
};

describe('Make payments endpoint', () => {
  const loggedErrorMessageWhenNoOrder =
    'makeGuestPayment: retrieveUserOrder : order journey_id id is missing';
  const mockOrder = { journey_id: 'superJoruney', payment_id: 'superOrder' };
  it('should make a payment request to BPPay', async () => {
    dynamoClient.query = dynamoReturnOrder(mockOrder);
    mockRequestBpPay.mockResolvedValueOnce({ data: { status: 'Success' } });
    const req = mockRequest;

    await makeGuestPayment(req, res);

    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      message: 'ok',
    });
    expect(mockRequestBpPay).toHaveBeenCalledTimes(1);
    expect(mockRequestBpPay).toHaveBeenCalledWith('/async/capture', {
      eventCallback: { customer: '123' },
      journeyId: 'superJoruney',
      captureAmount: { amount: 12, currency: 'EUR' },
    });
  });

  it('should return false when the call to bppay is unsuccessful', async () => {
    const req = mockRequest;

    dynamoClient.query = dynamoReturnOrder(mockOrder);
    dynamoClient.update = dynamoUpdateMockStoreTransactionId(mockAttributes);
    mockRequestBpPay.mockRejectedValueOnce(new Error('network error'));
    await makeGuestPayment(req, res);
    expect(res.json).toHaveBeenCalledWith({
      message: 'could not initiate payment',
    });
    expect(dynamoClient.update).toHaveBeenCalledWith({
      ExpressionAttributeValues: {
        ':c': undefined,
        ':p': { amount: 0, currency: 'EUR' },
        ':s': 'Outstanding',
      },
      Key: { payment_id: 'superOrder' },
      ReturnValues: 'UPDATED_NEW',
      TableName: 'paymentsTable',
      UpdateExpression: 'set final_payment = :p, final_payment_status = :s',
    });
  });

  it('should throw an error if user id does not exist', async () => {
    const req = mockRequest;

    dynamoClient.query = dynamoReturnOrder({});
    await makeGuestPayment(req, res);
    expect(logger.error).toHaveBeenLastCalledWith(
      loggedErrorMessageWhenNoOrder,
      {
        order: {},
        journey_id: undefined,
      },
    );
    expect(res.json).toHaveBeenCalledWith({
      message: returnedErrorMessageWhenNoOrder,
    });
    expect(res.status).toHaveBeenCalledWith(412);
  });

  it('should throw an error if order is undefined', async () => {
    const req = mockRequest;

    dynamoClient.query = dynamoReturnOrder();

    await makeGuestPayment(req, res);
    expect(logger.error).toHaveBeenLastCalledWith(
      loggedErrorMessageWhenNoOrder,
      {
        order: undefined,
        journey_id: undefined,
      },
    );
    expect(res.json).toHaveBeenCalledWith({
      message: returnedErrorMessageWhenNoOrder,
    });
    expect(res.status).toHaveBeenCalledWith(412);
  });
});

describe('Get payment data by transaction id endpoint', () => {
  const transactionNotFoundSuccessfullyErrorMessage =
    'Payment data for transaction 12345 not available in store';
  it('should fetch order data from dynamodb', async () => {
    const req = mockReq;

    dynamoClient.query = dynamoReturnOrder(mockOrder);

    await paymentData(req, res);
    expect(res.json).toHaveBeenCalledWith({
      paymentId: 'orderId12345',
      preAuthAmount: 20,
      userId: '1234',
      message: 'ok',
      registered: true,
    });
    expect(res.status).toHaveBeenCalledWith(200);
  });

  it('should return data even if the preauth amount is 0', async () => {
    const req = mockReq;

    mockOrder.pre_authorization.amount = 0;
    dynamoClient.query = dynamoReturnOrder(mockOrder);

    await paymentData(req, res);
    expect(res.json).toHaveBeenCalledWith({
      paymentId: 'orderId12345',
      preAuthAmount: 0,
      userId: '1234',
      message: 'ok',
      registered: true,
    });
    expect(res.status).toHaveBeenCalledWith(200);
  });

  it('should print an error if the paymentData cannot be found', async () => {
    const req = mockReq;

    dynamoClient.query = dynamoReturnOrder();

    await paymentData(req, res);
    expect(res.json).toHaveBeenCalledWith({
      message: transactionNotFoundSuccessfullyErrorMessage,
    });
    expect(res.status).toHaveBeenCalledWith(412);
  });

  it('should print an error if the paymentData is incomplete', async () => {
    const req = mockReq;

    const mockOrder = {
      payment_id: undefined,
      pre_authorization: {
        amount: 60,
      },
      user_id: '1234',
      message: 'ok',
    };
    dynamoClient.query = dynamoReturnOrder(mockOrder);

    await paymentData(req, res);
    expect(res.json).toHaveBeenCalledWith({
      message: transactionNotFoundSuccessfullyErrorMessage,
    });
    expect(res.status).toHaveBeenCalledWith(412);
  });
});

describe('Update payment status endpoint', () => {
  const req = {
    body: {
      userId: '123',
      currency: 'EUR',
      amount: 10,
      status: 'Captured',
      cardData: {
        cardNumber: 'cardNumber',
        fundingMethod: 'fundingMethod',
        cardScheme: 'cardScheme',
      },
    },
  };
  it('should save the payment status in dynamo', async () => {
    const mockOrder = {
      payment_id: 'orderId12345',
      journey_id: 'superJoruney',
    };
    dynamoClient.query = dynamoReturnOrder(mockOrder);
    dynamoClient.put();
    dynamoClient.update = dynamoUpdateMockStoreTransactionId({});
    await paymentCallbackStatus(req, res);
    expect(res.json).toHaveBeenCalledWith({
      message: 'ok',
      paymentId: 'orderId12345',
      chargeSessionId: undefined,
    });
  });

  it('should use the paymentId if it is provided in the rest call body', async () => {
    const registeredReq = {
      body: {
        ...req.body,
        userType: 'Registered',
        paymentId: 'paymentId',
        status: 'Outstanding',
      },
    };
    // the mocking below would cause an error in the guest use case
    dynamoClient.get = dynamoReturnGetItem({
      charge_session_id: 'chargeSessionId',
    });
    await paymentCallbackStatus(registeredReq, res);
    expect(res.json).toHaveBeenCalledWith({
      message: 'ok',
      paymentId: 'paymentId',
      chargeSessionId: 'chargeSessionId',
    });
    expect(dynamoClient.get).toHaveBeenCalledWith({
      Key: { payment_id: 'paymentId' },
      TableName: 'paymentsTable',
    });
  });

  it('should return an error message if there is no order record in the default flow', async () => {
    dynamoClient.query = dynamoReturnOrder();
    await paymentCallbackStatus(req, res);
    expect(logger.error).toHaveBeenLastCalledWith(
      'paymentCallbackStatus: retrieveUserOrder: missing order data',
      {
        order: undefined,
        journey_id: undefined,
        payment_id: undefined,
        userType: undefined,
      },
    );
    expect(res.json).toHaveBeenCalledWith({
      message: 'No journey id for user in store',
    });
    expect(res.status).toHaveBeenCalledWith(412);
  });
});

describe('User data endpoint', () => {
  const req = {
    headers: { 'x-log-trace-id': '123' },
    params: { userId: 'user12345' },
  };
  const res = {
    status: jest.fn(() => res),
    json: jest.fn(),
  };
  it('should return the correct status and payload from the getUserPaymentDetails component', async () => {
    const messageObject = {
      status: 412,
      message: 'Payment data for transaction not available in store',
    };
    mockGetUserPaymentDetails.mockResolvedValue(messageObject);
    await userData(req, res);
    expect(res.json).toHaveBeenLastCalledWith(messageObject);
    expect(res.status).toHaveBeenLastCalledWith(412);
  });

  it('should return a 500 status code if the getUserPaymentDetails call fails', async () => {
    mockGetUserPaymentDetails.mockRejectedValue(new Error('dynamo error'));
    await userData(req, res);
    expect(res.json).toHaveBeenLastCalledWith({
      message: 'could not retrieve data',
    });
    expect(res.status).toHaveBeenLastCalledWith(500);
  });

  it('should pass the correct logTraceId to the functionality', async () => {
    await userData(req, res);

    const [ctxArg, userIdArg] = mockGetUserPaymentDetails.mock.calls[0];

    expect(userIdArg).toBe('user12345');
    expect(ctxArg.logTraceId).toBe('123');
  });
});

describe('Void transaction endpoint', () => {
  it('should void the transaction and update in dynamo', async () => {
    const req = {
      body: {
        transactions: [
          {
            paymentId: '1509',
            journeyId: '34567',
          },
        ],
      },
    };

    axios.post.mockImplementationOnce(() =>
      Promise.resolve({ data: { status: 'Voided' } }),
    );

    dynamoClient.transactWrite();

    await voidTransaction(req, res);
    expect(res.json).toHaveBeenCalledWith({
      message: 'Transactions voided successfully.',
    });
  });
});

describe('Store void transaction endpoint', () => {
  it('should update in dynamo', async () => {
    const req = {
      body: ['123', '456'],
    };

    axios.post.mockImplementationOnce(() =>
      Promise.resolve({ data: { status: 'Voided' } }),
    );

    dynamoClient.transactWrite();

    await storeVoidTransaction(req, res);
    expect(res.json).toHaveBeenCalledWith({
      message: 'Transactions void status stored successfully.',
    });
  });
});

describe('Store charge status endpoint', () => {
  it('should update in dynamo', async () => {
    const req = {
      body: testBody,
    };

    dynamoClient.query = dynamoReturnOrder({
      payment_id: '1234',
    });
    dynamoClient.update = dynamoUpdateMockStoreTransactionId({});
    await storeChargeStatus(req, res);
    expect(res.json).toHaveBeenCalledWith({
      message: 'ok',
    });
  });

  it('should return an error message if cannot retrieve order', async () => {
    const req = {
      body: testBody,
    };

    dynamoClient.query = dynamoReturnOrder();
    await storeChargeStatus(req, res);
    expect(logger.error).toHaveBeenLastCalledWith(
      'storeChargeStatus: order details missing',
      { order: undefined, paymentId: undefined },
    );
    expect(res.json).toHaveBeenCalledWith({
      message: returnedErrorMessageWhenNoOrder,
    });
    expect(res.status).toHaveBeenCalledWith(412);
  });
});

describe('Store Transaction Id endpoint', () => {
  dynamoClient.update = dynamoUpdateMockStoreTransactionId(mockAttributes);
  it('should store transaction id if it receives a paymentId', async () => {
    testBody.paymentId = 'test';
    const req = {
      body: testBody,
    };

    await storeTransactionId(req, res);
    expect(res.json).toHaveBeenCalledWith({
      message: 'ok',
    });
  });

  it('should store transaction id if it receives a paymentId and registered property', async () => {
    testBody.paymentId = 'test';
    testBody.registered = true;
    const req = {
      body: testBody,
    };

    await storeTransactionId(req, res);
    expect(res.json).toHaveBeenCalledWith({
      message: 'ok',
    });
  });

  it('should generate new paymentId and store transaction id if no paymentId is received', async () => {
    const req = {
      body: {
        transactionId: '123',
        transactionNumber: '456',
        userId: '1234',
      },
    };

    dynamoClient.put();
    await storeTransactionId(req, res);
    expect(res.json).toHaveBeenCalledWith({
      message: 'ok',
    });
  });

  it('should generate new paymentId and log error if storeJourney call fails when no paymentId is passed', async () => {
    jest.clearAllMocks();
    const req = {
      body: {
        transactionId: '123',
        transactionNumber: '456',
        userId: '1234',
      },
    };
    const res = { status: jest.fn(() => res), json: jest.fn() };
    dynamoClient.put = jest.fn(() => ({
      promise: jest.fn(() => {
        return new Promise(function (_, reject) {
          reject({});
        });
      }),
    }));
    dynamoClient.update = dynamoUpdateMockStoreTransactionId(mockAttributes);
    await storeTransactionId(req, res);
    expect(logger.error).toHaveBeenCalledTimes(2);
    expect(res.json).toHaveBeenCalledWith({
      message: 'ok',
    });
  });
});

describe('getSessionId', () => {
  it('should return the sessionId', async () => {
    jest.clearAllMocks();
    const req = {
      params: {
        userId: '1234',
      },
    };

    dynamoClient.query = dynamoReturnOrder({
      session_id: '12345',
    });
    await getSessionId(req, res);
    expect(res.json).toHaveBeenCalledWith({
      sessionId: '12345',
      message: 'ok',
    });
  });

  it('should return an error message if the session id cannot be retrieved', async () => {
    jest.clearAllMocks();
    const req = {
      params: {
        userId: '1234',
      },
    };

    dynamoClient.query = dynamoReturnOrder();
    await getSessionId(req, res);
    expect(logger.error).toHaveBeenCalledWith(
      'getSessionId: order id not available: ',
      { order: undefined, sessionId: undefined },
    );
    expect(res.json).toHaveBeenCalledWith({
      message: 'Get session Id for user Id not available in store',
    });
    expect(res.status).toHaveBeenCalledWith(412);
  });
});

describe('registeredPaymentData', () => {
  it('should return the payment data for a registered user', async () => {
    const req = mockReq;

    const mockOrder = {
      payment_id: 'orderId12345',
      user_id: '1234',
      pre_authorization: { amount: 20 },
      charge_session_id: 'chargeSessionId',
      final_payment_status: 'captured',
      final_payment: {
        amount: 20,
        currency: 'EUR',
      },
      payment_method_type: 'wallet',
      transaction_number: 'transactionNumber',
    };
    dynamoClient.query = dynamoReturnOrder(mockOrder);

    await registeredPaymentData(req, res);
    expect(res.json).toHaveBeenCalledWith({
      chargeSessionId: 'chargeSessionId',
      finalPaymentStatus: 'captured',
      finalPayment: {
        amount: 20,
        currency: 'EUR',
      },
      paymentId: 'orderId12345',
      paymentMethodType: 'wallet',
      transactionNumber: 'transactionNumber',
      userId: '1234',
      message: 'ok',
    });
    expect(res.status).toHaveBeenCalledWith(200);
  });

  it('should print an error if the paymentData cannot be found', async () => {
    const req = mockReq;

    dynamoClient.query = dynamoReturnOrder();

    await registeredPaymentData(req, res);
    expect(res.json).toHaveBeenCalledWith({
      message: 'Registered payment data for transaction not available in store',
    });
    expect(res.status).toHaveBeenCalledWith(412);
    expect(logger.error).toHaveBeenLastCalledWith(
      'registeredPaymentData: order not available: ',
      {
        order: undefined,
        payment_id: undefined,
        user_id: undefined,
      },
    );
  });
});
