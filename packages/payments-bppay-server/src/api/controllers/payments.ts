import pMap from 'p-map';
import { v4 as uuidv4 } from 'uuid';

import { maxVoidedTransactions } from '../../common/constants';
import getRegisteredUserPaymentDetails from '../../components/getRegisteredUserPaymentDetails/getRegisteredUserPaymentDetails';
import getUserPaymentDetails from '../../components/getUserPaymentDetails/getUserPaymentDetails';
import { UserType, UserTypeDynamo } from '../../enums';
import { StoreVoidTransactionI } from '../../services/database/interfaces';
import { storeJourney } from '../../services/database/store';
import {
  makePayment,
  retrieveOrder,
  retrieveTransactionOrder,
  retrieveTransactions,
  retrieveUserOrder,
  setVoidTransaction,
  storeCardDetails,
  storeChargeStatus as storeChargeStatusService,
  storePaymentStatus,
  storeTransactionID,
  storeVoidTransaction as storeVoidTransactionService,
} from '../../services/services';
import { ChargeStatus } from '../../types';
import { BpPayServerContext } from '../../types/apollo';
import { contextBuilder } from '../../utils/apollo';

const ERROR_MESSAGE_NO_DATA = 'could not retrieve data';
const ERROR_MESSAGE_NO_JOURNEY_ID = 'No journey id for user in store';

const getTransactions = async (req: any, res: any) => {
  const ctx = await contextBuilder({ req });
  ctx.logger.info('getTransactions request: ', req.body);

  const {
    date1,
    date2,
    exclusiveStartKeyRecord,
    paymentStatus,
    transactionType,
  } = req.body;

  if (!date1 || !date2) {
    ctx.logger.info('Date parameters required', { date1, date2 });
    return res.status(500).json({
      message: 'Date parameters required',
    });
  }
  const isoDateToUnix = (iso: string) => new Date(iso).getTime();

  const params = {
    startDate: isoDateToUnix(date1),
    endDate: isoDateToUnix(date2),
    ...(paymentStatus && { paymentStatus }),
    ...(exclusiveStartKeyRecord && {
      exclusiveStartKey: exclusiveStartKeyRecord,
    }),
    transactionType,
  };
  const transactions = await retrieveTransactions(params).catch((e) => {
    ctx.logger.error('Error retrieving transactions: ', {
      err: e,
      date1,
      date2,
    });
    return res.status(500).json({
      message: 'Error retrieving transactions',
    });
  });

  return res.status(200).json({
    message: 'ok',
    transactions,
  });
};

const getSessionId = async (req: any, res: any) => {
  const ctx = await contextBuilder({ req });

  ctx.logger.info(
    'getSessionId: Get Session ID request received: ',
    req.params,
  );
  const order = await retrieveUserOrder(ctx, {
    userId: req.params.userId,
  }).catch((e) => {
    ctx.logger.error(
      `getSessionId error: retrieveUserOrder : ${(e as Error).message}`,
    );
  });

  ctx.logger.info('getSessionId: order: ', { order });

  if (!order || !order.session_id) {
    ctx.logger.error('getSessionId: order id not available: ', {
      order,
      sessionId: order?.session_id,
    });
    return res.status(412).json({
      message: 'Get session Id for user Id not available in store',
    });
  }

  const { session_id: sessionId } = order;

  if (sessionId) {
    return res.status(200).json({
      sessionId,
      message: 'ok',
    });
  }

  // This is unreachable code
  ctx.logger.error('getSessionId: order session_id not available', { order });
  return res.status(500).json({
    message: ERROR_MESSAGE_NO_DATA,
  });
};

const paymentData = async (req: any, res: any) => {
  const ctx = await contextBuilder({ req });

  const order = await retrieveTransactionOrder({
    transactionId: req.params.transactionId,
  }).catch((e) => {
    ctx.logger.error(
      `paymentData error: retrieveTransactionOrder : ${(e as Error).message}`,
    );
  });
  if (!order || !order.payment_id || !order.user_id) {
    return res.status(412).json({
      message: `Payment data for transaction ${req.params.transactionId} not available in store`,
    });
  }

  ctx.logger.info('paymentData: order: ', { order });

  const {
    payment_id: paymentId,
    user_id: userId,
    pre_authorization: preAuth,
    transaction_number: transactionNumber,
    final_payment_status: finalPaymentStatus,
    final_payment: finalPayment,
    payment_method_type: paymentMethodType,
    journey_id: journeyId,
    session_id: sessionId,
    charge_session_id: chargeSessionId,
    user_type: userType,
  } = order;

  const preAuthAmount = preAuth?.amount;
  const roundedAmount = preAuthAmount?.roundedAmount;
  const registered = userType === UserTypeDynamo.REGISTERED_USER;

  if (paymentId && userId) {
    return res.status(200).json({
      paymentId,
      chargeSessionId,
      userId,
      preAuthAmount,
      roundedAmount,
      transactionNumber,
      finalPaymentStatus,
      finalPayment,
      paymentMethodType,
      journeyId,
      registered,
      sessionId,
      message: 'ok',
    });
  }
  ctx.logger.info('paymentData: order has invalid data:', { order });
  return res.status(500).json({
    message: ERROR_MESSAGE_NO_DATA,
  });
};

const registeredPaymentData = async (req: any, res: any) => {
  const ctx = await contextBuilder({ req });

  ctx.logger.info('registeredPaymentData: request received: ', req.params);
  const order = await retrieveTransactionOrder({
    transactionId: req.params.transactionId,
  }).catch((e) => {
    ctx.logger.error(
      `registeredPaymentData error: retrieveTransactionOrder : ${
        (e as Error).message
      }`,
    );
  });
  ctx.logger.info('registeredPaymentData order: ', { order });
  if (!order || !order.payment_id || !order.user_id) {
    ctx.logger.error('registeredPaymentData: order not available: ', {
      order,
      payment_id: order?.payment_id,
      user_id: order?.user_id,
    });
    return res.status(412).json({
      message: 'Registered payment data for transaction not available in store',
    });
  }

  const {
    payment_id: paymentId,
    user_id: userId,
    transaction_number: transactionNumber,
    final_payment_status: finalPaymentStatus,
    final_payment: finalPayment,
    payment_method_type: paymentMethodType,
    charge_session_id: chargeSessionId,
  } = order;

  if (paymentId && userId) {
    return res.status(200).json({
      chargeSessionId,
      paymentId,
      userId,
      transactionNumber,
      finalPaymentStatus,
      finalPayment,
      paymentMethodType,
      message: 'ok',
    });
  }
  ctx.logger.error('registeredPaymentData: invalid order data:', { order });
  return res.status(500).json({
    message: ERROR_MESSAGE_NO_DATA,
  });
};

const userData = async (req: any, res: any) => {
  const ctx = await contextBuilder({ req });

  ctx.logger.info(`REST User data request received`);

  return getUserPaymentDetails(ctx, req.params.userId)
    .then((responseObject) => {
      return res.status(responseObject.status).json(responseObject);
    })
    .catch((e) => {
      ctx.logger.error(`userData error: ${(e as Error).message}`);
      return res.status(500).json({
        message: ERROR_MESSAGE_NO_DATA,
      });
    });
};

const registeredUserData = async (req: any, res: any) => {
  const ctx = await contextBuilder({ req });

  ctx.logger.info(`REST Registered User data request received`);
  return getRegisteredUserPaymentDetails(ctx, req.params.userId)
    .then((responseObject) => {
      return res.status(responseObject.status).json(responseObject);
    })
    .catch((e) => {
      ctx.logger.error(
        `getRegisteredUserPaymentDetails error: ${(e as Error).message}`,
      );
      return res.status(500).json({
        message: `${ERROR_MESSAGE_NO_DATA}`,
      });
    });
};

const makeGuestPayment = async (req: any, res: any) => {
  const ctx = await contextBuilder({ req });

  ctx.logger.info('makeGuestPayment Request received: ', req.body);

  const { userId, amount } = req.body;

  const order = await retrieveUserOrder(ctx, { userId: req.body.userId }).catch(
    (e) => {
      ctx.logger.error(
        `makeGuestPayment error: retrieveUserOrder : ${(e as Error).message}`,
      );
    },
  );

  if (!order || !order.journey_id) {
    ctx.logger.error(
      'makeGuestPayment: retrieveUserOrder : order journey_id id is missing',
      {
        order,
        journey_id: order?.journey_id,
      },
    );
    return res.status(412).json({
      message: ERROR_MESSAGE_NO_JOURNEY_ID,
    });
  }

  const { journey_id: journeyId, payment_id: paymentId } = order;
  let paymentResponse = null;
  if (amount <= 0) {
    const setVoidStatus = await setVoidTransaction(ctx, { journeyId }).catch(
      async (e) => {
        ctx.logger.error(
          `makeGuestPayment error: setVoidTransaction:  ${
            (e as Error).message
          }`,
        );
        return null;
      },
    );

    if (setVoidStatus) {
      paymentResponse = setVoidStatus;
      await storeVoidTransactionService(ctx, [
        { paymentId, voidStatus: setVoidStatus },
      ]).catch(async (e) => {
        ctx.logger.error(
          `makeGuestPayment error: storeVoidTransaction:  ${
            (e as Error).message
          }`,
        );
        return null;
      });
    }
  } else {
    let guestPaymentAmount = amount;
    const preAuthAmount = order?.pre_authorization?.amount;
    ctx.logger.info(`makeGuestPayment: preAuthAmount ${preAuthAmount}`);

    if (preAuthAmount && preAuthAmount < amount) {
      guestPaymentAmount = preAuthAmount;
      ctx.logger.info(`makeGuestPayment: Amount ${amount} is overceeded`);
    }

    paymentResponse = await makePayment(ctx, {
      journeyId,
      amount: guestPaymentAmount,
      currency: req.body.currency,
      userId,
    }).catch(async (e) => {
      ctx.logger.error(
        `makeGuestPayment error: makePayment :${(e as Error).message}`,
      );
    });
  }

  if (paymentResponse) {
    return res.status(200).json({
      message: 'ok',
    });
  }

  await storePaymentStatus(ctx, {
    paymentId,
    amount: 0,
    currency: req.body.currency,
    status: 'Outstanding',
  }).catch((e) => {
    ctx.logger.error(
      `makeGuestPayment error: storePaymentStatus :${(e as Error).message}`,
    );
  });
  return res.status(500).json({
    message: 'could not initiate payment',
  });
};

const paymentCallbackStatus = async (req: any, res: any) => {
  const ctx = await contextBuilder({ req });

  ctx.logger.info('Payment callback request received: ', req.body);
  const { userId, userType, currency, status, amount, cardData } = req.body;
  let { paymentId } = req.body;

  let order;

  // makes sure where we know the paymentId that we update the status of the correct payment
  if (paymentId) {
    order = await retrieveOrder({ paymentId }).catch((e: Error) => {
      ctx.logger.error(
        `paymentCallbackStatus error: retrieveOrder : ${e.message}`,
      );
    });
  } else {
    // legacy Guest behaviour which is based on a callback from the bppay service
    order = await retrieveUserOrder(ctx, { userId }).catch((e: Error) => {
      ctx.logger.error(
        `paymentCallbackStatus error: retrieveUserOrder : ${e.message}`,
      );
    });
  }

  if (
    userType !== UserType.REGISTERED &&
    (!order || !order.journey_id || !order.payment_id)
  ) {
    ctx.logger.error(
      'paymentCallbackStatus: retrieveUserOrder: missing order data',
      {
        order,
        journey_id: order?.journey_id,
        payment_id: order?.payment_id,
        userType,
      },
    );

    return res.status(412).json({
      message: ERROR_MESSAGE_NO_JOURNEY_ID,
    });
  }

  const { charge_session_id: chargeSessionId } = order;
  if (!paymentId) {
    paymentId = order.payment_id;
  }

  if (cardData) {
    const { cardNumber, fundingMethod, cardScheme } = cardData;

    if (cardNumber || fundingMethod || cardScheme) {
      await storeCardDetails(ctx, {
        paymentId,
        cardNumber,
        fundingMethod,
        cardScheme,
      }).catch((e) => {
        ctx.logger.error(
          `paymentCallbackStatus error: storeCardDetails :${
            (e as Error).message
          }`,
        );
      });
    }
  }

  const dbResponse = await storePaymentStatus(ctx, {
    paymentId,
    amount,
    currency,
    status,
    chargeSessionId,
  }).catch((e) => {
    ctx.logger.error(
      `paymentCallbackStatus error: storePaymentStatus :${
        (e as Error).message
      }`,
    );
  });

  if (dbResponse) {
    return res.status(200).json({
      message: 'ok',
      paymentId,
      chargeSessionId,
    });
  }
  ctx.logger.info('paymentCallbackStatus: dbResponse has invalid data:', {
    dbResponse,
  });
  return res.status(500).json({
    message: 'Failed to store payment status in DB',
  });
};

const getStatuses = async (
  ctx: BpPayServerContext,
  voidedTransactions: any,
) => {
  const statuses = [];
  if (voidedTransactions.length > maxVoidedTransactions) {
    const numberOfIterations = Math.ceil(
      voidedTransactions.length / maxVoidedTransactions,
    );

    for (let i = 0; i < numberOfIterations; i += 1) {
      const startIncrement = i * maxVoidedTransactions;
      const arrayToVoid = voidedTransactions.slice(
        startIncrement,
        startIncrement + maxVoidedTransactions,
      );
      statuses.push(
        await storeVoidTransactionService(
          ctx,
          arrayToVoid as Array<StoreVoidTransactionI>,
        ),
      );
    }
  } else {
    statuses.push(
      await storeVoidTransactionService(
        ctx,
        voidedTransactions as Array<StoreVoidTransactionI>,
      ),
    );
  }
  return statuses;
};

const mapper = (ctx: BpPayServerContext) => async (order: any) => {
  const { paymentId, journeyId } = order;
  const setVoidStatus = await setVoidTransaction(ctx, { journeyId }).catch(
    async (e) => {
      ctx.logger.error(
        `voidTransaction error: setVoidTransaction:  ${(e as Error).message}`,
      );

      return { paymentId, voidStatus: null };
    },
  );
  return { paymentId, voidStatus: setVoidStatus };
};

const checkSuccessfulVoidCaseMessages = async (
  ctx: BpPayServerContext,
  voidTransactionResponse: any,
) => {
  const successfullyVoidedTransactions = voidTransactionResponse.filter(
    (transaction: any) => transaction.voidStatus === true,
  );

  if (successfullyVoidedTransactions.length > 0) {
    const statuses = await getStatuses(ctx, successfullyVoidedTransactions);

    const failedStoreVoid = statuses.filter((status) => status.status === 500);

    const loggerMessage =
      failedStoreVoid.length > 0
        ? 'voidTransaction: Failures storing transaction void status.'
        : 'voidTransaction: Transactions void status stored successfully.';
    ctx.logger.info(loggerMessage);
  } else {
    ctx.logger.info('No void transaction to store!');
  }
};

const voidTransaction = async (req: any, res: any) => {
  const ctx = await contextBuilder({ req });

  ctx.logger.info(
    'voidTransaction: Void transaction request received ',
    req.body,
  );
  const { transactions } = req.body;
  if (transactions.length > 0) {
    const voidTransactionResponse = await pMap(transactions, mapper(ctx), {
      concurrency: 2,
    });

    checkSuccessfulVoidCaseMessages(ctx, voidTransactionResponse);

    const failedVoidTransactions = voidTransactionResponse.filter(
      (transaction: any) =>
        transaction.voidStatus === false || transaction.voidStatus === null,
    );

    if (failedVoidTransactions.length > 0) {
      ctx.logger.info(
        'voidTransaction: Failed void transactions',
        failedVoidTransactions,
      );
      return res.status(500).json({
        message: 'Void transaction result contains failures.',
      });
    }

    return res.status(200).json({
      message: 'Transactions voided successfully.',
    });
  }
  return res.status(200).json({
    message: 'No transactions to void.',
  });
};

const storeVoidTransaction = async (req: any, res: any) => {
  const ctx = await contextBuilder({ req });

  ctx.logger.info(
    'storeVoidTransaction: Store void transaction request received ',
    req.body,
  );
  const paymentIds = req.body;
  const voidOrderIdsToStore = paymentIds.map((paymentId: string) => ({
    paymentId,
    voidStatus: true,
  }));
  const statuses = [];
  if (voidOrderIdsToStore.length > 10) {
    const numberOfIterations = Math.ceil(voidOrderIdsToStore.length / 10);

    for (let i = 0; i < numberOfIterations; i += 1) {
      const startIncrement = i * 10;
      const arrayToVoid = voidOrderIdsToStore.slice(
        startIncrement,
        startIncrement + 10,
      );
      statuses.push(await storeVoidTransactionService(ctx, arrayToVoid));
    }
  } else {
    statuses.push(await storeVoidTransactionService(ctx, voidOrderIdsToStore));
  }
  const failedStoreVoid = statuses.filter((status) => status.status === 500);
  if (failedStoreVoid.length > 0) {
    ctx.logger.info('storeVoidTransaction: Failed storeVoidTransaction', {
      failedStoreVoid,
    });
    return res.status(500).json({
      message: 'Failures storing transaction void status.',
    });
  }
  return res.status(200).json({
    message: 'Transactions void status stored successfully.',
  });
};

const storeTransactionId = async (req: any, res: any) => {
  const ctx = await contextBuilder({ req });

  ctx.logger.info(
    'storeTransactionId: Store transaction id request received ',
    req.body,
  );
  let { paymentId } = req.body;
  const { transactionId, transactionNumber, userId, registered } = req.body;
  //if the user is registered with HTB he does not get paymentId, in order to proceed we need to generate it
  if (!paymentId) {
    //generate a new unique id
    paymentId = uuidv4();
    //store it in the Dynamo alongside the user id
    await storeJourney(ctx, {
      paymentId,
      userId,
      journeyId: '',
      preAuthAmount: 0,
      roundedAmount: 0,
      preAuthCurrency: '',
      startDate: Date.now(),
      paymentMethodType: 'Wallet',
      sessionId: '',
      registered,
    }).catch((e: any) => {
      ctx.logger.error(
        `storeTransactionId error: storeJourney:  ${(e as Error).message}`,
      );
    });
  }

  const dbResponse = await storeTransactionID(
    ctx,
    transactionId,
    transactionNumber,
    paymentId,
    registered,
  ).catch((e: any) => {
    ctx.logger.error(
      `storeTransactionId error: storeTransactionID:  ${(e as Error).message}`,
    );
  });

  if (dbResponse) {
    return res.status(200).json({
      message: 'ok',
    });
  }
  return res.status(500).json({
    message: 'Failed to store transaction id in dynamo',
  });
};

const storeChargeStatus = async (req: any, res: any) => {
  const ctx = await contextBuilder({ req });

  ctx.logger.info('Store charge status request received: ', req.body);
  const { userId, connectorId, chargeSessionId } = req.body;
  const order = await retrieveUserOrder(ctx, { userId }).catch((e: any) => {
    ctx.logger.error(
      `storeChargeStatus error: retrieveUserOrder:  ${(e as Error).message}`,
    );
  });

  if (!order || !order.payment_id) {
    ctx.logger.error('storeChargeStatus: order details missing', {
      order,
      paymentId: order?.payment_id,
    });
    return res.status(412).json({
      message: ERROR_MESSAGE_NO_JOURNEY_ID,
    });
  }

  const { payment_id: paymentId } = order;
  const chargeStatus = ChargeStatus.STARTED; // hardcoded for now as only one possible status 'started'

  const dbResponse = await storeChargeStatusService(ctx, {
    paymentId,
    chargeStatus,
    connectorId,
    chargeSessionId,
  }).catch((e: any) => {
    ctx.logger.error(
      `storeChargeStatus error: storeChargeStatus:  ${(e as Error).message}`,
    );
  });

  if (dbResponse) {
    return res.status(200).json({ message: 'ok' });
  }

  ctx.logger.info('storeChargeStatus: dbResponse has invalid data:', {
    dbResponse,
  });
  return res.status(500).json({
    message: 'Failed to store charge status in dynamo',
  });
};

export default {
  getTransactions,
  getSessionId,
  paymentData,
  registeredPaymentData,
  userData,
  registeredUserData,
  paymentCallbackStatus,
  makeGuestPayment,
  voidTransaction,
  storeVoidTransaction,
  storeChargeStatus,
  storeTransactionId,
};
