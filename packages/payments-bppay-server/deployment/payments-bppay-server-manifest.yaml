---
apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: payments-bppay-server-aws-secrets
  namespace: payments-server
spec:
  provider: aws
  parameters:
    ## Follow PascalCase for Service, SecretName and SecretType
    objects: |
      - objectName: '${env}-v${version_number}-Payments-AzureDpaasAdClient-Secret'
        objectType: "secretsmanager"
        jmesPath:
          - path: "secret"
            objectAlias: "AzureDpaasAdClient-Secret"
      - objectName: '${env}-v${version_number}-Payments-ApolloInternal-Secret'
        objectType: "secretsmanager"
        jmesPath:
          - path: "secret"
            objectAlias: "ApolloInternal-Secret"
  secretObjects:
    ## SECRET NAME MUST USE - rather than _ and must all be lowercase due to syntax requirements
    - secretName: azure-dpaas-ad-client-secret ##secretname seperated by - where its seperated by PascalCase
      type: Opaque
      data:
        - objectName: AzureDpaasAdClient-Secret
          key: secret
    - secretName: apollo-internal-secret
      type: Opaque
      data:
        - objectName: ApolloInternal-Secret
          key: secret
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: bppay-server-deployment-v${version_number}
  annotations:
    reloader.stakater.com/auto: 'true'
spec:
  selector:
    matchLabels:
      app: bppay-server-v${version_number}
  replicas: #{REPLICAS_LARGE}#
  template:
    metadata:
      annotations:
        sidecar.opentelemetry.io/inject: 'true'
        instrumentation.opentelemetry.io/inject-nodejs: 'true'
      labels:
        app: bppay-server-v${version_number}
    spec:
      serviceAccountName: bppay-server-service-account
      containers:
        - name: bppay-server-v${version_number}
          image: ************.dkr.ecr.eu-west-1.amazonaws.com/payments-bppay-server:${image_tag}
          imagePullPolicy: #{IMAGE_PULL_POLICY}#
          resources:
            requests:
              cpu: #{REPLICAS_CPU_REQUEST_MEDIUM}#
          ports:
            - containerPort: 4011
          volumeMounts:
            - name: secrets-store-inline
              mountPath: /mnt/secrets-store
              readOnly: true
          readinessProbe:
            tcpSocket:
              port: 4011
            initialDelaySeconds: 60
            timeoutSeconds: 50
            periodSeconds: 60
            successThreshold: 1
            failureThreshold: 3
          env:
            - name: OTEL_SERVICE_NAME
              value: 'payments-bppay-server-${env}'
            - name: NODE_ENV
              value: '#{NODE_ENV}#'
            - name: PREAUTH_AMOUNT_DE
              value: '#{PREAUTH_AMOUNT_DE}#'
            - name: PREAUTH_AMOUNT_UK
              value: '#{PREAUTH_AMOUNT_UK}#'
            - name: PREAUTH_AMOUNT_US
              value: '#{PREAUTH_AMOUNT_UK}#'
            - name: PREAUTH_AMOUNT_NL
              value: '#{PREAUTH_AMOUNT_NL}#'
            - name: GO_LIVE_DATE
              value: '#{GO_LIVE_DATE}#'
            - name: PAYMENT_DB_USER_TYPE_DEPLOYMENT_DATE
              value: '#{PAYMENT_DB_USER_TYPE_DEPLOYMENT_DATE}#'
            - name: BP_PAY_ENDPOINT
              value: '#{BP_PAY_ENDPOINT}#'
            - name: DPAAS_PAY_ENDPOINT
              value: '#{DPAAS_PAY_ENDPOINT}#'
            - name: PAYMENTS_DB_TABLE_NAME
              value: '#{PAYMENTS_DB_TABLE_NAME}#'
            - name: HISTORY_DB_TABLE_NAME
              value: '#{HISTORY_DB_TABLE_NAME}#'
            - name: DYNAMO_DB_REGION
              value: '#{DYNAMO_DB_REGION}#'
            - name: DYNAMO_DB_URL
              value: '#{DYNAMO_DB_URL}#'
            - name: DYNAMO_DB_ACCESS_KEY
              value: ''
            - name: DYNAMO_DB_ACCESS_KEY_ID
              value: ''
            - name: SIGNED_REQUEST_LAMBDA_NAME
              value: '#{SIGNED_REQUEST_LAMBDA_NAME}#'
            - name: APOLLO_INTERNAL_USER_ID
              value: '#{APOLLO_INTERNAL_USER_ID}#'
            - name: CARDINAL_ENDPOINT
              value: '#{CARDINAL_ENDPOINT}#'
            - name: ELASTICACHE_HOST
              value: '#{ELASTICACHE_HOST}#'
            - name: ELASTICACHE_PORT
              value: '#{ELASTICACHE_PORT}#'
            - name: MOCK_DPAAS_PAY_ENDPOINT
              value: '#{MOCK_DPAAS_PAY_ENDPOINT}#'
            - name: DPAAS_AZURE_AD_TOKEN_URL
              value: '#{DPAAS_AZURE_AD_TOKEN_URL}#'
            - name: DPAAS_AZURE_AD_CLIENT_ID
              value: '#{DPAAS_AZURE_AD_CLIENT_ID}#'
            - name: DPAAS_WALLET_SCOPE
              value: '#{DPAAS_WALLET_SCOPE}#'
            - name: DPAAS_PAY_SCOPE
              value: '#{DPAAS_PAY_SCOPE}#'
            - name: DPAAS_3DS_SCOPE
              value: '#{DPAAS_3DS_SCOPE}#'
            - name: PRIVATE_GATEWAY_CLUSTER_URL
              value: '#{PRIVATE_GATEWAY_CLUSTER_URL}#'
            - name: APP_ENV
              value: '#{APP_ENV}#'
            - name: AMDE_MERCHANT_ID
              value: '#{AMDE_MERCHANT_ID}#'
            - name: AMNL_MERCHANT_ID
              value: '#{AMNL_MERCHANT_ID}#'
            - name: AMUK_MERCHANT_ID
              value: '#{AMUK_MERCHANT_ID}#'
            - name: AMUS_MERCHANT_ID
              value: '#{AMUS_MERCHANT_ID}#'
            - name: AMES_MERCHANT_ID
              value: '#{AMES_MERCHANT_ID}#'
            - name: 'IDP_PULSE_ISSUER'
              value: '#{IDP_PULSE_ISSUER}#'
            - name: 'IDP_ARAL_ISSUER'
              value: '#{IDP_ARAL_ISSUER}#'
            - name: 'CIP_TOKEN_ISSUER'
              value: '#{CIP_TOKEN_ISSUER}#'
            - name: 'ANON_TOKEN_ISSUER'
              value: '#{ANON_TOKEN_ISSUER}#'
            - name: 'DATE_TYPE'
              value: '#{DATE_TYPE}#'
            - name: 'PROCESSING_PAYMENT_FEATURE_FLAG'
              value: '#{PROCESSING_PAYMENT_FEATURE_FLAG}#'
            - name: 'PAYMENT_REFERENCE_NUMBER'
              value: '#{PAYMENT_REFERENCE_NUMBER}#'
            - name: APOLLO_INTERNAL_SECRET
              value: null
              valueFrom:
                secretKeyRef:
                  name: apollo-internal-secret
                  key: secret
            - name: DPAAS_AZURE_AD_CLIENT_SECRET
              value: null
              valueFrom:
                secretKeyRef:
                  name: azure-dpaas-ad-client-secret
                  key: secret
            - name: DPAAS_TENANT_ENV
              value: '#{DPAAS_TENANT_ENV}#'
            - name: 'OPEN_ID_ACCESS_ROLE'
              value: '#{OPEN_ID_ACCESS_ROLE}#'
          # - name: PLACERHOLDER_SECRET
          #   value: null
          #   valueFrom:
          #     secretKeyRef:
          #       name: placeholder-secret
          #       key: secret
      volumes:
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: payments-bppay-server-aws-secrets
