deployName: 'bppay-server'
serverName: 'payments-bppay-server'
namespace: 'payments-server'
imageRepo: payments-bppay-server
versionNumber: 'x'
replicas: #{REPLICAS_LARGE}#
rolloutReplicas: #{HPA_REPLICAS_LARGE}#

shared:
  hpapdbName: bppay-server
  minReplicas: #{HPA_REPLICAS_LARGE}#
  maxReplicas: #{REPLICAS_MAX_XTRA_LARGE}#
  averageUtilization: 95
  microservicePort: 4011

serviceAccount: bppay-server-service-account

rollouts:
  enabled: #{HELM_ENABLE_ROLLOUTS}#

includeSchema: true

service:
  clusterIp:
    enabled: true
  loadBalancer:
    enabled: true
    tags: 'placeholder'

deployment:
  hpa:
    enabled: false
  servers:
    enabled: true

secrets:
  - objectName: '{{ .Values.environment }}-v{{ .Values.versionNumber }}-Payments-AzureDpaasAdClient-Secret'
    alias: AzureDpaasAdClient-Secret
    k8sSecretName: azure-dpaas-ad-client-secret
  - objectName: '{{ .Values.environment }}-v{{ .Values.versionNumber }}-Payments-ApolloInternal-Secret'
    alias: ApolloInternal-Secret
    k8sSecretName: apollo-internal-secret

env:
  - name: OTEL_SERVICE_NAME
    value: 'payments-bppay-server-{{ .Values.environment }}'
  - name: NODE_ENV
    value: '#{NODE_ENV}#'
  - name: PREAUTH_AMOUNT_DE
    value: '#{PREAUTH_AMOUNT_DE}#'
  - name: PREAUTH_AMOUNT_UK
    value: '#{PREAUTH_AMOUNT_UK}#'
  - name: PREAUTH_AMOUNT_US
    value: '#{PREAUTH_AMOUNT_UK}#'
  - name: PREAUTH_AMOUNT_NL
    value: '#{PREAUTH_AMOUNT_NL}#'
  - name: GO_LIVE_DATE
    value: '#{GO_LIVE_DATE}#'
  - name: PAYMENT_DB_USER_TYPE_DEPLOYMENT_DATE
    value: '#{PAYMENT_DB_USER_TYPE_DEPLOYMENT_DATE}#'
  - name: BP_PAY_ENDPOINT
    value: '#{BP_PAY_ENDPOINT}#'
  - name: DPAAS_PAY_ENDPOINT
    value: '#{DPAAS_PAY_ENDPOINT}#'
  - name: PAYMENTS_DB_TABLE_NAME
    value: '#{PAYMENTS_DB_TABLE_NAME}#'
  - name: HISTORY_DB_TABLE_NAME
    value: '#{HISTORY_DB_TABLE_NAME}#'
  - name: DYNAMO_DB_REGION
    value: '#{DYNAMO_DB_REGION}#'
  - name: DYNAMO_DB_URL
    value: '#{DYNAMO_DB_URL}#'
  - name: DYNAMO_DB_ACCESS_KEY
    value: ''
  - name: DYNAMO_DB_ACCESS_KEY_ID
    value: ''
  - name: SIGNED_REQUEST_LAMBDA_NAME
    value: '#{SIGNED_REQUEST_LAMBDA_NAME}#'
  - name: APOLLO_INTERNAL_USER_ID
    value: '#{APOLLO_INTERNAL_USER_ID}#'
  - name: CARDINAL_ENDPOINT
    value: '#{CARDINAL_ENDPOINT}#'
  - name: ELASTICACHE_HOST
    value: '#{ELASTICACHE_HOST}#'
  - name: ELASTICACHE_PORT
    value: '#{ELASTICACHE_PORT}#'
  - name: MOCK_DPAAS_PAY_ENDPOINT
    value: '#{MOCK_DPAAS_PAY_ENDPOINT}#'
  - name: DPAAS_AZURE_AD_TOKEN_URL
    value: '#{DPAAS_AZURE_AD_TOKEN_URL}#'
  - name: DPAAS_AZURE_AD_CLIENT_ID
    value: '#{DPAAS_AZURE_AD_CLIENT_ID}#'
  - name: DPAAS_WALLET_SCOPE
    value: '#{DPAAS_WALLET_SCOPE}#'
  - name: DPAAS_PAY_SCOPE
    value: '#{DPAAS_PAY_SCOPE}#'
  - name: DPAAS_3DS_SCOPE
    value: '#{DPAAS_3DS_SCOPE}#'
  - name: PRIVATE_GATEWAY_CLUSTER_URL
    value: '#{PRIVATE_GATEWAY_CLUSTER_URL}#'
  - name: APP_ENV
    value: '#{APP_ENV}#'
  - name: AMDE_MERCHANT_ID
    value: '#{AMDE_MERCHANT_ID}#'
  - name: AMNL_MERCHANT_ID
    value: '#{AMNL_MERCHANT_ID}#'
  - name: AMUK_MERCHANT_ID
    value: '#{AMUK_MERCHANT_ID}#'
  - name: AMUS_MERCHANT_ID
    value: '#{AMUS_MERCHANT_ID}#'
  - name: AMES_MERCHANT_ID
    value: '#{AMES_MERCHANT_ID}#'
  - name: 'IDP_PULSE_ISSUER'
    value: '#{IDP_PULSE_ISSUER}#'
  - name: 'IDP_ARAL_ISSUER'
    value: '#{IDP_ARAL_ISSUER}#'
  - name: 'CIP_TOKEN_ISSUER'
    value: '#{CIP_TOKEN_ISSUER}#'
  - name: 'ANON_TOKEN_ISSUER'
    value: '#{ANON_TOKEN_ISSUER}#'
  - name: 'DATE_TYPE'
    value: '#{DATE_TYPE}#'
  - name: 'PROCESSING_PAYMENT_FEATURE_FLAG'
    value: '#{PROCESSING_PAYMENT_FEATURE_FLAG}#'
  - name: 'PAYMENT_REFERENCE_NUMBER'
    value: '#{PAYMENT_REFERENCE_NUMBER}#'
  - name: APOLLO_INTERNAL_SECRET
    value: null
    valueFrom:
      secretKeyRef:
        name: apollo-internal-secret
        key: secret
  - name: DPAAS_AZURE_AD_CLIENT_SECRET
    value: null
    valueFrom:
      secretKeyRef:
        name: azure-dpaas-ad-client-secret
        key: secret
  - name: DPAAS_TENANT_ENV
    value: '#{DPAAS_TENANT_ENV}#'
  - name: 'OPEN_ID_ACCESS_ROLE'
    value: '#{OPEN_ID_ACCESS_ROLE}#'
# - name: PLACERHOLDER_SECRET
#   value: null
#   valueFrom:
#     secretKeyRef:
#       name: placeholder-secret
#       key: secret
