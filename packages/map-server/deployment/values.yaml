deployName: 'map-server'
serverName: 'map-server'
namespace: 'map-server'
imageRepo: map-server
versionNumber: 'x'
replicas: #{REPLICAS_LARGE}#
rolloutReplicas: #{HPA_REPLICAS_LARGE}#

shared:
  hpapdbName: map-server
  minReplicas: #{HPA_REPLICAS_LARGE}#
  maxReplicas: #{REPLICAS_MAX_XTRA_LARGE}#
  averageUtilization: 80
  microservicePort: 4003

serviceAccount: map-server-service-account

rollouts:
  enabled: #{HELM_ENABLE_ROLLOUTS}#

image:
  repository: ************.dkr.ecr.eu-west-1.amazonaws.com
  imageRepo: map-server
  tag: ''
  pullPolicy: '#{IMAGE_PULL_POLICY}#'

resources:
  cpuRequest: '#{REPLICAS_CPU_REQUEST_MEDIUM}#'

includeSchema: true

service:
  targetPort: 4003
  clusterIp:
    enabled: true
  loadBalancer:
    enabled: true
    tags: 'placeholder'

deployment:
  hpa:
    enabled: false
  servers:
    enabled: true

cronjobs:
  - cronJobName: get-data-dcs
    cronExpression: '0 0 * * *'
    cronImageTag: ''

secrets:
  - objectName: '{{ .Values.environment }}-v{{ .Values.versionNumber }}-Backend-Bpcm-ApiKey'
    alias: BpcmServices-ApiKey
    k8sSecretName: bpcm-services-api-key

  - objectName: '{{ .Values.environment }}-v{{ .Values.versionNumber }}-Map-DcsSubscription-Key'
    alias: DcsSubscription-Key
    k8sSecretName: dcs-subscription-key

  - objectName: '{{ .Values.environment }}-v{{ .Values.versionNumber }}-Map-DcsTokenClient-Secret'
    alias: DcsTokenClient-Secret
    k8sSecretName: dcs-token-client-secret

  - objectName: '{{ .Values.environment }}-v{{ .Values.versionNumber }}-Map-Evc-Token'
    alias: Evc-Token
    k8sSecretName: evc-token

  - objectName: '{{ .Values.environment }}-v{{ .Values.versionNumber }}-Map-HtbServices-BearerToken'
    alias: HtbServices-BearerToken
    k8sSecretName: htb-services-bearer-token

env:
  - name: OTEL_SERVICE_NAME
    value: 'map-server-{{ .Values.environment }}'
  - name: APP_ENV
    value: '#{APP_ENV}#'
  - name: NODE_ENV
    value: '#{NODE_ENV}#'
  - name: ELASTICACHE_HOST
    value: '#{ELASTICACHE_HOST}#'
  - name: ELASTICACHE_PORT
    value: '#{ELASTICACHE_PORT}#'
  - name: PRICES_ELASTICACHE_HOST
    value: '#{PRICES_ELASTICACHE_HOST}#'
  - name: PRICES_ELASTICACHE_PORT
    value: '#{PRICES_ELASTICACHE_PORT}#'
  - name: ELASTICSEARCH_URL
    value: '#{ELASTICSEARCH_URL}#'
  - name: BPCM_AWS_SERVICES_URL
    value: '#{BPCM_AWS_SERVICES_URL}#'
  - name: HTB_SERVICES_URL
    value: '#{HTB_SERVICES_URL}#'
  - name: DCS_TOKEN_URL
    value: '#{DCS_TOKEN_URL}#'
  - name: DCS_TOKEN_CLIENT_ID
    value: '#{DCS_TOKEN_CLIENT_ID}#'
  - name: DCS_TOKEN_RESOURCE
    value: '#{DCS_TOKEN_RESOURCE}#'
  - name: DCS_USER_SERVICES_URL
    value: '#{DCS_USER_SERVICES_URL}#'
  - name: EVC_SERVICES_URL
    value: '#{EVC_SERVICES_URL}#'
  - name: AWS_REGION
    value: '#{AWS_REGION}#'
  - name: S3_BUCKET_MAP
    value: '#{S3_BUCKET_MAP}#'
  - name: REGISTERED_PREAUTH_AMOUNT_DE
    value: '#{REGISTERED_PREAUTH_AMOUNT_DE}#'
  - name: REGISTERED_PREAUTH_AMOUNT_ES
    value: '#{REGISTERED_PREAUTH_AMOUNT_ES}#'
  - name: REGISTERED_PREAUTH_AMOUNT_NL
    value: '#{REGISTERED_PREAUTH_AMOUNT_NL}#'
  - name: REGISTERED_PREAUTH_AMOUNT_UK
    value: '#{REGISTERED_PREAUTH_AMOUNT_UK}#'

  - name: BPCM_AWS_SERVICES_KEY
    valueFrom:
      secretKeyRef:
        name: bpcm-services-api-key
        key: secret
  - name: DCS_SUBSCRIPTION_KEY
    valueFrom:
      secretKeyRef:
        name: dcs-subscription-key
        key: secret
  - name: DCS_TOKEN_CLIENT_SECRET
    valueFrom:
      secretKeyRef:
        name: dcs-token-client-secret
        key: secret
  - name: EVC_TOKEN
    valueFrom:
      secretKeyRef:
        name: evc-token
        key: secret
  - name: HTB_SERVICES_BEARER_TOKEN
    valueFrom:
      secretKeyRef:
        name: htb-services-bearer-token
        key: secret
