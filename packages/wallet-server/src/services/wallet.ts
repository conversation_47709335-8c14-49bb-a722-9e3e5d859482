import { Countries, UserType } from '../common/enums';
import { IDeleteSubsDefaultMethod, IRange } from '../common/interfaces';
import logger from '../utils/logger';
import {
  addPaymentMethod,
  deletePaymentMethod,
  getPaymentMethods,
} from './bpWallet/bpWallet';
import {
  deleteSUBSDefaultPayment,
  deleteUserWalletData,
  deleteWalletDeletionDate,
  retrieveDefaultPaymentMethod,
  retrievePendingDeletionMethods,
  storeDefaultPaymentMethod,
  storeToBeDeleted,
} from './dynamo/dynamo';
import { get, set } from './elasticache/elasticache';
import { setDefaultPaymentMethodWallet } from './setDefaultPaymentMethodWallet/setDefaultPaymentMethodWallet';
import { getUserInfo } from './user/getUserInfo';
interface StorePaymentToken {
  userId: string;
  token: string;
  isDefault: boolean | null;
  isSubsDefault?: boolean;
}

interface GetWalletParams {
  userId: string;
  default: boolean;
  country: Countries;
  paymentMethodToken: string;
}

interface DpaasError extends Error {
  response?: {
    status?: number;
  };
}

export const storePaymentTokenMethod = async ({
  userId,
  token,
  isDefault,
  isSubsDefault,
}: StorePaymentToken) => {
  const response = await set(
    userId,
    JSON.stringify({ token, isDefault, isSubsDefault }),
  );
  if (response) {
    return {
      status: '201',
      message: 'OK',
    };
  }
  return {
    status: '500',
    message: 'INTERNAL_SERVER_ERROR',
  };
};

export const retrievePaymentTokenMethod = async (userId: string) => {
  try {
    const res: any = await get(userId);

    if (!res) {
      throw new Error('No token found');
    } else {
      return res;
    }
  } catch (error) {
    logger.error('🚨 Error encountered while retrieving payment token: ', {
      userId,
      error,
    });
    throw error;
  }
};

export const createAndStorePaymentMethod = async (
  args: GetWalletParams,
  logTraceId: string,
) => {
  const paymentId = await addPaymentMethod(args, logTraceId);
  if (args.default) {
    storeDefaultPaymentMethod({
      userId: args.userId,
      defaultPaymentMethodId: paymentId,
    });
  }
  return paymentId;
};

export const createAndStoreSUBSPaymentMethod = async (
  args: GetWalletParams,
  logTraceId: string,
) => {
  const paymentId = await addPaymentMethod(args, logTraceId);
  await setDefaultPaymentMethodWallet({
    userId: args.userId,
    paymentMethodId: paymentId,
    isSubsDefault: true,
    logTraceId,
  });
  return paymentId;
};

export const getPaymentMethodsFromWallet = async (
  userId: string,
  logTraceId: string,
) => {
  const walletMethods = await getPaymentMethods(userId, logTraceId);

  if (walletMethods.length === 0) {
    return [];
  }
  const defaultMethod = await retrieveDefaultPaymentMethod({ userId });
  // after running retrieveDefaultPaymentMethod from the dynamo database if paymentMethod
  // has a delete_date value it is not marked as the default payment method when returning the data

  const defaultMethodInWallet = walletMethods.some(
    (method: any) =>
      method?.paymentMethodId === defaultMethod?.default_method_id,
  );
  if (!defaultMethodInWallet) {
    storeDefaultPaymentMethod({
      userId,
      defaultPaymentMethodId: 'null',
    });
  }

  const acceptedCardTypes = ['visa', 'mastercard', 'amex'];

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const paymentMethods: Array<any> = [];
  walletMethods.some((method: any) => {
    if (
      !method.card_type ||
      !acceptedCardTypes.includes(method.card_type.toLowerCase())
    ) {
      logger.error(
        `Card type of ${method.card_type} not supported, ignoring from results`,
        { logTraceId },
      );
      return;
    }
    paymentMethods.push({
      ...method,
      cardholderName: method.cardholder_name,
      first_name: '',
      last_name: '',
      firstName: '',
      lastName: '',
      bin: '',
      city: '',
      country: '',
      email: '',
      address1: '',
      address2: '',
      state: '',
      postalCode: '',
      phoneNumber: '',
      phone_number: '',
      nickname: '',
      lastFour: method.last_four,
      cardType: method.card_type,
      paymentMethodType: method.payment_method_type,
      paymentMethodToken: method.payment_method_token,
      default:
        method.paymentMethodId === defaultMethod?.default_method_id &&
        !defaultMethod?.delete_date,
      subsDefault:
        method.paymentMethodId === defaultMethod?.subs_default_method_id,
      deleteDate:
        method.paymentMethodId === defaultMethod?.default_method_id
          ? defaultMethod?.delete_date
          : null,
    });
  });

  return paymentMethods;
};

interface DeleteAndRemoveWalletMethodParams {
  userId: string;
  toDeleteId: string;
  logTraceId: string;
}

const deleteAndRemoveDefaultWalletMethod = async ({
  userId,
  toDeleteId,
  logTraceId,
}: DeleteAndRemoveWalletMethodParams) => {
  await deletePaymentMethod(userId, toDeleteId, logTraceId).catch((e) => {
    throw e;
  });
  await deleteUserWalletData({ userId, logTraceId }).catch((e) => {
    logger.error(
      `🚨 Error encountered on deletePaymentMethodWallet, could not delete the user's wallet data successfully for userId ${userId}, logTraceId${logTraceId}: `,
    );
    throw e;
  });
  return 'Done';
};

interface DeleteMethodParams {
  userId: string;
  paymentMethodId: string;
}

export const deletePaymentMethodWallet = async (
  args: DeleteMethodParams,
  logTraceId: string,
) => {
  const { userId, paymentMethodId: toDeleteId } = args;
  const methods = await getPaymentMethodsFromWallet(userId, logTraceId);
  const toDeletePaymentMethod = methods.find(
    (method: any) => method?.paymentMethodId === toDeleteId,
  );
  if (!toDeletePaymentMethod) {
    logger.error(
      `🚨 Error encountered on deletePaymentMethodWallet, no reference to the to be deleted payment method id:${toDeleteId} on userId: ${userId}, logTraceId${logTraceId}: `,
    );
    throw new Error(`No reference to payment method id:${toDeleteId}`);
  }

  // If the payment method is subs default and the user is subs it cannot be deleted
  if (toDeletePaymentMethod?.subsDefault) {
    const { type } = await getUserInfo(userId);
    if (type === UserType.SUBS_WALLET) {
      logger.error(
        `🚨 Error encountered on deletePaymentMethodWallet, payment method id:${toDeleteId} is subscription defualt on userId: ${userId}, logTraceId${logTraceId}: `,
      );
      throw new Error(
        `Payment method id:${toDeleteId} is subscription default`,
      );
    }
    // Delete record from dynamo
    const deleteResponse = await deleteSUBSDefaultPayment({ userId });
    if (deleteResponse.status !== 200) {
      logger.error(
        `🚨 Error encountered on deletePaymentMethodWallet, subs default payment method id:${toDeleteId} could not be deleted from dynamo for userId: ${userId}, logTraceId${logTraceId}: `,
      );
      throw new Error(
        `Could not delete subs default method id:${toDeleteId} from dynamo`,
      );
    }
  }

  // If the payment method is not default and has no delete_date the payment method can be deleted immediately
  if (!toDeletePaymentMethod?.default && !toDeletePaymentMethod?.deleteDate) {
    await deletePaymentMethod(userId, toDeleteId, logTraceId).catch((e) => {
      throw e;
    });
    return 'Done';
  }

  // If the payment method is not default and has a delete_date in the past
  // the payment method can be deleted immediately and the entry in the dynamo database
  //  should be removed for the user by running deleteWalletData
  if (!toDeletePaymentMethod?.default) {
    const timeDifference = Date.now() - toDeletePaymentMethod?.deleteDate;
    if (timeDifference >= 0) {
      return deleteAndRemoveDefaultWalletMethod({
        userId,
        toDeleteId,
        logTraceId,
      });
    }

    // If the payment method is not default and there is a delete date which has not yet passed then throw a 500 error
    throw new Error(
      'Attempting to delete a payment method that is pending deletion',
    );
  }

  // If the payment method is default but there are other cards
  // delete the payment method
  // set the first other card in the list of payment methods as the default with the existing setDefaultPaymentMethodWallet method
  if (methods.length > 1) {
    await deletePaymentMethod(userId, toDeleteId, logTraceId).catch((e) => {
      throw e;
    });
    const newDefaultCard = methods.find(
      (method: any) => method?.paymentMethodId !== toDeleteId,
    );
    await storeDefaultPaymentMethod({
      userId,
      defaultPaymentMethodId: newDefaultCard?.paymentMethodId,
    }).catch((e) => {
      logger.error(
        `🚨 Error encountered on deletePaymentMethodWallet, deleted method but didn't store the new default method succesfuly for userId ${userId}, logTraceId${logTraceId}: `,
      );
      throw e;
    });
    return 'Done';
  }

  // If the payment method is default but there are no other cards
  // but the final card is missing the agreementId or the card is an AMEX one
  // then it needs to be deleted immediately
  if (
    !toDeletePaymentMethod?.agreementId ||
    toDeletePaymentMethod?.cardType === 'amex'
  ) {
    return deleteAndRemoveDefaultWalletMethod({
      userId,
      toDeleteId,
      logTraceId,
    });
  }

  //   If the payment method is default but there are no other cards
  // run the new storeToBeDeleted function
  await storeToBeDeleted({ userId }).catch((e) => {
    logger.error(
      `🚨 Error encountered on deletePaymentMethodWallet inside storeToBeDeleted for userId ${userId}, logTraceId${logTraceId}: `,
    );
  });
  return 'Stored deletion pending state';
};

export const addPaymentMethodWallet = async (args: any, logTraceId: string) => {
  const { userId } = args;
  const paymentMethodParams = {
    userId: args.userId,
    default: args.default || false,
    paymentMethodToken: args.token,
    country: args.country,
    threeDS: args.threeDS,
  };
  const defaultMethod = await retrieveDefaultPaymentMethod({ userId });
  const subsDefaultFlag = args.subsDefaultFlag || false;
  if (!defaultMethod?.delete_date) {
    if (subsDefaultFlag) {
      return createAndStoreSUBSPaymentMethod(paymentMethodParams, logTraceId);
    }
    return createAndStorePaymentMethod(paymentMethodParams, logTraceId);
  }
  let reinstateDeletedCard = false;
  const paymentId = await addPaymentMethod(
    paymentMethodParams,
    logTraceId,
  ).catch((e: DpaasError) => {
    if (e?.response?.status === 409) {
      reinstateDeletedCard = true;
    } else {
      throw e;
    }
  });
  if (reinstateDeletedCard) {
    await deleteWalletDeletionDate({ userId });
    return defaultMethod.default_method_id;
  }
  await deletePaymentMethod(
    userId,
    defaultMethod.default_method_id,
    logTraceId,
  );
  if (subsDefaultFlag) {
    await setDefaultPaymentMethodWallet({
      userId,
      paymentMethodId: paymentId,
      isSubsDefault: true,
      logTraceId,
    });
  }
  await storeDefaultPaymentMethod({
    userId,
    defaultPaymentMethodId: paymentId,
  });
  await deleteWalletDeletionDate({ userId });
  return paymentId;
};

export const getPendingDeletionPaymentMethods = async (
  args: IRange,
  logTraceId: string,
) => {
  return retrievePendingDeletionMethods(args, logTraceId).catch((e) => {
    logger.error(
      `🚨 Error encountered on retrievePendingDeletionMethods, could not load values, logTraceId: ${logTraceId}: `,
    );
    throw e;
  });
};

export const deleteSUBSDefaultPaymentMethodWallet = async ({
  userId,
  paymentMethodId,
}: IDeleteSubsDefaultMethod) => {
  const defaultMethod = await retrieveDefaultPaymentMethod({ userId });
  if (paymentMethodId !== defaultMethod?.subs_default_method_id) {
    return {
      status: 500,
      message: `Payment method does not match subs default card for userId: ${userId}`,
    };
  } else {
    logger.info(
      `Calling deleteSUBSDefaultPayment method for userId: ${userId}`,
    );
    return deleteSUBSDefaultPayment({ userId }).catch((e) => {
      logger.error(
        `🚨 Error encountered on deleteSUBSDefaultPayment, could not delete the user's default payment successfully for userId ${userId} `,
      );
      throw e;
    });
  }
};
