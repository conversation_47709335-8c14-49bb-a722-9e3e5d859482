const axios = require('axios');
import {
  ChargeProviders,
  Countries,
  TagStatus,
  UserTypes,
} from '../common/enums';
import {
  del,
  getEntriesByCardNumber,
  getEntriesByStatus,
  updateEntriesByCardNumber,
} from '../database/dynamo.client';
import logger from '../utils/logger';
import {
  addRFID,
  blockRFID,
  deleteRfidData,
  fetchRfidDataByCardNumber,
  fetchRfidDataByStatus,
  getLatestRequestIdentifier,
  insertRfidData,
  replaceRFID,
  requestRFID,
  unblockRFID,
  updateRfidData,
  updateRFIDInternal,
} from './rfid.resolver.functions';
import {
  addOrUnblockRFIDSuccessPayload,
  blockRFIDSuccessPayload,
  deleteRfidPayload,
  fetchRFIDPayload,
  incompletePayloadError,
  incorrectDeleteRfidPayload,
  incorrectUpdateRFIDPayload,
  replaceRFIDSuccessPayload,
  requestSuccessRFIDPayloadNL,
  rfidResponsedata,
} from './rfid.resolver.functions.test.mockData';

jest.mock('../clients/elasticache', () => ({
  get: jest.fn(),
  set: jest.fn(),
}));

jest.mock('../env.ts', () => ({ env: {} }));

const mockRequestRFID = jest.fn().mockResolvedValue({
  status: 200,
  data: { eventDetails: '', eventTime: '', salesforceID: '' },
});

const mockDefault = jest.fn().mockResolvedValue({
  status: 200,
  message: 'Success',
});

const mockReplaceRFID = jest.fn().mockResolvedValue({
  status: 200,
  data: {
    error: null,
    code: 200,
    eventDetails: {},
    eventTime: {},
    message: 'message',
    salesforceID: 'xxx',
  },
});

jest.mock('../providers', () => {
  return {
    AURORA: {
      requestRFID: jest.fn(() => mockRequestRFID()),
      addRFID: jest.fn(() => mockDefault()),
      unblockRFID: jest.fn(() => mockDefault()),
      blockRFID: jest.fn(() => mockDefault()),
    },
    BPCM: {
      requestRFID: jest.fn(() => mockRequestRFID()),
      replaceRFID: jest.fn(() => mockReplaceRFID()),
    },
  };
});
const mockUpdatePhysicalAuthMediaHtbResult = {
  status: 200,
  message: 'Success',
};

const mockUpdatePhysicalAuthMediaHtb = jest
  .fn()
  .mockResolvedValue(mockUpdatePhysicalAuthMediaHtbResult);

jest.mock('../services/htb', () => ({
  updatePhysicalAuthMediaHtb: (
    rfidUid,
    active,
    cardNumber,
    logTraceId,
    deleted,
    rateId,
  ) =>
    mockUpdatePhysicalAuthMediaHtb(
      rfidUid,
      active,
      cardNumber,
      logTraceId,
      deleted,
      rateId,
    ),
}));

jest.spyOn(logger, 'info');
const mockGetLatestRequestIdentifier = jest.fn();
// .mockResolvedValue(() => Promise.resolve('00000001'));
jest.mock('../database/dynamo.client', () => ({
  getEntriesByCardNumber: jest.fn(async () => Promise.resolve('')),
  getEntriesByStatus: jest.fn(),
  updateEntriesByCardNumber: jest.fn(),
  del: jest.fn(),
  insertRFIDEntry: jest.fn(async () => {
    return {
      status: 200,
    };
  }),
  getLatestRequestIdentifierDynamo: () => mockGetLatestRequestIdentifier(),
  getEntriesByUserId: jest.fn(async () => {
    return {
      data: [],
    };
  }),
}));

const mockGetUserInfo = jest.fn();

jest.mock('../services/userService/userService', () => ({
  getUserInfo: () => mockGetUserInfo(),
}));

jest.mock('axios');

describe('Replace RFID', () => {
  it('should return a successful request response', async () => {
    const result = await replaceRFID(replaceRFIDSuccessPayload);
    expect(result).toEqual({
      status: 200,
      data: {
        error: null,
        code: 200,
        eventDetails: {},
        eventTime: {},
        message: 'message',
        salesforceID: 'xxx',
      },
    });
  });

  it('Request RFID fails with null payload', async () => {
    const result = await replaceRFID(null);
    expect(result).toEqual(
      new Error('🚨 logTraceId undefined - incomplete payload'),
    );
  });

  it('should throw an error if the provider function fails', async () => {
    const error = new Error('Failed to insert rfid data');
    mockReplaceRFID.mockRejectedValueOnce(error);
    await expect(replaceRFID(replaceRFIDSuccessPayload)).rejects.toThrow(error);
  });
});

describe('Fetch RFID Data', () => {
  it('should return existing RFID data from dynamo when the correct payload is sent', async () => {
    const mockedReponse = { data: rfidResponsedata };
    getEntriesByStatus.mockImplementationOnce(() =>
      Promise.resolve(mockedReponse),
    );
    const response = await fetchRfidDataByStatus(fetchRFIDPayload);
    expect(response).toEqual(mockedReponse);
  });

  it('should return an error when an incorrect payload is sent', async () => {
    expect(await fetchRfidDataByStatus(null)).toEqual(
      new Error('🚨 logTraceId undefined - incomplete payload'),
    );
  });
});

describe('Update RFID Data', () => {
  it('should update existing RFID data in dynamo when the correct payload is sent', async () => {
    const mockedReponse = {
      status: 200,
      message: 'Success updating Dynamo RFID data',
    };
    updateEntriesByCardNumber.mockImplementationOnce(() =>
      Promise.resolve(mockedReponse),
    );
    const response = await updateRfidData(fetchRFIDPayload);
    expect(response).toEqual(mockedReponse);
  });

  it('should not update any data from dynamo when no payload is sent', () => {
    expect(updateRfidData(null)).rejects.toThrow(
      new Error('🚨 logTraceId undefined - incomplete payload'),
    );
  });

  it('should throw an error during updateRfidData call to Dynamo.', async () => {
    updateEntriesByCardNumber.mockImplementationOnce(() =>
      Promise.reject(new Error('Wrong table name')),
    );
    expect(updateRfidData(incorrectUpdateRFIDPayload)).rejects.toThrow();
  });
});

describe('Delete RFID Data', () => {
  it('should delete existing RFID data from dynamo when the correct payload is sent', async () => {
    const mockedReponse = {
      status: 200,
      message: 'Success deleting Dynamo RFID data',
    };
    del.mockImplementationOnce(() => Promise.resolve(mockedReponse));
    const response = await deleteRfidData(deleteRfidPayload);
    expect(response).toEqual(mockedReponse);
  });

  it('should not delete any data from dynamo when no payload is sent', () => {
    expect(deleteRfidData(null)).rejects.toThrow(
      new Error('🚨 logTraceId undefined - incomplete payload'),
    );
  });

  it('should throw an error when an incorrect payload is sent', async () => {
    del.mockImplementationOnce(() =>
      Promise.reject(new Error('Wrong data in the payload')),
    );
    expect(deleteRfidData(incorrectDeleteRfidPayload)).rejects.toThrow();
  });
});

describe('Request RFID Data', () => {
  it('should return a successful request response for Aurora', async () => {
    mockGetUserInfo.mockResolvedValueOnce({
      type: UserTypes['PAYG_Wallet'],
    });

    const result = await requestRFID(requestSuccessRFIDPayloadNL);
    expect(result).toEqual({
      status: 200,
      data: { eventDetails: '', eventTime: '', salesforceID: '' },
    });
  });

  it('Request RFID fails with null payload', async () => {
    const result = await requestRFID(null);
    expect(result).toEqual(
      new Error('🚨 logTraceId undefined - incomplete payload'),
    );
  });

  it('should throw an error if the provider function fails', async () => {
    mockGetUserInfo.mockResolvedValueOnce({
      type: UserTypes['PAYG_Wallet'],
    });
    const error = new Error('Failed to insert rfid data');
    mockRequestRFID.mockRejectedValueOnce(error);
    await expect(requestRFID(requestSuccessRFIDPayloadNL)).rejects.toThrow(
      error,
    );
  });
});

describe('Add RFID', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return a successful request response', async () => {
    const result = await addRFID(addOrUnblockRFIDSuccessPayload);
    expect(result).toEqual({
      status: 200,
      message: 'Success',
    });
  });

  it('should fail with null payload', async () => {
    const result = await addRFID(null);
    expect(result).toEqual(
      new Error('🚨 logTraceId undefined - incomplete payload'),
    );
  });

  it('should throw an error if the provider function fails', async () => {
    const error = new Error('Failed to insert rfid data');
    mockDefault.mockRejectedValueOnce(error);
    await expect(addRFID(addOrUnblockRFIDSuccessPayload)).rejects.toThrow(
      error,
    );
  });
});

describe('UnblockRFID', () => {
  it('fails if passed no payload', async () => {
    const res = await unblockRFID(null);
    expect(res).toStrictEqual(
      new Error('🚨 logTraceId undefined - incomplete payload'),
    );
  });

  it('should return a successful request response', async () => {
    const result = await unblockRFID(addOrUnblockRFIDSuccessPayload);
    expect(result).toEqual({
      status: 200,
      message: 'Success',
    });
  });

  it('should throw an error if the provider function fails', async () => {
    const error = new Error('Failed to insert rfid data');
    mockDefault.mockRejectedValueOnce(error);
    await expect(unblockRFID(addOrUnblockRFIDSuccessPayload)).rejects.toThrow(
      error,
    );
  });
});

describe('BlockRFID', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('should run successfully', async () => {
    const response = await blockRFID(blockRFIDSuccessPayload);
    expect(response).toEqual({
      status: 200,
      message: 'Success',
    });
  });

  it('blockRFID should result in error when has incomplete payload', async () => {
    const result = await blockRFID(null);
    expect(result).toEqual(incompletePayloadError);
  });

  it('should throw an error if the provider function fails', async () => {
    const error = new Error('Failed to insert rfid data');
    mockDefault.mockRejectedValueOnce(error);
    await expect(blockRFID(blockRFIDSuccessPayload)).rejects.toThrow(error);
  });
});

describe('fetchRfidDataByCardNumber', () => {
  it('passes the resolved payload', async () => {
    const result = await fetchRfidDataByCardNumber('ewrqweqwf');
    expect(result).toEqual('');
  });

  it('fail when the getEntriesByCardNumber got rejected', async () => {
    const err = new Error('Bad request');
    getEntriesByCardNumber.mockImplementationOnce(() => Promise.reject(err));
    await expect(fetchRfidDataByCardNumber('cardnumber')).rejects.toThrow(
      new Error(
        `fetchRfidDataByCardNumber call to Dynamo failed. Error ${err}`,
      ),
    );
  });

  it('fail on incomplete payload', async () => {
    const result = await fetchRfidDataByCardNumber(null);
    expect(result).toEqual(incompletePayloadError);
  });
});

describe('insertRfidData', () => {
  it('fail on incomplete payload', async () => {
    await expect(insertRfidData(null)).rejects.toThrow(incompletePayloadError);
  });
});

describe('updateRFIDInternal', () => {
  const mockSuccessPayload = {
    userId: 'mockUserId',
    country: Countries.DE,
    revenuePlan: [
      {
        provider: ChargeProviders.DCS,
        revenuePlanName: 'DCS-RevenuePlan',
      },
      {
        provider: ChargeProviders.HTB,
        revenuePlanName: 'HTB-RevenuePlan',
      },
    ],
  };
  const mockFailurePayload = {
    userId: 'mockUserId',
    country: Countries.DE,
    revenuePlan: [
      {
        provider: ChargeProviders.DCS,
        revenuePlanName: 'DCS-RevenuePlan',
      },
      {
        provider: ChargeProviders.DCS,
        revenuePlanName: 'DCS-RevenuePlan-2',
      },
    ],
  };
  const mockSuccessResult = {
    status: 200,
    message: 'Successfully updated RFID',
  };
  const mockFailureResult = {
    status: 500,
    message: 'Failed to update RFID',
  };
  const noHtbRevenuePlansResult = {
    status: 500,
    message: 'No HTB revenue plans available',
  };
  const noTagIdsResult = {
    status: 500,
    message: 'No physical-RFID tags available',
  };
  const noMatchingRevenuePlansResult = {
    status: 500,
    message: 'No tag matching the specified criteria was found',
  };

  const mockValidTagIds = [
    {
      tagId: '123',
      tagCategoryName: 'tagCategory',
      tagTypeName: 'tagType',
      tagNotes: 'virtual-HTB',
      tagStatus: 'ACTIVE',
      tagCardNumber: '123',
    },
    {
      tagId: '1235',
      tagCategoryName: 'tagCategory',
      tagTypeName: 'tagType',
      tagNotes: 'physical-RFID',
      tagStatus: 'ACTIVE',
      tagCardNumber: '1235',
    },
    {
      tagId: '1234',
      tagCategoryName: 'tagCategory',
      tagTypeName: 'tagType',
      tagNotes: 'virtual-DCS',
      tagStatus: 'NOT_SUPPORTED',
      tagCardNumber: '123',
    },
  ];

  const mockNotValidTagIds = [
    {
      tagId: '123',
      tagCategoryName: 'tagCategory',
      tagTypeName: 'tagType',
      tagNotes: 'virtual-DCS',
      tagStatus: 'ACTIVE',
      tagCardNumber: '123',
    },
    {
      tagId: '1234',
      tagCategoryName: 'tagCategory',
      tagTypeName: 'tagType',
      tagNotes: 'virtual-HTB',
      tagStatus: 'NOT_SUPPORTED_STATUS',
      tagCardNumber: '123',
    },
    {
      tagId: '1235',
      tagCategoryName: 'tagCategory',
      tagTypeName: 'tagType',
      tagNotes: 'physical-RFID',
      tagStatus: 'TERMINATED',
      tagCardNumber: '1235',
    },
  ];
  const mockDCSTagIds = [
    {
      tagId: '123',
      tagCategoryName: 'tagCategory',
      tagTypeName: 'tagType',
      tagNotes: 'virtual-DCS',
      tagStatus: 'ACTIVE',
      tagCardNumber: '123',
    },
    {
      tagId: '1234',
      tagCategoryName: 'tagCategory',
      tagTypeName: 'tagType',
      tagNotes: 'virtual-DCS',
      tagStatus: 'ACTIVE',
      tagCardNumber: '123',
    },
  ];

  it('should return error response if no tag ids are found', async () => {
    mockGetUserInfo.mockResolvedValueOnce({
      tagIds: [],
    });
    const res = await updateRFIDInternal(mockSuccessPayload, 'mockLogTraceId');
    expect(res).toEqual(noTagIdsResult);
  });

  it('should return success response if no valid tag ids are filtered and updated', async () => {
    mockGetUserInfo.mockResolvedValueOnce({
      tagIds: mockNotValidTagIds,
    });
    const res = await updateRFIDInternal(mockSuccessPayload, 'mockLogTraceId');
    expect(res).toEqual(mockSuccessResult);
    expect(mockUpdatePhysicalAuthMediaHtb).not.toHaveBeenCalled();
  });

  it('should return error response if no HTB revenue plans are found', async () => {
    mockGetUserInfo.mockResolvedValueOnce({
      tagIds: mockValidTagIds,
    });
    const res = await updateRFIDInternal(mockFailurePayload, 'mockLogTraceId');
    expect(res).toEqual(noMatchingRevenuePlansResult);
  });

  it('should return error response if no HTB revenue plans are found', async () => {
    mockGetUserInfo.mockResolvedValueOnce({
      tagIds: mockValidTagIds,
    });
    const res = await updateRFIDInternal(mockFailurePayload, 'mockLogTraceId');

    expect(res).toEqual(noMatchingRevenuePlansResult);
  });

  it('should call updatePhysicalAuthMediaHtb with the correct payload', async () => {
    mockGetUserInfo.mockResolvedValueOnce({
      tagIds: mockValidTagIds,
    });
    const expectedUpdatePhysicalAuthMediaHtbPayload = [
      mockValidTagIds[1].tagId,
      mockValidTagIds[1].tagStatus === TagStatus.ACTIVE ? '1' : '0',
      mockValidTagIds[1].tagCardNumber,
      'mockLogTraceId',
      undefined,
      mockSuccessPayload.revenuePlan[1].revenuePlanName,
    ];
    const res = await updateRFIDInternal(mockSuccessPayload, 'mockLogTraceId');

    expect(mockUpdatePhysicalAuthMediaHtb).toHaveBeenCalledWith(
      ...expectedUpdatePhysicalAuthMediaHtbPayload,
    );
  });

  it('Should return success status if the updatePhysicalAuthMediaHtb call is successful', async () => {
    mockGetUserInfo.mockResolvedValueOnce({
      tagIds: mockValidTagIds,
    });
    const res = await updateRFIDInternal(mockSuccessPayload, 'mockLogTraceId');
    expect(res).toEqual(mockSuccessResult);
  });

  it('should return failure status if updatePhysicalAuthMediaHtb return a status code other than 200', async () => {
    mockGetUserInfo.mockResolvedValueOnce({
      tagIds: mockValidTagIds,
    });
    mockUpdatePhysicalAuthMediaHtb.mockResolvedValueOnce({
      status: 500,
      message: 'updatePhysicalAuthMediaHtb error',
    });

    const res = await updateRFIDInternal(mockSuccessPayload, 'mockLogTraceId');

    expect(res).toEqual({
      message: 'Failed to update the following RFID tags: ["1235"]',
      status: 500,
    });
  });
});

describe('getLatestRequestIdentifier', () => {
  it('should return latest request identifier if promise resolves', async () => {
    const resolvedIdentifier = '00000001';
    mockGetLatestRequestIdentifier.mockResolvedValueOnce(resolvedIdentifier);
    const reqIdentifier = await getLatestRequestIdentifier('mockLogTraceId');
    expect(reqIdentifier).toEqual({
      latestRequestIdentifier: resolvedIdentifier,
    });
  });
  it('should return error message and status if getLatestRequestIdentifierDynamo rejects', async () => {
    mockGetLatestRequestIdentifier.mockRejectedValueOnce(
      new Error('dynamo error message'),
    );
    const result = await getLatestRequestIdentifier('mockLogTraceId');

    expect(result).toEqual({
      status: 500,
      message: 'Error getting latest request identifier: dynamo error message',
    });
  });
});
