import logger from '../utils/logger';
import {
  del,
  getEntriesByCardNumber,
  getEntriesByStatus,
  getEntriesByUserId,
  getLatestRequestIdentifierDynamo,
  insertRFIDEntry,
  updateEntriesByCardNumber,
} from './dynamo.client';

jest.mock('../env', () => ({ env: {} }));
jest.spyOn(logger, 'error').mockImplementation(jest.fn());

const mockPromise = jest.fn();
jest.mock('aws-sdk', () => ({
  config: {
    update: jest.fn(),
  },
  DynamoDB: {
    DocumentClient: jest.fn().mockImplementation(() => ({
      query: () => ({
        promise: mockPromise,
      }),
      put: () => ({
        promise: mockPromise,
      }),
      update: () => ({
        promise: mockPromise,
      }),
      delete: () => ({
        promise: mockPromise,
      }),
    })),
  },
}));

describe('RFID - database - dynamo', () => {
  describe('getEntriesByStatus', () => {
    const args = {
      countryCode: 'DE',
      status: 'ACTIVE',
    };
    it('should return an empty array if there are no items returned by the database', async () => {
      mockPromise.mockResolvedValueOnce({});
      const res = await getEntriesByStatus(args, 'logId');
      expect(res).toEqual({ data: [] });
    });

    it('should return the items array if returned by the database', async () => {
      mockPromise.mockResolvedValueOnce({ Items: ['data'] });
      const res = await getEntriesByStatus(args, 'logId');
      expect(res).toEqual({ data: ['data'] });
    });

    it('should throw an error if the database call fails', async () => {
      const err = new Error('database fails');
      mockPromise.mockRejectedValueOnce(err);
      await expect(getEntriesByStatus(args, 'logId')).rejects.toThrow(err);
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('getEntriesByCardNumber', () => {
    const args = {
      cardNumber: '1234',
    };
    it('should return an empty array if there are no items returned by the database', async () => {
      mockPromise.mockResolvedValueOnce({});
      const res = await getEntriesByCardNumber(args, 'logId');
      expect(res).toEqual({ data: [] });
    });

    it('should return the items array if returned by the database', async () => {
      mockPromise.mockResolvedValueOnce({ Items: ['data'] });
      const res = await getEntriesByCardNumber(args, 'logId');
      expect(res).toEqual({ data: ['data'] });
    });

    it('should throw an error if the database call fails', async () => {
      const err = new Error('database fails');
      mockPromise.mockRejectedValueOnce(err);
      await expect(getEntriesByCardNumber(args, 'logId')).rejects.toThrow(err);
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('insertRFIDEntry', () => {
    const args = {
      data: 'data',
    };

    it('should return the result of the put request to add the data', async () => {
      mockPromise.mockResolvedValueOnce('OK');
      const res = await insertRFIDEntry(args, 'logId');
      expect(res).toEqual('OK');
    });

    it('should throw an error if the database call fails', async () => {
      const err = new Error('database fails');
      mockPromise.mockRejectedValueOnce(err);
      await expect(insertRFIDEntry(args, 'logId')).rejects.toThrow(err);
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('updateEntriesByCardNumber', () => {
    const args = {
      cardNumber: '1234',
      status: 'ACTIVE',
    };

    it('should return the result of the put request to add the data', async () => {
      mockPromise.mockResolvedValueOnce('OK');
      const res = await updateEntriesByCardNumber(args, 'logId');
      expect(res).toEqual('OK');
    });

    it('should throw an error if the database call fails', async () => {
      const err = new Error('database fails');
      mockPromise.mockRejectedValueOnce(err);
      await expect(updateEntriesByCardNumber(args, 'logId')).rejects.toThrow(
        err,
      );
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('del', () => {
    const args = {
      cardNumber: '1234',
    };

    it('should return the result of the del request to remove the data', async () => {
      mockPromise.mockResolvedValueOnce('OK');
      const res = await del(args, 'logId');
      expect(res).toEqual('OK');
    });

    it('should throw an error if the database call fails', async () => {
      const err = new Error('database fails');
      mockPromise.mockRejectedValueOnce(err);
      await expect(del(args, 'logId')).rejects.toThrow(err);
      expect(logger.error).toHaveBeenCalled();
    });
  });
  describe('getLatestRequestIdentifierDynamo', () => {
    it('should return the request identifier of the latest item with an rfid status of CUSTOMER_REQUESTED', async () => {
      mockPromise.mockResolvedValueOnce({
        Items: [
          { request_identifier: '00000020', rfid_status: 'CUSTOMER_REQUESTED' },
        ],
      });

      const result = await getLatestRequestIdentifierDynamo();
      expect(result).toEqual('00000020');
    });
    it('should start a sequence from 1 if no items containing request identifier are available', async () => {
      mockPromise.mockResolvedValueOnce({
        Items: [],
      });

      const result = await getLatestRequestIdentifierDynamo();
      expect(result).toEqual('00000001');
    });
    it('should log and throw an error if the dynamo query throws an error', async () => {
      const error = new Error('dynamo query error');

      mockPromise.mockRejectedValueOnce(error);

      await expect(getLatestRequestIdentifierDynamo()).rejects.toThrow(error);
    });
  });
  describe('getEntriesByUserId', () => {
    const args = ['mockUserId', 'logId'];
    it('should return an empty array if there are no items returned by the dynamo query', async () => {
      mockPromise.mockResolvedValueOnce({});
      const res = await getEntriesByUserId(...args);
      expect(res).toEqual({ data: [] });
    });

    it('should return the items array if there are items returned by the dynamo query', async () => {
      mockPromise.mockResolvedValueOnce({ Items: ['data'] });
      const res = await getEntriesByUserId(...args);
      expect(res).toEqual({ data: ['data'] });
    });

    it('should throw an error if the database call fails', async () => {
      const err = new Error('database fails');
      mockPromise.mockRejectedValueOnce(err);
      await expect(getEntriesByUserId(...args)).rejects.toThrow(err);
      expect(logger.error).toHaveBeenCalled();
    });

    it(`should throw an error if the userId doesn't have a value`, async () => {
      const errorArgs = ['', 'logId'];
      await expect(getEntriesByUserId(...errorArgs)).rejects.toThrow('');
      expect(logger.error).toHaveBeenCalled();
    });
  });
});
