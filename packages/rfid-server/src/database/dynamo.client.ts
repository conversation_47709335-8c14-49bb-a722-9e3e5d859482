import { config, DynamoDB } from 'aws-sdk';
import type { ItemList, Key, QueryOutput } from 'aws-sdk/clients/dynamodb';

import { TagStatus } from '../common/enums';
import {
  DeleteRfidParamsObject,
  GetRfidParamsObjectCardNumber,
  GetRfidParamsObjectStatus,
  InsertItem,
  RfidQueryParameters,
  UpdateRfidParamsObject,
} from '../common/interfaces';
import { env } from '../env';
import logger from '../utils/logger';

const {
  DYNAMO_DB_URL,
  DYNAMO_DB_ACCESS_KEY_ID,
  DYNAMO_DB_ACCESS_KEY,
  RFID_DB_TABLE_NAME,
  DYNAMO_DB_REGION,
} = env;

config.update({
  region: DYNAMO_DB_REGION as string,
  secretAccessKey: DYNAMO_DB_ACCESS_KEY as string,
  accessKeyId: DYNAMO_DB_ACCESS_KEY_ID as string,
});

const dynamoClient = new DynamoDB.DocumentClient({
  endpoint: DYNAMO_DB_URL as string,
});

async function getRemainingDynamoEntries({
  limit,
  nextItems,
  items,
  lastEvaluatedKey,
  isLimitNotReached,
}: {
  limit: number | undefined;
  nextItems: QueryOutput;
  items: DynamoDB.DocumentClient.ItemList;
  lastEvaluatedKey: DynamoDB.DocumentClient.Key | undefined;
  isLimitNotReached: boolean;
}) {
  if (nextItems && (nextItems.Items as ItemList).length > 0) {
    items.push(...(nextItems.Items as ItemList));
    lastEvaluatedKey = nextItems?.LastEvaluatedKey;
    if (limit) {
      isLimitNotReached = limit - items.length > 0;
    }
  } else {
    lastEvaluatedKey = undefined;
  }
  return { lastEvaluatedKey, isLimitNotReached };
}

export const getEntriesByStatus = async (
  rfidParamsObject: GetRfidParamsObjectStatus,
): Promise<{ data: ItemList }> => {
  const { countryCode, status, exclusiveStartKey, limit } = rfidParamsObject;
  const ExpressionAttributeValues = countryCode
    ? {
        ':h': `${status}`,
        ':r': `${countryCode}`,
      }
    : {
        ':h': `${status}`,
      };
  const rfidQueryParameters: RfidQueryParameters = {
    TableName: RFID_DB_TABLE_NAME as string,
    IndexName: 'rfid_status_index',
    KeyConditionExpression: `rfid_status = :h ${
      countryCode ? 'and country = :r' : ''
    }`,
    ExpressionAttributeValues,
    ScanIndexForward: false,
    ...(limit ? { Limit: limit } : {}),
    ...(exclusiveStartKey ? { ExclusiveStartKey: exclusiveStartKey } : {}),
  };

  console.log(
    `GetEntriesByStatus with Dynamo params: ${JSON.stringify(
      rfidQueryParameters,
      null,
      2,
    )}`,
  );

  try {
    const res = await dynamoClient.query(rfidQueryParameters).promise();

    const items = res.Items || [];
    if (!items.length) {
      return { data: [] };
    }

    let lastEvaluatedKey = res.LastEvaluatedKey;
    let nextItems: QueryOutput;

    let isLimitNotReached = limit ? limit - items.length > 0 : true;
    while (lastEvaluatedKey && isLimitNotReached) {
      rfidQueryParameters.ExclusiveStartKey = lastEvaluatedKey;
      if (limit) {
        rfidQueryParameters.Limit = limit - items.length;
      }
      nextItems = await dynamoClient.query(rfidQueryParameters).promise();

      ({ lastEvaluatedKey, isLimitNotReached } =
        await getRemainingDynamoEntries({
          limit,
          nextItems,
          items,
          lastEvaluatedKey,
          isLimitNotReached,
        }));
    }

    console.log(
      'Total number of items returned by getEntriesByStatus',
      items.length,
    );
    return { data: items };
  } catch (error) {
    logger.error(`🚨 Error fetching RFID entries by status: ${error}`);
    throw error;
  }
};

export const getEntriesByCardNumber = async (
  rfidParamsObject: GetRfidParamsObjectCardNumber,
  logTraceId: string,
) => {
  const { cardNumber, exclusiveStartKey } = rfidParamsObject;
  const rfidQueryParameters: RfidQueryParameters = {
    TableName: RFID_DB_TABLE_NAME as string,
    KeyConditionExpression: 'rfid_card_number = :s',
    ExpressionAttributeValues: {
      ':s': `${cardNumber}`,
    },
    ScanIndexForward: false,
  };
  if (exclusiveStartKey) {
    rfidQueryParameters.ExclusiveStartKey = exclusiveStartKey as Key;
  }
  return dynamoClient
    .query(rfidQueryParameters)
    .promise()
    .then((res) => {
      return { data: res.Items || [] };
    })
    .catch((err) => {
      logger.error(
        `🚨 Error encountered: logTraceId ${logTraceId} Error fetching data from Dynamo ${RFID_DB_TABLE_NAME} table: ${err}`,
      );
      throw err;
    });
};

export const insertRFIDEntry = async (
  rfidItem: InsertItem,
  logTraceId: string,
) =>
  dynamoClient
    .put({
      TableName: RFID_DB_TABLE_NAME as string,
      Item: rfidItem,
    })
    .promise()
    .catch((err) => {
      logger.error(
        `🚨 Error encountered: logTraceId ${logTraceId} Error inserting data in Dynamo ${RFID_DB_TABLE_NAME} table: ${err}`,
      );
      throw err;
    });

export const updateEntriesByCardNumber = async (
  rfidParamsObject: UpdateRfidParamsObject,
  logTraceId: string,
) => {
  const { status, cardNumber, requestIdentifier } = rfidParamsObject;
  return dynamoClient
    .update({
      TableName: RFID_DB_TABLE_NAME as string,
      Key: { rfid_card_number: cardNumber },
      UpdateExpression: 'set #s = :x, #ri = :rri',
      ExpressionAttributeNames: {
        '#s': 'rfid_status',
        '#ri': 'request_identifier',
      },
      ExpressionAttributeValues: {
        ':x': status,
        ':rri': requestIdentifier,
      },
    })
    .promise()
    .catch((err) => {
      logger.error(
        `🚨 Error encountered: logTraceId ${logTraceId} Error updating data in Dynamo ${RFID_DB_TABLE_NAME} table: ${err}`,
      );
      throw err;
    });
};

export const del = async (
  rfidParamsObject: DeleteRfidParamsObject,
  logTraceId: string,
) => {
  const { cardNumber } = rfidParamsObject;
  return dynamoClient
    .delete({
      TableName: RFID_DB_TABLE_NAME as string,
      Key: { rfid_card_number: cardNumber },
    })
    .promise()
    .catch((err) => {
      logger.error(
        `🚨 Error encountered: logTraceId ${logTraceId} Error deleting data from Dynamo ${RFID_DB_TABLE_NAME} table: ${err}`,
      );
      throw err;
    });
};

export const getLatestRequestIdentifierDynamo = async (
  logTraceId: string,
): Promise<string> => {
  try {
    const queryParams = {
      TableName: RFID_DB_TABLE_NAME as string,
      IndexName: 'rfid_identifier_index',
      KeyConditionExpression: 'rfid_status=:s',
      ScanIndexForward: false,
      ExpressionAttributeValues: {
        ':s': TagStatus.CUSTOMER_REQUESTED,
      },
      Limit: 1,
    };
    const result = await dynamoClient.query(queryParams).promise();
    if (!result?.Items || !result.Items.length) {
      const sequenceStart = 1;
      return sequenceStart.toString().padStart(8, '0');
    } else {
      return result.Items[0].request_identifier;
    }
  } catch (err) {
    logger.error(
      `🚨 logTraceId ${logTraceId} - Error getting latest request identifier from dynamo`,
      { err },
    );
    throw err;
  }
};

export const getEntriesByUserId = async (
  userId: string,
  logTraceId: string,
) => {
  const rfidQueryParameters: RfidQueryParameters = {
    TableName: RFID_DB_TABLE_NAME as string,
    IndexName: 'user_entries_index',
    KeyConditionExpression: 'user_id = :s',
    ExpressionAttributeValues: {
      ':s': `${userId}`,
    },
    ScanIndexForward: false,
  };
  return dynamoClient
    .query(rfidQueryParameters)
    .promise()
    .then((res) => {
      return { data: res.Items || [] };
    })
    .catch((err) => {
      logger.error(
        `🚨 Error encountered: logTraceId ${logTraceId} Error get data from Dynamo ${RFID_DB_TABLE_NAME} table: ${err}`,
      );
      throw err;
    });
};
