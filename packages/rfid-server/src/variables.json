[{"name": "NODE_ENV", "source": "ado-variable-group", "sourceName": "NODE_ENV"}, {"name": "BPCM_AWS_SERVICES_URL", "source": "ado-variable-group", "sourceName": "BPCM_AWS_SERVICES_URL"}, {"name": "APOLLO_INTERNAL_USER_ID", "source": "ado-variable-group", "sourceName": "APOLLO_INTERNAL_USER_ID"}, {"name": "PRIVATE_GATEWAY_CLUSTER_URL", "source": "ado-variable-group", "sourceName": "PRIVATE_GATEWAY_CLUSTER_URL"}, {"name": "DCS_FLEET_SERVICES_URL", "source": "ado-variable-group", "sourceName": "DCS_FLEET_SERVICES_URL"}, {"name": "RFID_DB_TABLE_NAME", "source": "ado-variable-group", "sourceName": "RFID_DB_TABLE_NAME"}, {"name": "DYNAMO_DB_ACCESS_KEY_ID", "source": "local", "sourceName": "DYNAMO_DB_ACCESS_KEY_ID"}, {"name": "DYNAMO_DB_ACCESS_KEY", "source": "local", "sourceName": "DYNAMO_DB_ACCESS_KEY"}, {"name": "DYNAMO_DB_URL", "source": "ado-variable-group", "sourceName": "DYNAMO_DB_URL"}, {"name": "DYNAMO_DB_REGION", "source": "ado-variable-group", "sourceName": "DYNAMO_DB_REGION"}, {"name": "FLEET_GROUP_DE", "source": "ado-variable-group", "sourceName": "FLEET_GROUP_DE"}, {"name": "FLEET_GROUP_NL", "source": "ado-variable-group", "sourceName": "FLEET_GROUP_NL"}, {"name": "FLEET_GROUP_ES", "source": "ado-variable-group", "sourceName": "FLEET_GROUP_ES"}, {"name": "COMPANY_CODE_DE", "source": "ado-variable-group", "sourceName": "COMPANY_CODE_DE"}, {"name": "COMPANY_CODE_NL", "source": "ado-variable-group", "sourceName": "COMPANY_CODE_NL"}, {"name": "COMPANY_CODE_ES", "source": "ado-variable-group", "sourceName": "COMPANY_CODE_ES"}, {"name": "HTB_SERVICES_REST", "source": "ado-variable-group", "sourceName": "HTB_SERVICES_REST"}, {"name": "DCS_TOKEN_CLIENT_ID", "source": "ado-variable-group", "sourceName": "DCS_TOKEN_CLIENT_ID"}, {"name": "DCS_TOKEN_CLIENT_SECRET", "source": "ado-variable-group", "sourceName": "DCS_TOKEN_CLIENT_SECRET"}, {"name": "DCS_TOKEN_RESOURCE", "source": "ado-variable-group", "sourceName": "DCS_TOKEN_RESOURCE"}, {"name": "DCS_TOKEN_URL", "source": "ado-variable-group", "sourceName": "DCS_TOKEN_URL"}, {"name": "HTB_NL_ENTITY_ID", "source": "ado-variable-group", "sourceName": "HTB_NL_ENTITY_ID"}, {"name": "HTB_DE_ENTITY_ID", "source": "ado-variable-group", "sourceName": "HTB_DE_ENTITY_ID"}, {"name": "HTB_ES_ENTITY_ID", "source": "ado-variable-group", "sourceName": "HTB_ES_ENTITY_ID"}, {"name": "TOKEN_ELASTICACHE_HOST", "source": "ado-variable-group", "sourceName": "TOKEN_ELASTICACHE_HOST"}, {"name": "ELASTICACHE_PORT", "source": "ado-variable-group", "sourceName": "ELASTICACHE_PORT"}, {"name": "AWS_REGION", "source": "ado-variable-group", "sourceName": "AWS_REGION"}, {"name": "DCS_TOKEN_REFRESH_LAMBDA_NAME", "source": "ado-variable-group", "sourceName": "DCS_TOKEN_REFRESH_LAMBDA_NAME"}, {"name": "HTB_NL_CONTACT", "source": "ado-variable-group", "sourceName": "HTB_NL_CONTACT"}, {"name": "HTB_DE_CONTACT", "source": "ado-variable-group", "sourceName": "HTB_DE_CONTACT"}, {"name": "HTB_ES_CONTACT", "source": "ado-variable-group", "sourceName": "HTB_ES_CONTACT"}, {"name": "BPCM_AWS_SERVICES_KEY", "source": "aws-secrets-manager", "sourceName": "Backend-Bpcm-ApiKey"}, {"name": "APOLLO_INTERNAL_SECRET", "source": "aws-secrets-manager", "sourceName": "Rfid-ApolloInternal-Secret"}, {"name": "HTB_API_KEY", "source": "aws-secrets-manager", "sourceName": "Rfid-Htb-ApiKey"}, {"name": "DCS_SUBSCRIPTION_KEY", "source": "aws-secrets-manager", "sourceName": "Rfid-DcsSubscription-ApiKey"}, {"name": "DCS_FLEET_ACCESS_KEY", "source": "aws-secrets-manager", "sourceName": "Rfid-DcsFleet-AccessKey"}, {"name": "OPEN_ID_ACCESS_ROLE", "source": "ado-variable-group", "sourceName": "OPEN_ID_ACCESS_ROLE"}, {"name": "ENABLE_HTB_UK", "source": "ado-variable-group", "sourceName": "ENABLE_HTB_UK"}, {"name": "COMPANY_CODE_UK", "source": "ado-variable-group", "sourceName": "COMPANY_CODE_UK"}, {"name": "OCPI_IDENTIFIER", "source": "ado-variable-group", "sourceName": "OCPI_IDENTIFIER"}, {"name": "FLEET_GROUP_UK", "source": "ado-variable-group", "sourceName": "FLEET_GROUP_UK"}, {"name": "HTB_UK_ENTITY_ID", "source": "ado-variable-group", "sourceName": "HTB_UK_ENTITY_ID"}, {"name": "ENABLE_DE_XBORDER_RFID", "source": "ado-variable-group", "sourceName": "ENABLE_DE_XBORDER_RFID"}]