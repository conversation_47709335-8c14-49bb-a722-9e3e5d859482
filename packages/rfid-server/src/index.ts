// Open tracing libs
import 'opentracing';

import compression from 'compression';
import cors from 'cors';
import express, { json, urlencoded } from 'express';
import helmet from 'helmet';
import { createServer } from 'http';

import rfidRoutes from './api/routes/rfid';
import initialiseApolloServer from './apolloServer';
import { env } from './env';
import logger from './utils/logger';

const app = express();
const server = createServer(app);

// Express config
app.use(compression());
app.use(
  helmet({
    contentSecurityPolicy: env.NODE_ENV === 'production' ? undefined : false,
  }),
);
app.use(cors());
app.use(urlencoded({ extended: true, limit: '10mb' }));
app.use(json({ limit: '10mb' }));

// Expose API routes
app.use('/api/', rfidRoutes);

// Initialise Apollo server
initialiseApolloServer(app, server);

server.listen({ port: 4017 }, () => {
  logger.info(`🚀 RFID server ready on 4017`);
});

// Shut down in the case of interrupt and termination signals
['SIGINT', 'SIGTERM'].forEach((signal) => {
  process.on(signal, () => server.close());
});
