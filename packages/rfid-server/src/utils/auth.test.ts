import { isInternal, isUserRolePermitted, userAuthorised } from './auth';

const APOLLO_INTERNAL = 'APOLLO-INTERNAL';

jest.mock('../env', () => ({
  env: {
    APOLLO_INTERNAL_USER_ID: 'APOLLO-INTERNAL',
    OPEN_ID_ACCESS_ROLE: 'ACCESS_ROLE',
  },
}));

describe('auth', () => {
  describe('isUserRolePermitted', () => {
    it('should return true if the user role is authorised', () => {
      expect(isUserRolePermitted(['ACCESS_ROLE'])).toBe(true);
    });

    it('should return false if the user role is not authorised', () => {
      expect(isUserRolePermitted(['ACCESS'])).toBe(false);
    });
  });

  describe('isInternal', () => {
    it('should return true if the user is internal', () => {
      expect(isInternal(APOLLO_INTERNAL)).toBe(true);
    });

    it('should return false if the user is not internal', () => {
      expect(isInternal('APOLLO')).toBe(false);
    });
  });

  describe('userAuthorised', () => {
    it('should return true if the user is authorised', () => {
      expect(userAuthorised(APOLLO_INTERNAL, APOLLO_INTERNAL)).toBe(true);
    });

    it('should return false if the user is not authorised', () => {
      expect(userAuthorised('APOLLO', APOLLO_INTERNAL)).toBe(false);
    });
  });
});
