import AWS, { AWSError, config } from 'aws-sdk';

import { env } from '../env';
import logger from './logger';

// set AWS to log its calls to the console
config.logger = console;

const lambda = new AWS.Lambda({
  region: env.AWS_REGION,
});

export default async (name: string | undefined, request?: unknown) => {
  let response;
  try {
    const params = {
      FunctionName: name as string,
      Payload: JSON.stringify(request),
    };
    response = await lambda.invoke(params).promise();
  } catch (excp) {
    // we want to specifically catch throttling errors
    if ((excp as AWSError).code === 'TooManyRequestsException') {
      return {
        status: 'Retry',
        message: `Lambda Throttle: '${name}​​'`,
      };
    }

    // all other errors are handled generically
    return {
      status: 'Apollo Error',
      message: `LambdaCallError:'${name}​​':code'${
        (excp as AWSError).code
      }​​':message'${(excp as AWSError).message}​​'`,
    };
  }

  try {
    // we always expect a 200 status if the function managed to start
    if (response.StatusCode !== 200) {
      return {
        status: 'ApolloError',
        message: `LambdaUnexpectedStatus:'${name}​​':message'Lambdareturnedstatus${response.StatusCode}​​'`,
      };
    }
    // presence of this implies a runtime error has occured, timeout, memory, unhandled exception
    if (response.FunctionError) {
      const errorDetails = JSON.parse(response.Payload as string);
      // have to check the error message to see if it is a time out ... not ideal
      if (errorDetails.errorMessage.includes('Task timed out')) {
        return {
          status: 'Retry',
          message: `LambdaTimeout:'${name}​​':message'${errorDetails.errorMessage}​​'`,
        };
      }
      return {
        status: 'ApolloError',
        message: `LambdaRuntimeError:'${name}​​':message:'${errorDetails.errorMessage}​​'`,
      };
    }

    logger.info('response.Payload', response.Payload);
    return JSON.parse(response.Payload as string);
  } catch (excp) {
    // this is to catch any errors in processing the response
    return {
      status: 'ApolloError',
      message: `LambdaCallError:'${name}​​':code'${
        (excp as AWSError).code
      }​​':error'${(excp as AWSError).message}​​'`,
    };
  }
};
