import { env } from '../env';

// Apollo Internal Id, for validating internal service calls
const {
  APOLLO_INTERNAL_USER_ID = 'APOLLO-INTERNAL',
  OPEN_ID_ACCESS_ROLE = 'ACCESS_ROLE',
} = env;

/**
 * Authorises the request user Id matches the authenticated userId
 * @param requestUserId the request payload userId
 * @param tokenUserId the authenticated userId
 */
export const userAuthorised = (requestUserId: string, tokenUserId: string) =>
  tokenUserId === requestUserId;

/**
 * Checks if a given user Id matches the internal user identifier
 * @param userId user Id to check matches internal identifier
 */
export const isInternal = (userId: string) =>
  userId === APOLLO_INTERNAL_USER_ID;

export const isUserRolePermitted = (roles: (string | null)[]): boolean => {
  if (!roles) {
    return false;
  }
  return roles?.some(
    (role: string | null) => role?.toUpperCase() === OPEN_ID_ACCESS_ROLE,
  );
};
