// Federated schema for gateway server
import { buildFederatedSchema } from '@apollo/federation';
// Opentracing dependencies
import OpentracingExtension from 'apollo-opentracing';
// Apollo server landing pages
import {
  ApolloServerPluginDrainHttpServer,
  ApolloServerPluginLandingPageDisabled,
  ApolloServerPluginLandingPageGraphQLPlayground,
} from 'apollo-server-core';
// GraphQL server dependencies
import { ApolloServer, gql } from 'apollo-server-express';
import type { Application } from 'express';
import { Server } from 'http';
import path from 'path';
// Glue used for matching resolvers with schema definitions
import glue from 'schemaglue';

import type {
  ApolloContextParams,
  RfidServerContext,
} from './common/interfaces';
// Get env vars
import { env } from './env';
// Initialise logging tooling
import logger from './utils/logger';
import initTracer from './utils/tracer';

// Glue schemas/resolvers together
const baseDir = path.resolve(__dirname, '..');
const schemaDir = path.resolve(baseDir, 'schema');
const resolverDir = path.relative(schemaDir, __dirname);

const { schema, resolver } = glue(schemaDir, {
  mode: `${resolverDir}/**/*.[jt]s`,
  ignore: `${resolverDir}/**/*.test.[jt]s`,
});

// Initialise opentracing and extension
const serverTracer = initTracer('@bp/feature-rfid/server');
const localTracer = initTracer('@bp/feature-rfid/local');

// @ts-expect-error resolved with x-ray
const tracer = new OpentracingExtension({
  server: serverTracer,
  local: localTracer,
});

// Set the Apollo server landing page
const landingPage =
  env.NODE_ENV === 'production'
    ? ApolloServerPluginLandingPageDisabled()
    : ApolloServerPluginLandingPageGraphQLPlayground();

// Initialise Apollo server
const initialiseApolloServer = (app: Application, httpServer: Server): void => {
  const server = new ApolloServer({
    schema: buildFederatedSchema({
      typeDefs: gql(schema),
      resolvers: resolver,
    }),
    plugins: [
      tracer,
      landingPage,
      ApolloServerPluginDrainHttpServer({
        httpServer,
      }),
    ],
    logger,
    context: async ({
      connection,
      req,
    }: ApolloContextParams): Promise<RfidServerContext> => {
      // Filter out subscriptions
      if (connection) {
        return connection.context;
      }

      // Extract user Id from federation servers forwarded headers
      const userId = req?.headers['x-apollo-user-id'] || null;
      // Extract federation server log trace id
      const logTraceId = req?.headers['x-log-trace-id'] || 'unspecified';
      const roles = req?.headers['x-apollo-roles']?.split(',');

      // Apply context to request
      return {
        userId: userId as string,
        logTraceId,
        roles: roles as string[],
        jaegersession: req?.headers.jaegersession,
        jaegerrequest: req?.headers.jaegerrequest,
      };
    },
  });

  // Middleware: GraphQL
  server
    .start()
    .then(() =>
      server.applyMiddleware({
        app: app as Parameters<typeof server.applyMiddleware>[0]['app'],
      }),
    )
    .catch((err: Error) => {
      console.error(err);
      throw err;
    });
};

export default initialiseApolloServer;
