import axios, { AxiosInstance } from 'axios';

import { env } from '../../env';
import oAuthResponse from './__mock-responses__/oauth.json';
import { blockDcsRfidCard, fetchOAuthToken } from './api';
import { DcsBlockRfidInput } from './types';

jest.mock('axios');
jest.mock('../../env');

const mockedAxios = axios as jest.Mocked<typeof axios>;
mockedAxios.create.mockReturnValue({
  ...axios,
  interceptors: {
    request: {
      use: jest.fn(),
    },
  },
} as unknown as AxiosInstance);

beforeEach(() => {
  jest.clearAllMocks();
});

describe('Dcs Api', () => {
  describe('fetchOAuthToken()', () => {
    it('calls DCS token URL with correct params and headers', async () => {
      mockedAxios.post.mockResolvedValueOnce({
        status: 200,
        data: oAuthResponse,
      });

      const result = await fetchOAuthToken();

      // Assert return
      expect(result).toEqual(oAuthResponse);

      // Assert axios call
      expect(mockedAxios.post).toHaveBeenCalledWith(
        env.DCS_TOKEN_URL,
        expect.any(URLSearchParams),
        { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } },
      );

      // Assert URLSearchParams includes required keys
      const params = (
        mockedAxios.post.mock.calls[0][1] as URLSearchParams
      ).toString();
      expect(params).toContain(`client_id=${env.DCS_TOKEN_CLIENT_ID}`);
      expect(params).toContain(`client_secret=${env.DCS_TOKEN_CLIENT_SECRET}`);
      expect(params).toContain(`grant_type=client_credentials`);
      expect(params).toContain(
        `resource=${encodeURIComponent(env.DCS_TOKEN_RESOURCE)}`,
      );
    });

    it('throws if axios.post fails', async () => {
      mockedAxios.post.mockRejectedValueOnce('Network Fail');

      await expect(fetchOAuthToken()).rejects.toEqual('Network Fail');
    });
  });

  describe('blockDcsRfidCard()', () => {
    const input: DcsBlockRfidInput = {
      fleetGroupId: 'ARALME',
      contractId: 'DE*DCS*123456*7',
      cardNumber: 'NLBPECK6Z6BJ1',
      logTraceId: 'trace-123',
    };

    it('calls axios.put with the correct URL', async () => {
      mockedAxios.put.mockResolvedValueOnce({ status: 200 });

      await blockDcsRfidCard(input);

      expect(mockedAxios.put).toHaveBeenCalledWith(
        `/fleetgroups/${input.fleetGroupId}/contracts/${input.contractId}/cards/${input.cardNumber}/block`,
      );
    });

    it('re-throws errors', async () => {
      mockedAxios.put.mockRejectedValueOnce('Network Fail');

      await expect(blockDcsRfidCard(input)).rejects.toEqual('Network Fail');
    });
  });
});
