import axios, { AxiosError, AxiosInstance } from 'axios';

import { env } from '../../env';
import logger from '../../utils/logger';
import { DcsBlockRfidInput, DcsOAuthResponse } from './types';

const httpClient = (() => {
  let instance: AxiosInstance;

  return () => {
    if (!instance) {
      instance = axios.create({
        baseURL: env.DCS_FLEET_SERVICES_URL,
        headers: {
          'fleet-access-key': env.DCS_FLEET_ACCESS_KEY,
          'Ocp-Apim-Subscription-Key': env.DCS_SUBSCRIPTION_KEY,
          'Content-Type': 'application/json',
        },
      });

      instance.interceptors.request.use(async (config) => {
        const result = await fetchOAuthToken();
        config.headers = config.headers || {};
        config.headers.Authorization = `Bearer ${result.access_token}`;

        return config;
      });
    }

    return instance;
  };
})();

export async function fetchOAuthToken(): Promise<DcsOAuthResponse> {
  const headers = {
    'Content-Type': 'application/x-www-form-urlencoded',
  };

  const params = new URLSearchParams({
    client_id: env.DCS_TOKEN_CLIENT_ID,
    client_secret: env.DCS_TOKEN_CLIENT_SECRET,
    grant_type: 'client_credentials',
    resource: env.DCS_TOKEN_RESOURCE,
  });

  const response = await axios.post<DcsOAuthResponse>(
    env.DCS_TOKEN_URL,
    params,
    { headers },
  );

  return response.data;
}

function handleDcsApiError(
  context: string,
  error: Error,
  logTraceId: string,
): never {
  const err = error as AxiosError;

  if (err.response) {
    const { status, statusText, data } = err.response;
    logger.error(
      `[${logTraceId}] ${context} => DCS API failed with status (${status} - ${statusText}): ${data}`,
    );
  } else {
    logger.error(
      `[${logTraceId}] ${context} => Network/Unknown error: ${err.message}`,
    );
  }

  throw error;
}

export async function blockDcsRfidCard(
  input: DcsBlockRfidInput,
): Promise<void> {
  const { fleetGroupId, contractId, cardNumber, logTraceId } = input;

  try {
    const endpoint = `/fleetgroups/${fleetGroupId}/contracts/${contractId}/cards/${cardNumber}/block`;

    logger.debug(
      `[${logTraceId}] Blocking RFID card number ${cardNumber} within DCS, associated to contract ${contractId}, and fleet group ${fleetGroupId}`,
    );

    await httpClient().put<void>(endpoint);

    logger.debug(
      `[${logTraceId}] Successfully blocked RFID card number ${cardNumber} within DCS, associated to contract ${contractId}`,
    );
  } catch (err) {
    const error = err as Error;

    handleDcsApiError('blockDcsRfidCard', error, logTraceId);
  }
}
