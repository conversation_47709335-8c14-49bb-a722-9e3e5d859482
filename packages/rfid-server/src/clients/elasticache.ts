import IORedis from 'ioredis';

import { env } from '../env';
import logger from '../utils/logger';

const tokenCacheClientConnection = {
  host: env.TOKEN_ELASTICACHE_HOST,
  port: env.ELASTICACHE_PORT ? parseInt(env.ELASTICACHE_PORT, 10) : 6379,
  tls: env.NODE_ENV !== 'local' ? {} : undefined,
};

const tokenCache = new IORedis(tokenCacheClientConnection);

tokenCache.on('error', (e) =>
  logger.error(
    '🚨 Error encountered: <PERSON><PERSON> has encountered an error with token cache',
    e,
  ),
);

/**
 * Sets an item on the cache using the key and return boolean if success/conflict
 * @param key the salesforce userId
 * @param payload payload to be stored for the key
 * @param expiry expiry time of the item being set in seconds, default 5 minutes
 */
export const set = async (
  key: string,
  payload: string,
  expiry = 1800,
): Promise<boolean> =>
  tokenCache
    .set(key, payload, 'EX', expiry)
    .then((res: string | null): boolean => res === 'OK')
    .catch((e) => {
      throw e;
    });

/**
 * Get an item from the cache for the specified key
 * @param key value of the key to be returned from the cache
 */
export const get = async (key: string): Promise<string | null> =>
  tokenCache
    .get(key)
    .then((res: string | null) => {
      if (res) {
        return JSON.parse(res);
      }
      return null;
    })
    .catch((e) => {
      throw e;
    });
