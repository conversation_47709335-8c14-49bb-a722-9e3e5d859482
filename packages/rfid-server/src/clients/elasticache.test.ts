import IORedis from 'ioredis';

import { get, set } from './elasticache';

jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    set: jest.fn(),
    on: jest.fn(),
    quit: jest.fn(),
  }));
});

jest.mock('../env', () => ({ env: {} }));

const MockedIORedis = IORedis as jest.MockedClass<typeof IORedis>;

const mockInstance = MockedIORedis.mock.results[0]?.value as {
  get: jest.Mock<Promise<string | null | undefined>, [key: string]>;
  set: jest.Mock<
    Promise<string | null>,
    [key: string, value: string, mode: string, duration: number]
  >;
  on: jest.Mock<
    IORedis,
    [event: string, listener: (...args: unknown[]) => void]
  >;
  quit: jest.Mock<Promise<string>, []>;
};

const mockGet = mockInstance.get;
const mockSet = mockInstance.set;
const mockQuit = mockInstance.quit;

describe('user server | elasticache', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterAll(async () => {
    mockQuit.mockResolvedValue('OK');
  });

  describe('get', () => {
    it('should get a value from the elasticache instance and parse it', async () => {
      mockGet.mockResolvedValue('{ "jwt": "token", "status": "Pending"}');

      const res = await get('key');

      expect(res).toStrictEqual({ jwt: 'token', status: 'Pending' });
      expect(mockGet).toHaveBeenCalledWith('key');
    });

    it('should throw an error if the get request fails', async () => {
      const error = new Error('failed');
      mockGet.mockRejectedValue(error);

      await expect(get('key')).rejects.toThrow(error);
      expect(mockGet).toHaveBeenCalledWith('key');
    });

    it('should return null if unable to find data for key', async () => {
      mockGet.mockResolvedValue(undefined);

      const res = await get('key');

      expect(res).toBe(null);
      expect(mockGet).toHaveBeenCalledWith('key');
    });

    it('should return null if get returns null', async () => {
      mockGet.mockResolvedValue(null);

      const res = await get('key');

      expect(res).toBe(null);
      expect(mockGet).toHaveBeenCalledWith('key');
    });

    it('should handle JSON parse errors gracefully', async () => {
      mockGet.mockResolvedValue('invalid json {');

      await expect(get('key')).rejects.toThrow();
      expect(mockGet).toHaveBeenCalledWith('key');
    });
  });

  describe('set', () => {
    it('should set a value in the redis cache with default expiry (1800 seconds)', async () => {
      mockSet.mockResolvedValue('OK');

      const res = await set('key', 'payload');

      expect(res).toBe(true);
      expect(mockSet).toHaveBeenCalledWith('key', 'payload', 'EX', 1800);
    });

    it('should set a value in the redis cache with a specified expiry', async () => {
      mockSet.mockResolvedValue('OK');

      const res = await set('key', 'payload', 500);

      expect(res).toBe(true);
      expect(mockSet).toHaveBeenCalledWith('key', 'payload', 'EX', 500);
    });

    it('should return false when set operation does not return OK', async () => {
      mockSet.mockResolvedValue('FAIL');

      const res = await set('key', 'payload');

      expect(res).toBe(false);
      expect(mockSet).toHaveBeenCalledWith('key', 'payload', 'EX', 1800);
    });

    it('should return false when set operation returns null', async () => {
      mockSet.mockResolvedValue(null);

      const res = await set('key', 'payload');

      expect(res).toBe(false);
      expect(mockSet).toHaveBeenCalledWith('key', 'payload', 'EX', 1800);
    });

    it('should throw an error if setting the cache fails', async () => {
      const error = new Error('fail');
      mockSet.mockRejectedValue(error);

      await expect(set('key', 'payload')).rejects.toThrow(error);
      expect(mockSet).toHaveBeenCalledWith('key', 'payload', 'EX', 1800);
    });

    it('should handle custom expiry correctly', async () => {
      mockSet.mockResolvedValue('OK');

      const res = await set('user123', '{"token": "abc123"}', 3600);

      expect(res).toBe(true);
      expect(mockSet).toHaveBeenCalledWith(
        'user123',
        '{"token": "abc123"}',
        'EX',
        3600,
      );
    });

    it('should handle zero expiry', async () => {
      mockSet.mockResolvedValue('OK');

      const res = await set('key', 'payload', 0);

      expect(res).toBe(true);
      expect(mockSet).toHaveBeenCalledWith('key', 'payload', 'EX', 0);
    });

    it('should handle string payload correctly', async () => {
      mockSet.mockResolvedValue('OK');

      const res = await set('key', 'simple string payload');

      expect(res).toBe(true);
      expect(mockSet).toHaveBeenCalledWith(
        'key',
        'simple string payload',
        'EX',
        1800,
      );
    });
  });
});
