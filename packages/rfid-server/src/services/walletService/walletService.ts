import { gql, request } from 'graphql-request';

import { env } from '../../env';
import logger from '../../utils/logger';

const {
  PRIVATE_GATEWAY_CLUSTER_URL = 'http://localhost:4030',
  APOLLO_INTERNAL_SECRET = 'secret',
  APOLLO_INTERNAL_USER_ID = 'APOLLO-INTERNAL',
} = env;

// Query definiton for getting cached charge session data by user ID from Charge micro-service
const GET_PAYMENT_METHODS = gql`
  query getPaymentMethodsWallet($userId: String!) {
    getPaymentMethodsWallet(userId: $userId) {
      cardholderName
      paymentMethodId
      default
    }
  }
`;

/**
 * Retreive the payment methods from the wallet server
 * @param userId userId to query by
 */
export const paymentMethodsWallet = async (
  userId: string,
  logTraceId: string,
) =>
  request(
    PRIVATE_GATEWAY_CLUSTER_URL,
    GET_PAYMENT_METHODS,
    {
      userId,
    },
    {
      'x-apollo-internal-secret': APOLLO_INTERNAL_SECRET,
      'x-apollo-user-id': APOLLO_INTERNAL_USER_ID,
    },
  )
    .then(({ getPaymentMethodsWallet }) => getPaymentMethodsWallet)
    .catch((e) => {
      logger.error(
        `🚨 Error encountered: logTraceId ${logTraceId} - getPaymentMethodsWallet failed to fetch Payment Methods from the Wallet`,
        {
          userId,
          error: e,
        },
      );
      throw new Error('Failed to fetch Payment Method from wallet service');
    });
