import { paymentMethodsWallet } from './walletService';
import logger from '../../utils/logger';
import * as gqlRequest from 'graphql-request';

const mockRequest = jest.spyOn(gqlRequest, 'request');
jest.mock('../../env', () => ({ env: {} }));
jest.spyOn(logger, 'error').mockImplementation(jest.fn());

describe('RFID - services - walletService', () => {
  describe('paymentMethodsWallet', () => {
    it('should return destructured results object on success', async () => {
      mockRequest.mockResolvedValue({
        getPaymentMethodsWallet: {
          status: 200,
        },
      });
      const res = await paymentMethodsWallet({ userId: 'userId' }, 'logId');
      expect(res).toHaveProperty('status', 200);
    });

    it('should throw if the gql request fails', async () => {
      mockRequest.mockRejectedValue(new Error('NETWORK ERROR'));
      await expect(
        paymentMethodsWallet({ userId: 'userId' }, 'logId'),
      ).rejects.toThrow(
        new Error('Failed to fetch Payment Method from wallet service'),
      );
    });
  });
});
