import { request } from 'graphql-request';

import {
  AddNewTokenResponse,
  IAddNewToken,
  UpdateTokenInput,
  UpdateTokenResponse,
} from '../../common/interfaces';
import { env } from '../../env';
import logger from '../../utils/logger';
import { ADD_NEW_TOKEN, UPDATE_TOKEN } from './ocpiService.queries';

const {
  PRIVATE_GATEWAY_CLUSTER_URL = '',
  APOLLO_INTERNAL_SECRET = '',
  APOLLO_INTERNAL_USER_ID = '',
} = env;

export const updateToken = async (
  data: UpdateTokenInput,
  logTraceId: string,
) => {
  return request<UpdateTokenResponse>(
    PRIVATE_GATEWAY_CLUSTER_URL,
    UPDATE_TOKEN,
    { data },
    {
      'x-apollo-internal-secret': APOLLO_INTERNAL_SECRET,
      'x-apollo-user-id': APOLLO_INTERNAL_USER_ID,
    },
  )
    .then(({ updateToken }) => {
      if (updateToken.statusCode !== 200) {
        logger.error(
          `🚨 Error encountered: logTraceId ${logTraceId} - Error calling updateTokenStatus mutation: returned status ${updateToken.statusCode}`,
        );
        throw new Error(
          `Error calling updateToken mutation: returned status ${updateToken.statusCode}`,
        );
      }
      return { status: 200, message: 'Success' };
    })
    .catch((e) => {
      logger.error(
        `🚨 Error encountered: logTraceId ${logTraceId} - Error calling updateTokenStatus mutation: ${e}`,
      );
      throw e;
    });
};

export const addNewToken = async (data: IAddNewToken, logTraceId: string) => {
  return request<AddNewTokenResponse>(
    PRIVATE_GATEWAY_CLUSTER_URL,
    ADD_NEW_TOKEN,
    {
      ...data,
    },
    {
      'x-apollo-internal-secret': APOLLO_INTERNAL_SECRET,
      'x-apollo-user-id': APOLLO_INTERNAL_USER_ID,
    },
  )
    .then(({ addNewToken }) => {
      if (addNewToken.statusCode !== 200) {
        logger.error(
          `🚨 Error encountered: logTraceId ${logTraceId} - Error calling addNewTokenStatus mutation: returned status ${addNewToken.statusCode}`,
        );
        throw new Error(
          `Error calling addNewToken mutation: returned status ${addNewToken.statusCode}`,
        );
      }
      return { status: 200, message: 'Success' };
    })
    .catch((e) => {
      logger.error(
        `🚨 Error encountered: logTraceId ${logTraceId} - Error calling addNewToken mutation: ${e}`,
      );
      throw e;
    });
};
