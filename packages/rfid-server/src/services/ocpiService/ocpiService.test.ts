import * as gqlRequest from 'graphql-request';

import logger from '../../utils/logger';
import { addNewToken, updateToken } from './ocpiService';

const mockRequest = jest.spyOn(gqlRequest, 'request');
jest.mock('../../env', () => ({ env: {} }));
jest.spyOn(logger, 'error').mockImplementation(jest.fn());

describe('ocpiService', () => {
  describe('updateToken', () => {
    const data = {
      ocpiIdentifier: '',
      uid: '',
      valid: false,
    };
    it('should return a success status object if successful', async () => {
      mockRequest.mockResolvedValue({
        updateToken: {
          statusCode: 200,
        },
      });
      const res = await updateToken(data, 'logId');
      expect(res).toHaveProperty('status', 200);
    });

    it('should throw an error if the returned status is not "200"', async () => {
      mockRequest.mockResolvedValue({
        updateToken: {
          statusCode: 400,
        },
      });
      await expect(updateToken(data, 'logId')).rejects.toThrow(
        new Error('Error calling updateToken mutation: returned status 400'),
      );
    });

    it('should throw an error if the call to the ocpi service fails', async () => {
      const error = new Error('failed request');
      mockRequest.mockRejectedValue(error);
      await expect(updateToken(data, 'logId')).rejects.toThrow(error);
    });
  });
  describe('addNewToken', () => {
    const data = {
      authId: 'authId',
      ocpiIdentifier: 'ocpiIdentifier',
      type: 'type',
      uid: 'uid',
    };
    it('should return a success status object if successful', async () => {
      mockRequest.mockResolvedValue({
        addNewToken: {
          statusCode: 200,
        },
      });
      const res = await addNewToken(data, 'logId');
      expect(res).toHaveProperty('status', 200);
    });

    it('should throw an error if the returned status is not "200"', async () => {
      mockRequest.mockResolvedValue({
        addNewToken: {
          statusCode: 400,
        },
      });
      await expect(addNewToken(data, 'logId')).rejects.toThrow(
        new Error('Error calling addNewToken mutation: returned status 400'),
      );
    });

    it('should throw an error if the call to the ocpi service fails', async () => {
      const error = new Error('failed request');
      mockRequest.mockRejectedValue(error);
      await expect(addNewToken(data, 'logId')).rejects.toThrow(error);
    });
  });
});
