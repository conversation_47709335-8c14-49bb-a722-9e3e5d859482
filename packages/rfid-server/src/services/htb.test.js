import { createPhysicalAuthMediaHtb, updatePhysicalAuthMediaHtb } from './htb';
import axios from 'axios';
import logger from '../utils/logger';

jest.spyOn(axios, 'post');
jest.spyOn(axios, 'put');
jest.spyOn(logger, 'info').mockImplementation(jest.fn());
jest.spyOn(logger, 'error').mockImplementation(jest.fn());
jest.mock('../env', () => ({
  env: {
    HTB_SERVICES_REST: 'http:/htb.com',
  },
}));

afterEach(() => {
  jest.clearAllMocks();
});

describe('RFID - services - htb', () => {
  describe('createPhysicalAuthMediaHtb', () => {
    const args = [
      'rfidUid123',
      'DE',
      '12345',
      'HEAVY',
      'ACTIVE',
      'logId',
      'currentTime',
    ];
    it('should post the card details to htb and log success', async () => {
      axios.post.mockResolvedValueOnce({
        status: 200,
      });

      const res = await createPhysicalAuthMediaHtb(...args);

      expect(res).toEqual({ status: 200 });
      expect(logger.info).toHaveBeenCalled();
    });

    it('should throw an error if the call to HTB fails', async () => {
      const axiosErrorResponse = {
        response: { data: { message: 'failed to run htb add card query' } },
      };
      axios.post.mockRejectedValue(axiosErrorResponse);

      await expect(() => createPhysicalAuthMediaHtb(...args)).rejects.toThrow(
        new Error(
          `failed to createPhysicalAuthMediaHtb for rfidUid: rfidUid123. Message: ${axiosErrorResponse.response.data.message}`,
        ),
      );
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('updatePhysicalAuthMediaHtb', () => {
    const args = ['rfidUid123', 'ACTIVE', '12345', 'logId'];
    it('should post the card details to htb and log success', async () => {
      axios.put.mockResolvedValueOnce({
        status: 200,
      });

      const res = await updatePhysicalAuthMediaHtb(...args);

      expect(res).toEqual({ status: 200 });
      expect(logger.info).toHaveBeenCalled();
    });

    it('should throw an error if the call to HTB fails', async () => {
      const axiosErrorResponse = {
        response: { data: { message: 'failed to run htb add card query' } },
      };
      axios.put.mockRejectedValue(axiosErrorResponse);

      await expect(() => updatePhysicalAuthMediaHtb(...args)).rejects.toThrow(
        new Error(
          `failed to updatePhysicalAuthMediaHtb: ${axiosErrorResponse.response.data.message}`,
        ),
      );
      expect(logger.error).toHaveBeenCalled();
    });
  });
});
