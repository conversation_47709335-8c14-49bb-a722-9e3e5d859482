import axios from 'axios';

import { Countries } from '../common/enums';
import { countryMap } from '../config/countryMapper';
import { env } from '../env';
import dateNow from '../utils/dateNow';
import logger from '../utils/logger';

const contactByCountry: Record<string, string> = {
  NL: env.HTB_NL_CONTACT as string,
  DE: env.HTB_DE_CONTACT as string,
};

export const createPhysicalAuthMediaHtb = (
  rfidUid: string,
  appCountry: Countries,
  cardNumber: string,
  revenuPlanName: string,
  active: string,
  logTraceId: string,
  currentTime: string,
) =>
  axios
    .post(
      `${env.HTB_SERVICES_REST}/card`,
      {
        tag: rfidUid,
        number: cardNumber,
        contact: contactByCountry[appCountry],
        entity_id: countryMap[appCountry].physicalAuthMediaHTBEntityId,
        rate_id: revenuPlanName,
        valid_from: currentTime,
        active,
        reference: 'bp_pulse',
        billing_contact: contactByCountry[appCountry],
      },
      {
        headers: {
          'x-api-token': env.HTB_API_KEY || '',
        },
      },
    )
    .then((res) => {
      logger.info(
        `Successfully called createPhysicalAuthMediaHtb for rfidUid: ${rfidUid}`,
      );
      return res;
    })
    .catch((err) => {
      logger.error(
        `🚨 Error encountered: logTraceId ${logTraceId} - failed to createPhysicalAuthMediaHtb for rfidUid: ${rfidUid}. Message: ${err.response?.data?.message}`,
        JSON.stringify(err),
      );
      throw new Error(
        `failed to createPhysicalAuthMediaHtb for rfidUid: ${rfidUid}. Message: ${err.response?.data?.message}`,
      );
    });

export const updatePhysicalAuthMediaHtb = (
  rfidUid: string,
  active: string,
  cardNumber: string,
  logTraceId: string,
  deleted?: boolean,
  rateId?: string,
) =>
  axios
    .put(
      `${env.HTB_SERVICES_REST}/card`,
      {
        tag: rfidUid,
        number: cardNumber,
        active: active,
        reference: 'bp_pulse',
        ...(active === '1' && { valid_from: dateNow().split('T')[0] }),
        ...(deleted && {
          expires: dateNow().split('T')[0],
        }),
        ...(rateId ? { rate_id: rateId } : {}),
      },
      {
        headers: {
          'x-api-token': env.HTB_API_KEY || '',
        },
      },
    )
    .then((res) => {
      logger.info(
        `Successfully called updatePhysicalAuthMediaHtb for rfidUid: ${rfidUid}`,
      );
      return res;
    })
    .catch((err) => {
      logger.error(
        `🚨 Error encountered: logTraceId ${logTraceId} - failed to updatePhysicalAuthMediaHtb. Message: ${err.response?.data?.message}`,
        JSON.stringify(err),
      );
      throw new Error(
        `failed to updatePhysicalAuthMediaHtb: ${err.response?.data?.message}`,
      );
    });
