import {
  dcsAddPhysicalRfidCard,
  dcsBlockRfid,
  dcsAssociateRfid,
  dcsUnblockRfid,
} from './dcs';
import axios from 'axios';
import logger from '../utils/logger';

//jest.spyOn(axios, 'post');
jest.mock('axios');
//jest.spyOn(axios, 'put');
jest.spyOn(logger, 'info').mockImplementation(jest.fn());
jest.spyOn(logger, 'error').mockImplementation(jest.fn());
jest.mock('../env', () => ({
  env: {
    DCS_FLEET_SERVICES_URL: 'http:/dcs.com',
  },
}));

const mockGetDcsToken = jest.fn();
jest.mock('../clients/elasticache', () => ({
  get: () => mockGetDcsToken(),
}));

afterEach(() => {
  jest.clearAllMocks();
});

describe('RFID - services - dcs', () => {
  describe('getDCSToken', () => {
    it.skip('should throw an error if getting the DCS token fails', async () => {
      const err = new Error('DCS get token error');
      // axios.post.mockRejectedValueOnce(err);
      await expect(dcsAddPhysicalRfidCard()).rejects.toThrow(
        new Error(`Could not get DCS token, ${err}`),
      );
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('dcsAddPhysicalRfidCard', () => {
    const args = ['1234', '1234', 'logId'];
    it('should post the card number to dcs and log success', async () => {
      mockGetDcsToken.mockImplementation(() =>
        Promise.resolve({
          data: {
            access_token: 'token',
          },
        }),
      );
      axios.post.mockImplementation(() =>
        Promise.resolve({
          status: 200,
        }),
      );

      const res = await dcsAddPhysicalRfidCard(...args);

      expect(res).toEqual(200);
      expect(logger.info).toHaveBeenCalled();
    });

    it('should throw an error if the call to DCS fails', async () => {
      const err = new Error('failed to run dcs add card query');
      mockGetDcsToken.mockImplementation(() =>
        Promise.resolve({
          data: {
            access_token: 'token',
          },
        }),
      );
      axios.post.mockRejectedValue(err);

      await expect(dcsAddPhysicalRfidCard(...args)).rejects.toThrow(
        new Error(`failed to add user to DCS Fleet API: ${err}`),
      );
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('dcsAssociateRfid', () => {
    const args = ['contractId', 'NL', '1234', 'logId'];
    it('should post the card number to dcs and log success', async () => {
      mockGetDcsToken.mockImplementation(() =>
        Promise.resolve({
          data: {
            access_token: 'token',
          },
        }),
      );
      axios.post.mockImplementation(() =>
        Promise.resolve({
          status: 200,
        }),
      );

      await dcsAssociateRfid(...args);

      expect(logger.info).toHaveBeenCalledWith(
        'Successfully associated contract to physical rfid. cardNumber: 1234, dcsContractId: contractId',
      );
    });

    it('should throw an error if the call to DCS fails', async () => {
      const err = new Error('failed to run dcs associate card query');
      mockGetDcsToken.mockImplementation(() =>
        Promise.resolve({
          data: {
            access_token: 'token',
          },
        }),
      );
      axios.post.mockRejectedValue(err);

      await expect(dcsAssociateRfid(...args)).rejects.toThrow(
        new Error(
          `failed to associate contract to physical rfid. cardNumber: 1234, dcsContractId: contractId, err: ${err}`,
        ),
      );
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('dcsUnblockRfid', () => {
    const args = ['contractId', '1234', 'NL', 'logId'];
    it('should return the result of successfully unblocking rfid', async () => {
      axios.post.mockResolvedValueOnce({
        data: {
          access_token: 'token',
        },
      });
      axios.put.mockResolvedValueOnce({
        status: 200,
      });

      const res = await dcsUnblockRfid(...args);

      expect(res).toEqual({ status: 200 });
      expect(logger.info).toHaveBeenCalled();
    });

    it('should throw an error if the call to DCS fails', async () => {
      const err = new Error('failed to run dcs unblock card query');
      axios.post.mockResolvedValueOnce({
        data: {
          access_token: 'token',
        },
      });
      axios.put.mockRejectedValue(err);

      await expect(dcsUnblockRfid(...args)).rejects.toThrow(
        new Error(`failed to unblock user RFID with DCS: ${err}`),
      );
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('dcsBlockRfid', () => {
    const args = ['contractId', '1234', 'NL', 'logId'];
    it('should return the result of successfully blocking rfid', async () => {
      axios.post.mockResolvedValueOnce({
        data: {
          access_token: 'token',
        },
      });
      axios.put.mockResolvedValueOnce({
        status: 200,
      });

      const res = await dcsBlockRfid(...args);

      expect(res).toEqual({ status: 200 });
      expect(logger.info).toHaveBeenCalled();
    });

    it('should throw an error if the call to DCS fails', async () => {
      const err = new Error('failed to run dcs block card query');
      axios.post.mockResolvedValueOnce({
        data: {
          access_token: 'token',
        },
      });
      axios.put.mockRejectedValue(err);

      await expect(dcsBlockRfid(...args)).rejects.toThrow(
        new Error(`failed to block user RFID with DCS: ${err}`),
      );
      expect(logger.error).toHaveBeenCalled();
    });
  });
});
