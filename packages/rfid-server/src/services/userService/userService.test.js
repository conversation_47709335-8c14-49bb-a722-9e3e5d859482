import { updateTagInternalStatus } from './userService';
import logger from '../../utils/logger';
import * as gqlRequest from 'graphql-request';

const mockRequest = jest.spyOn(gqlRequest, 'request');
jest.mock('../../env', () => ({ env: {} }));
jest.spyOn(logger, 'error').mockImplementation(jest.fn());

describe('userService', () => {
  describe('updateTagInternalStatus', () => {
    it('should return a success status object if successful', async () => {
      mockRequest.mockResolvedValue({
        updateTagInternal: {
          status: '200',
        },
      });
      const res = await updateTagInternalStatus({}, 'logId');
      expect(res).toHaveProperty('status', 200);
    });

    it('should throw an error if the returned status is not "200"', async () => {
      mockRequest.mockResolvedValue({
        updateTagInternal: {
          status: '400',
        },
      });
      await expect(updateTagInternalStatus({}, 'logId')).rejects.toThrow(
        new Error(
          '🚨 Error encountered: logTraceId logId - Error calling updateTagInternalStatus mutation with payload {}: returned status 400',
        ),
      );
    });

    it('should throw an error if the call to the user service fails', async () => {
      const error = new Error('failed request');
      mockRequest.mockRejectedValue(error);
      await expect(updateTagInternalStatus({}, 'logId')).rejects.toThrow(error);
    });
  });
  it('should throw an error if the response does not contain updateTagInternal', async () => {
    mockRequest.mockResolvedValue({
      updateTagInternal: {
        status: '20',
      },
    });
    await expect(updateTagInternalStatus({}, 'logId')).rejects.toThrow(
      new Error(
        `🚨 Error encountered: logTraceId logId - Error calling updateTagInternalStatus mutation with payload {}: returned status 20`,
      ),
    );
  });
});
