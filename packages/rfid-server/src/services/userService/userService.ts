import { request } from 'graphql-request';

import { Countries } from '../../common/enums';
import {
  UpdateInternalResponse,
  UpdateTagInternalInput,
  upsertRFIDTagProviderInternalInput,
  UserInfo,
} from '../../common/interfaces';
import { env } from '../../env';
import logger from '../../utils/logger';
import {
  ADD_ROAMING,
  TAG_INFO,
  UPDATE_TAG_INTERNAL,
  UPSERT_RFID_TAG_PROVIDER_INTERNAL,
  USER_INFO,
} from './userService.queries';

const {
  PRIVATE_GATEWAY_CLUSTER_URL = 'http://localhost:4030',
  APOLLO_INTERNAL_SECRET = 'secret',
  APOLLO_INTERNAL_USER_ID = 'APOLLO-INTERNAL',
} = env;

/**
 * Trigger the add roaming mutation for the user to create a virtual card and enroll for the DCS contract id
 * @param country users country
 * @param userId the user-salesforce-id of the user
 */
export const addRoaming = async (
  country: Countries,
  userId: string,
  logTraceId: string,
) =>
  request(
    PRIVATE_GATEWAY_CLUSTER_URL,
    ADD_ROAMING,
    {
      country,
      userId,
    },
    {
      'x-apollo-internal-secret': APOLLO_INTERNAL_SECRET,
      'x-apollo-user-id': APOLLO_INTERNAL_USER_ID,
    },
  )
    .then((data) => data)
    .catch((e) => {
      logger.error(
        `🚨 Error encountered: logTraceId ${logTraceId} - addRoaming failed to enable roaming for the user`,
        {
          userId,
          country,
          error: e,
        },
      );
      throw new Error('Failed to add roaming from user service');
    });

/**
 * Retreive the tagInfo data for the tag Id
 * @param tagId tagId to query by
 */
export const getTagInfo = async (
  tagCardNumber: string,
  country: Countries,
  logTraceId: string,
) =>
  request(
    PRIVATE_GATEWAY_CLUSTER_URL,
    TAG_INFO,
    {
      tagCardNumber,
      country,
    },
    { 'x-apollo-internal-secret': APOLLO_INTERNAL_SECRET },
  )
    .then(({ tagInfo }) => tagInfo)
    .catch((e) => {
      logger.error(
        `🚨 Error encountered: logTraceId ${logTraceId} - getTagInfo failed to fetch tagInfo from user service`,
        {
          tagCardNumber,
          error: e,
        },
      );
      throw new Error('Failed to fetch tag info from user service');
    });

/**
 * Retreive the userInfo data for the user Id
 * @param userId userId to query by
 */
export const getUserInfo = async (
  userId: string,
  appCountry: Countries,
  logTraceId: string,
): Promise<UserInfo> =>
  request(
    PRIVATE_GATEWAY_CLUSTER_URL,
    USER_INFO,
    {
      userId,
      appCountry,
    },
    { 'x-apollo-internal-secret': APOLLO_INTERNAL_SECRET },
  )
    .then(({ userInfo }) => userInfo)
    .catch((e) => {
      logger.error(
        `🚨 Error encountered: logTraceId ${logTraceId} - getUserInfo failed to fetch userInfo from user service`,
        {
          userId,
          error: e,
        },
      );
      throw new Error('Failed to fetch user info from user service');
    });

export const updateTagInternalStatus = async (
  payload: UpdateTagInternalInput,
  logTraceId: string,
) => {
  return request(
    PRIVATE_GATEWAY_CLUSTER_URL,
    UPDATE_TAG_INTERNAL,
    {
      payload,
    },
    {
      'x-apollo-internal-secret': APOLLO_INTERNAL_SECRET,
      'x-apollo-user-id': APOLLO_INTERNAL_USER_ID,
    },
  )
    .then(({ updateTagInternal }) => {
      if (updateTagInternal.status !== '200') {
        const err = `🚨 Error encountered: logTraceId ${logTraceId} - Error calling updateTagInternalStatus mutation with payload ${JSON.stringify(
          payload,
        )}: returned status ${updateTagInternal.status}`;
        logger.error(err);
        throw new Error(err);
      }
      return { status: 200, message: 'Success' };
    })
    .catch((e) => {
      logger.error(
        `🚨 Error encountered: logTraceId ${logTraceId} - Error calling updateTagInternalStatus mutation: ${e}`,
      );
      throw e;
    });
};

export const upsertRFIDTagProviderInternal = async (
  payload: upsertRFIDTagProviderInternalInput,
  logTraceId: string,
): Promise<{ status: number; message: string }> => {
  return request(
    PRIVATE_GATEWAY_CLUSTER_URL,
    UPSERT_RFID_TAG_PROVIDER_INTERNAL,
    {
      payload,
    },
    {
      'x-apollo-internal-secret': APOLLO_INTERNAL_SECRET,
      'x-apollo-user-id': APOLLO_INTERNAL_USER_ID,
    },
  )
    .then(
      ({
        upsertRFIDTagProviderInternal,
      }: {
        upsertRFIDTagProviderInternal: UpdateInternalResponse;
      }) => {
        if (upsertRFIDTagProviderInternal.status !== '200') {
          const err = `🚨 Error encountered: logTraceId ${logTraceId} - Error calling upsertRFIDTagProviderInternal mutation with payload ${JSON.stringify(
            payload,
          )}: returned status ${upsertRFIDTagProviderInternal.status}`;
          logger.error(err);
          throw new Error(err);
        }
        return { status: 200, message: 'Success' };
      },
    )
    .catch((e) => {
      logger.error(
        `🚨 Error encountered: logTraceId ${logTraceId} - Error calling upsertRFIDTagProviderInternal mutation: ${e}`,
      );
      throw e;
    });
};
