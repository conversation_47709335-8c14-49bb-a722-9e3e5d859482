import axios from 'axios';

import { get } from '../clients/elasticache';
import { countryMap } from '../config/countryMapper';
import { env } from '../env';
import callNamedLambda from '../utils/lambdaRequest';
import logger from '../utils/logger';

const useLocalMock = () =>
  axios.post('http://localhost:4040/user/dcsTokenRefresh').catch((e) => {
    logger.error('Lambda request error:', e);
    throw e;
  });

const getDCSToken = async (): Promise<string> => {
  const token = (await get('DCS')) as unknown as { jwt: string };
  if (!token) {
    if (env.NODE_ENV === 'local') {
      await useLocalMock();
    } else {
      const lambdaName = env.DCS_TOKEN_REFRESH_LAMBDA_NAME;
      await callNamedLambda(lambdaName);
    }
    return getDCSToken();
  }
  return token.jwt;
};

export const dcsAddPhysicalRfidCard = async (
  rfid: string,
  cardNumber: string,
  logTraceId: string,
) => {
  const token = await getDCSToken();
  return axios
    .post(
      `${env.DCS_FLEET_SERVICES_URL}/cards`,
      {
        number: cardNumber,
        rfid,
      },
      {
        headers: {
          'fleet-access-key': env.DCS_FLEET_ACCESS_KEY || '',
          'Ocp-Apim-Subscription-Key': env.DCS_SUBSCRIPTION_KEY || '',
          Authorization: `Bearer ${token}`,
        },
      },
    )
    .then((res) => {
      logger.info(
        `Successfully added user to DCS Fleet API with status ${res.status}`,
      );
      return res.status;
    })
    .catch((err) => {
      logger.error(
        `🚨 Error encountered: logTraceId ${logTraceId} - failed to add user to DCS Fleet API: ${err}`,
      );
      throw new Error(`failed to add user to DCS Fleet API: ${err}`);
    });
};

export const dcsAssociateRfid = async (
  dcsContractId: string,
  country: string,
  cardNumber: string,
  rfid: string,
  logTraceId: string,
) => {
  const token = await getDCSToken();
  return axios
    .post(
      `${env.DCS_FLEET_SERVICES_URL}/fleetgroups/${countryMap[country].fleetGroup}/contracts/${dcsContractId}/cards/add`,
      {
        number: cardNumber,
        rfid,
      },
      {
        headers: {
          'fleet-access-key': env.DCS_FLEET_ACCESS_KEY || '',
          'Ocp-Apim-Subscription-Key': env.DCS_SUBSCRIPTION_KEY || '',
          Authorization: `Bearer ${token}`,
        },
      },
    )
    .then(() => {
      logger.info(
        `Successfully associated contract to physical rfid. cardNumber: ${cardNumber}, dcsContractId: ${dcsContractId}`,
      );

      return true;
    })
    .catch((err) => {
      logger.error(
        `🚨 Error encountered: logTraceId ${logTraceId} - failed to associate contract to physical rfid. cardNumber: ${cardNumber}, dcsContractId: ${dcsContractId}, err: ${err}`,
      );

      throw new Error(
        `failed to associate contract to physical rfid. cardNumber: ${cardNumber}, dcsContractId: ${dcsContractId}, err: ${err}`,
      );
    });
};

export const dcsUnblockRfid = async (
  dcsContractId: string,
  cardNumber: string,
  country: string,
  logTraceId: string,
) => {
  const token = await getDCSToken();
  return axios
    .put(
      `${env.DCS_FLEET_SERVICES_URL}/fleetgroups/${countryMap[country].fleetGroup}/contracts/${dcsContractId}/cards/${cardNumber}/unblock`,
      null,
      {
        headers: {
          'fleet-access-key': env.DCS_FLEET_ACCESS_KEY || '',
          'Ocp-Apim-Subscription-Key': env.DCS_SUBSCRIPTION_KEY || '',
          Authorization: `Bearer ${token}`,
        },
      },
    )
    .then((res) => {
      logger.info(
        `Successfully unblocked user RFID with DCS. dcsContractId: ${dcsContractId}`,
      );
      return res;
    })
    .catch((err) => {
      logger.error(
        `🚨 Error encountered: logTraceId ${logTraceId} - failed to unblock user RFID with DCS: ${err}`,
      );
      throw new Error(`failed to unblock user RFID with DCS: ${err}`);
    });
};

export const dcsBlockRfid = async (
  dcsContractId: string,
  cardNumber: string,
  country: string,
  logTraceId: string,
) => {
  const token = await getDCSToken();
  return axios
    .put(
      `${env.DCS_FLEET_SERVICES_URL}/fleetgroups/${countryMap[country].fleetGroup}/contracts/${dcsContractId}/cards/${cardNumber}/block`,
      null,
      {
        headers: {
          'fleet-access-key': env.DCS_FLEET_ACCESS_KEY || '',
          'Ocp-Apim-Subscription-Key': env.DCS_SUBSCRIPTION_KEY || '',
          Authorization: `Bearer ${token}`,
        },
      },
    )
    .then((res) => {
      logger.info(
        `Successfully blocked user RFID with DCS. dcsContractId: ${dcsContractId}`,
      );
      return res;
    })
    .catch((err) => {
      logger.error(
        `🚨 Error encountered: logTraceId ${logTraceId} - failed to block user RFID with DCS: ${err}`,
      );
      throw new Error(`failed to block user RFID with DCS: ${err}`);
    });
};
