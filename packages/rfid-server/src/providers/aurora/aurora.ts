import {
  Countries,
  SupportedPartners,
  TagNotes,
  TagStatus,
  Type,
  UserStatus,
  UserTypes,
} from '../../common/enums';
import {
  AddRfid,
  BlockRfid,
  DefaultRfidResponse,
  Entitlements,
  InsertItem,
  Providers,
  RequestRFID,
  RequestRFIDResponse,
  RevenuePlan,
  TagId,
  TagProviderStatus,
  UnblockRfid,
  UserInfo,
} from '../../common/interfaces';
import { env } from '../../env';
import {
  deprecatedRequestRFID,
  insertRfidData,
} from '../../graphql/rfid.resolver.functions';
import { dcsUnblockRfid } from '../../services/dcs';
import {
  createPhysicalAuthMediaHtb,
  updatePhysicalAuthMediaHtb,
} from '../../services/htb';
import {
  addNewToken,
  updateToken,
} from '../../services/ocpiService/ocpiService';
import {
  getUserInfo,
  updateTagInternalStatus,
  upsertRFIDTagProviderInternal,
} from '../../services/userService/userService';
import { paymentMethodsWallet } from '../../services/walletService/walletService';
import dateNow from '../../utils/dateNow';
import logger from '../../utils/logger';
import { recursiveCardGenerator } from '../../utils/rfidCardGenerator';
import { blockingStatus } from './aurora.functions';

const currentTime = dateNow().split('T')[0];
const { OCPI_IDENTIFIER = 'ocpi-test-provider-dev', NODE_ENV } = env;
const HTB_ERRORS = {
  NO_HTB_REVENUE_PLAN: 'No HASTOBE revenue plan found',
  MORE_THAN_ONE_HTB_REVENUE_PLAN: 'More than one HASTOBE revenue plan found',
};

export const getOCPIIdentifier = (userType: string) => {
  userType = userType.replace(/-wallet/gi, '');
  switch (NODE_ENV) {
    case 'preproduction':
      return `chargeVision-${userType}-preprod`;
    case 'production':
      return `chargeVision-${userType}`;
    default:
      return OCPI_IDENTIFIER;
  }
};

const setTagStatusAsNoCredit = async (
  country: Countries,
  userId: string,
  tagSerialNumber: string,
  tagCardNumber: string,
  logTraceId: string,
  errorMessage: string,
) => {
  await updateTagInternalStatus(
    {
      country,
      salesforceId: userId,
      tagSerialNumber,
      tagCardNumber,
      tagStatus: TagStatus.NO_CREDIT,
    },
    logTraceId,
  ).catch((e) => {
    logger.error(
      `🚨 Error encountered: logTraceId ${logTraceId} - ${errorMessage}. Failed to update tagStatus to NO_CREDIT, error: ${e}`,
    );
    throw new Error(
      `${errorMessage}. Failed to update tagStatus to NO_CREDIT, error: ${e}`,
    );
  });

  logger.info(`Tag update successful with tagStatus ${TagStatus.NO_CREDIT}`);
  logger.error(
    `🚨 Error encountered: logTraceId ${logTraceId} - ${errorMessage}`,
  );
  throw new Error(errorMessage);
};

const checkBlockedPhysicalRFID = (tagIds: TagId[]) => {
  return tagIds.every(
    (tag) =>
      tag.tagStatus === TagStatus.BLOCKED ||
      tag.tagStatus === TagStatus.PENDING_TERMINATION,
  );
};

const checkIfCardAssociated = (
  tagIds: TagId[],
  cardNumber: string,
  cardUid: string,
) => {
  return tagIds.filter(
    (tag) => tag.tagCardNumber === cardNumber && tag.tagId === cardUid,
  );
};

const getTags = (tagIds: TagId[], tagStatus: TagStatus) => {
  // Allowlist DCS
  // array of one element containing virtual rfid card with HTB, or no elements if the user does not have a virtual card with HTB
  const htbVirtualRFID: TagId[] = [];

  // array of one element containing virtual rfid card with DCS, or no elements if the user does not have a virtual card with DCS
  const dcsVirtualRFID: TagId[] = [];

  // array of one element containing physical rfid card, or no elements if the user does not have a physical card
  const physicalRFID: TagId[] = [];
  //array of one element containing cvPaygVirtualRFID  card, or no elements if the user does not have a cvPaygVirtualRFID card

  const cvPaygVirtualRFID: TagId[] = [];

  tagIds.forEach((tag) => {
    if (
      tag.tagCategoryName === 'HTB' &&
      tag.tagTypeName === 'virtual' &&
      tag.tagNotes === TagNotes.VIRTUAL_HTB &&
      tag.tagStatus === tagStatus
    ) {
      htbVirtualRFID.push(tag);
    }
    if (
      tag.tagCategoryName === 'DCS' &&
      tag.tagTypeName === 'virtual' &&
      tag.tagNotes === TagNotes.VIRTUAL_DCS &&
      tag.tagStatus === tagStatus
    ) {
      dcsVirtualRFID.push(tag);
    }
    if (
      tag.tagCategoryName === 'CV-PAYG' &&
      tag.tagTypeName === 'virtual' &&
      tag.tagNotes === TagNotes.VIRTUAL_CV_PAYG &&
      tag.tagStatus === tagStatus
    ) {
      cvPaygVirtualRFID.push(tag);
    }

    if (
      tag.tagCategoryName === 'RFID' &&
      tag.tagTypeName === 'physical' &&
      tag.tagNotes === TagNotes.PHYSICAL_RFID
    ) {
      physicalRFID.push(tag);
    }
  });
  return {
    virtualTagLength: htbVirtualRFID.length ? dcsVirtualRFID.length : 0,
    cvPaygVirtualRFID,
    dcsVirtualRFID,
    htbVirtualRFID,
    physicalRFID,
  };
};

const getInsertPhysicalTagPayload = (
  cardNumber: string,
  country: Countries,
  salesforceId: string,
  status: TagStatus,
) => ({
  tagStatus: status,
  tagTypeName: 'physical',
  tagCategoryName: 'RFID',
  tagNotes: TagNotes.PHYSICAL_RFID,
  tagCardNumber: cardNumber,
  country,
  salesforceId,
});

const getUpdatePhysicalTagPayloadToTerminated = (
  cardNumber: string,
  country: Countries,
  salesforceId: string,
) => ({
  tagStatus: TagStatus.TERMINATED,
  tagCardNumber: cardNumber,
  country,
  salesforceId,
});

const getUpdateTagToActivePayload = (
  cardNumber: string,
  cardUid: string,
  country: Countries,
  salesforceId: string,
) => ({
  country,
  salesforceId,
  tagCardNumber: cardNumber,
  tagSerialNumber: cardUid,
  tagStatus: TagStatus.ACTIVE,
  tagTypeName: 'physical',
});

const notifyHASTOBEToUnblock = async (
  cardUid: string,
  cardNumber: string,
  _dcsContractId: string,
  _country: Countries,
  logTraceId: string,
) => updatePhysicalAuthMediaHtb(cardUid, '1', cardNumber, logTraceId);

export const addCardToHASTOBEAndDCS = async (
  revenuePlans: RevenuePlan[],
  cardUid: string,
  country: Countries,
  cardNumber: string,
  _userId: string,
  _dcsContractId: string | undefined,
  logTraceId: string,
) => {
  // HASTOBE Allowlisting
  const HTBProviders = revenuePlans?.filter((plan) =>
    plan.provider?.includes(Providers.HASTOBE),
  );

  if (!HTBProviders.length) {
    throw new Error(HTB_ERRORS.NO_HTB_REVENUE_PLAN);
  }

  if (HTBProviders.length > 1) {
    throw new Error(HTB_ERRORS.MORE_THAN_ONE_HTB_REVENUE_PLAN);
  }

  await createPhysicalAuthMediaHtb(
    cardUid,
    country,
    cardNumber,
    HTBProviders[0].revenuePlanName,
    '1',
    logTraceId,
    currentTime,
  );

  // add physical RFID to DCS card pool
  // const addPhysicalRfidStatus = await dcsAddPhysicalRfidCard(
  //   cardUid,
  //   cardNumber,
  //   logTraceId,
  // ).catch((e) => {
  //   logger.error(
  //     `🚨 Error encountered: logTraceId ${logTraceId} - failed to add user to DCS Fleet API: ${e}`,
  //   );
  // });
  // logger.info(
  //   `Add Physical RFID function call finished with status: ${addPhysicalRfidStatus}`,
  // );
  // if (addPhysicalRfidStatus === 200) {
  //   //if success, associate contract that own this physical rfid
  //   await dcsAssociateRfid(
  //     dcsContractId,
  //     country,
  //     cardNumber,
  //     cardUid,
  //     logTraceId,
  //   ).catch((e) => {
  //     logger.error(
  //       `🚨 Error encountered: logTraceId ${logTraceId} - failed to associate contract to the physical rfid ${userId}, err: ${e}`,
  //     );
  //     throw new Error(
  //       `failed to associate contract to the physical rfid ${userId}, err: ${e}`,
  //     );
  //   });
  //   logger.info(
  //     `DCS Contract associated successfully for userId: ${userId} with cardNumber: ${cardNumber}. Contract ID associated: ${dcsContractId}`,
  //   );
  // } else {
  //   logger.error(
  //     `Unable to add physical RFID card to DCS card pool, status: ${addPhysicalRfidStatus}`,
  //   );
  //   throw new Error(
  //     `Unable to add physical RFID card to DCS card pool, status: ${addPhysicalRfidStatus}`,
  //   );
  // }
};

export const requestRFIDAurora = async (
  payload: RequestRFID,
  userInfo: UserInfo | null,
  logTraceId: string,
): Promise<RequestRFIDResponse | Error> => {
  const { country, userId, address } = payload;

  if (!userId || !address) {
    const errorMessage = `no valid userId or address provided, userId: ${userId}, address: ${address}`;
    logger.error(`🚨 logTraceId ${logTraceId} -`, errorMessage);
    throw new Error(errorMessage);
  }

  if (!userInfo) {
    const errorMessage = `User info missing for RFID aurora`;
    logger.error(`🚨 logTraceId ${logTraceId} - `, errorMessage);
    return new Error(errorMessage);
  }

  const {
    balance,
    country: userCountry,
    entitlements,
    type: userType,
    partnerType,
  } = userInfo;

  if (
    userType !== UserTypes.PAYG_Wallet &&
    userType !== UserTypes.SUBS_Wallet &&
    country === Countries.UK
  ) {
    return await deprecatedRequestRFID(payload, logTraceId);
  }

  if (!entitlements.rfidEnabled) {
    const errorMessage = `RFID request not allowed for this region`;
    logger.error(`🚨 logTraceId ${logTraceId} - `, errorMessage);
    return new Error(errorMessage);
  }

  if (typeof balance !== 'number' || balance < 0) {
    const errorMessage = `user has an outstanding balance ${balance}`;
    logger.error(errorMessage);
    return new Error(errorMessage);
  }

  // validate paymentMethod (via wallet-server)
  const paymentMethods = await paymentMethodsWallet(userId, logTraceId);
  // filter the payment methods based on the `default` and if the array has 1 element then the payment method is validated
  if (
    !paymentMethods.filter(
      (paymentMethod: { default: boolean }) =>
        paymentMethod.default === false || paymentMethod.default,
    )?.length
  ) {
    logger.error(
      `🚨 logTraceId ${logTraceId} - user has no default payment method`,
    );
    throw new Error('user has no default payment method');
  }

  // generate a unique card number
  const uniqueCardNumber = await recursiveCardGenerator(country, logTraceId);

  // store request in RFID Dynamo Table
  const rfidItem: InsertItem = {
    date_added: Date.now().toString(),
    user_id: userId,
    country: userCountry,
    additional_address_1: '',
    additional_address_2: '',
    address_line: address.addressLine,
    address_postcode: address.addressPostcode,
    address_city: address.addressCity,
    address_country: address.addressCountry,
    first_name: payload.firstName || '',
    last_name: payload.lastName || '',
    rfid_card_number: uniqueCardNumber,
    rfid_status: 'CUSTOMER_REQUESTED',
    partner_type: partnerType,
  };

  const addTagProperitesToUpdate = getInsertPhysicalTagPayload(
    uniqueCardNumber,
    country,
    userId,
    TagStatus.CUSTOMER_REQUESTED,
  );

  const updateTagProperitesToTerminated =
    getUpdatePhysicalTagPayloadToTerminated(uniqueCardNumber, country, userId);

  // Add the Tag with the serial number fields
  try {
    await updateTagInternalStatus(addTagProperitesToUpdate, logTraceId);

    logger.info(
      `Successfully called updateTagInternalStatus for user: ${userId}, properties updated: ${JSON.stringify(
        addTagProperitesToUpdate,
      )}`,
    );
  } catch (e) {
    throw new Error(
      `Failed to call updateTagInternalStatus to store RFID details against userId, error: ${e}, properties to be updated: ${JSON.stringify(
        addTagProperitesToUpdate,
      )}`,
    );
  }

  const successResponse = {
    status: 200,
    data: { eventDetails: '', eventTime: '', salesforceID: userId },
  };

  try {
    await insertRfidData(rfidItem, logTraceId);
    logger.info(
      `Successfully requested RFID for user: ${userId}, cardNumber: ${uniqueCardNumber}`,
    );
    return successResponse;
  } catch (e) {
    logger.info(
      '🚨 Error encountered: dynamo insert failed. Updating tag status to terminated',
    );
    await updateTagInternalStatus(
      updateTagProperitesToTerminated,
      logTraceId,
    ).then(() =>
      logger.info(
        `Successfully updated tag status user: ${userId}, cardNumber: ${uniqueCardNumber} to TERMINATED`,
      ),
    );
    return successResponse;
  }
};

export const blockRFIDAurora = async (
  payload: BlockRfid,
  logTraceId: string,
): Promise<DefaultRfidResponse | Error> => {
  const { userId, country, cardUid, cardNumber, reasonForBlocking } = payload;

  try {
    const {
      tagIds,
      partnerType,
      type: userType,
      entitlements,
      userStatus,
    } = await getUserInfo(userId, country, logTraceId).catch((err) => {
      throw new Error(
        `Failed to get tagIds from getUserInfo for userId ${userId}, error ${err}`,
      );
    });

    if (userStatus !== UserStatus.ACTIVE) {
      const errorMessage = `User status is not ${UserStatus.ACTIVE} for userId: ${userId}, current status: ${userStatus}`;
      logger.error(`🚨 logTraceId ${logTraceId} - ${errorMessage}`);
      return {
        status: 400,
        message: errorMessage,
      };
    }

    if (!entitlements.rfidEnabled) {
      const errorMessage = `RFID functionality is not enabled in entitlements for user with userId: ${userId}`;
      logger.error(`🚨 logTraceId ${logTraceId} - ${errorMessage}`);
      return {
        status: 400,
        message: errorMessage,
      };
    }

    const { physicalRFID } = getTags(tagIds, TagStatus.ACTIVE);

    if (physicalRFID?.length === 0) {
      return {
        status: 400,
        message: `RFID could not be blocked. No physical card associated with userId: ${userId}`,
      };
    }

    const toBeBlockedTags = checkIfCardAssociated(
      physicalRFID,
      cardNumber,
      cardUid,
    );

    if (toBeBlockedTags.length === 0) {
      return {
        status: 400,
        message: `RFID could not be blocked. No card with cardNumber: ${cardNumber} cardUid: ${cardUid} associated with userId: ${userId}`,
      };
    }

    if (checkBlockedPhysicalRFID(toBeBlockedTags)) {
      return {
        status: 400,
        message: `RFID could not be blocked. Physical card is already blocked for userId: ${userId}`,
      };
    }

    const tagStatus = blockingStatus(reasonForBlocking) as TagStatus;

    // Add entry to the dynamo database for PENDING_TERMINATION
    if (tagStatus === TagStatus.PENDING_TERMINATION) {
      const insertTagDynamoPayload = {
        rfid_card_number: cardNumber,
        rfid_status: tagStatus,
        user_id: userId,
        country,
        partner_type: partnerType,
      };
      await insertRfidData(insertTagDynamoPayload, logTraceId).catch((e) => {
        throw new Error(
          `Failed to insert PENDING_TERMINATION data into Dynamo for cardNumber: ${cardNumber} and tagStatus: ${tagStatus}, error ${e}`,
        );
      });
    }

    await updateTagInternalStatus(
      {
        country: country,
        salesforceId: userId,
        tagCardNumber: cardNumber,
        tagStatus,
      },
      logTraceId,
    ).catch((err) => {
      throw new Error(
        `Failed to call updateTagInternalStatus to mark the tag as blocked for userId ${userId}, error ${err}`,
      );
    });

    const providers = entitlements?.rfidDefaultProviders || [];
    const successfulProviders: Providers[] = [];
    const failedProviders: { provider: Providers; error: string }[] = [];

    await processBlockingProviders(
      providers,
      {
        cardUid,
        cardNumber,
        country,
        userType,
        logTraceId,
        tagStatus,
        userId,
      },
      successfulProviders,
      failedProviders,
    );

    const tagProviderStatus =
      tagStatus === TagStatus.PENDING_TERMINATION
        ? TagProviderStatus.TERMINATED
        : TagProviderStatus.BLOCKED;

    if (successfulProviders.length > 0) {
      const tag = await refetchUserTag(userId, country, logTraceId, cardNumber);

      await upsertRFIDTagProviderInternal(
        {
          tagId: tag.tagInternalId,
          tagProviderStatus,
          providers: successfulProviders,
        },
        logTraceId,
      );
    }

    logBlockingResults(
      successfulProviders,
      failedProviders,
      userId,
      cardNumber,
    );

    if (successfulProviders.length === 0) {
      return {
        status: 422,
        message: 'Block RFID Unprocessable',
      };
    }

    const blockedSuccessMessage = 'RFID successfully blocked';
    logger.info(
      `${blockedSuccessMessage} for user: ${userId}, cardNumber: ${cardNumber}`,
    );

    return {
      status: 200,
      message: 'RFID successfully blocked',
    };
  } catch (error) {
    const typedError = error as Error;
    logger.error(`Error in blockRFIDAurora: ${typedError.message}`);
    throw new Error(`Error in blockRFIDAurora: ${typedError.message}`);
  }
};

export const unblockRFIDAurora = async (
  payload: UnblockRfid,
  logTraceId: string,
): Promise<DefaultRfidResponse | Error> => {
  const { userId, country, cardNumber, cardUid } = payload;

  try {
    const { balance, tagIds, entitlements, type } = await getUserInfo(
      userId,
      country,
      logTraceId,
    );

    if (!entitlements.rfidEnabled) {
      const errorMessage = `RFID functionality is not enabled for user with userId: ${userId}`;
      logger.error(
        `🚨 unblockRFID validation failed - RFID Enabled: ${errorMessage}`,
      );
      throw new Error(errorMessage);
    }

    logger.info(`unblockRFID: calling getTags for user ${userId}`);

    const { dcsVirtualRFID, htbVirtualRFID, cvPaygVirtualRFID, physicalRFID } =
      getTags(tagIds, TagStatus.ACTIVE);

    // NOTE: physicalRFID does not check the status so the NO_CREDIT tag will be returned
    logger.info(
      `unblockrfid getTags: \nphysicalRFID: ${JSON.stringify(
        physicalRFID,
      )}\ndcsVirtualRFID: ${JSON.stringify(
        dcsVirtualRFID,
      )} \nhtbVirtualRFID: ${JSON.stringify(
        htbVirtualRFID,
      )}; \ncvPaygVirtualRFID: ${JSON.stringify(cvPaygVirtualRFID)};`,
    );

    // Check if physical card is pending termination
    if (
      physicalRFID?.some(
        (item) =>
          item?.tagCardNumber === cardNumber &&
          item?.tagStatus === TagStatus.PENDING_TERMINATION,
      )
    ) {
      throw new Error(
        'RFID card cannot be unblocked due to pending termination status.',
      );
    }

    // Check if balance is valid
    if (typeof balance !== 'number' || balance < 0) {
      return setTagStatusAsNoCredit(
        country,
        userId,
        cardUid,
        cardNumber,
        logTraceId,
        `UnblockRFID failed, balance: ${balance} is not valid for userId: ${userId}`,
      );
    }

    // Check if any RFID type is available for unblocking
    const hasValidRFID =
      cvPaygVirtualRFID?.length > 0 ||
      htbVirtualRFID?.length > 0 ||
      dcsVirtualRFID?.length > 0;

    if (!hasValidRFID) {
      logger.error(`unblockRFIDAurora: no matching tags for userId: ${userId}`);
      throw new Error(`No matching tags`);
    }

    const providers = entitlements?.rfidDefaultProviders || [];
    const successfulProviders: Providers[] = [];
    const failedProviders: { provider: Providers; error: string }[] = [];

    await processUnblockingProviders(
      providers,
      {
        cvPaygVirtualRFID,
        htbVirtualRFID,
        dcsVirtualRFID,
        cardUid,
        cardNumber,
        userId,
        country,
        type,
        logTraceId,
      },
      successfulProviders,
      failedProviders,
    );

    await updateTagInternalStatus(
      getUpdateTagToActivePayload(cardNumber, cardUid, country, userId),
      logTraceId,
    ).catch((e) => {
      throw new Error(`Failed to update tag internal status, error: ${e}`);
    });

    if (successfulProviders.length > 0) {
      const tag = await refetchUserTag(userId, country, logTraceId, cardNumber);

      await upsertRFIDTagProviderInternal(
        {
          tagId: tag.tagInternalId,
          tagProviderStatus: TagProviderStatus.ACTIVE,
          providers: successfulProviders,
        },
        logTraceId,
      );
    }

    logUnblockingResults(
      successfulProviders,
      failedProviders,
      userId,
      cardNumber,
    );

    if (successfulProviders.length === 0) {
      return {
        status: 422,
        message: 'Unblock RFID Unprocessable',
      };
    }

    const unblockSuccessMessage = 'Successfully unblocked RFID';
    logger.info(
      `${unblockSuccessMessage} for user: ${userId}, cardNumber: ${cardNumber}`,
    );

    return {
      status: 200,
      message: unblockSuccessMessage,
    };
  } catch (error) {
    const typedError = error as Error;
    logger.error(`Error in unblockRFIDAurora: ${typedError.message}`);
    throw new Error(
      `UnblockRFID failed due to an internal error for userId: ${userId}. Error: ${typedError.message}`,
    );
  }
};

export const addRFIDAurora = async (
  payload: AddRfid,
  logTraceId: string,
): Promise<DefaultRfidResponse | Error> => {
  try {
    const { userId, country, cardNumber, cardUid } = payload;
    const {
      balance,
      revenuePlans,
      entitlements,
      type,
      tagIds,
      partnerType,
      userStatus,
    } = await getUserInfo(userId, country, logTraceId);

    if (userStatus !== UserStatus.ACTIVE) {
      const errorMessage = `User status is not ${UserStatus.ACTIVE} for userId: ${userId}, current status: ${userStatus}`;
      logger.error(`🚨 logTraceId ${logTraceId} - ${errorMessage}`);
      return {
        status: 400,
        message: errorMessage,
      };
    }

    validateRFIDEntitlements(entitlements, tagIds, userId, country);

    if (typeof balance !== 'number' || balance < 0) {
      await setTagStatusAsNoCredit(
        country,
        userId,
        cardUid,
        cardNumber,
        logTraceId,
        `Add RFID failed, balance: ${balance} is not valid for userId: ${userId}`,
      );
      return {
        status: 402,
        message: 'No credit, payment required',
      };
    }

    await checkAndAddRfid({ tagIds, cardNumber, country, userId, logTraceId });

    const providers = entitlements?.rfidDefaultProviders || [];
    const successfulProviders: Providers[] = [];
    const failedProviders: { provider: Providers; error: string }[] = [];

    await processAllProviders(
      providers,
      {
        revenuePlans,
        cardUid,
        country,
        cardNumber,
        logTraceId,
        balance,
        userId,
        partnerType,
        type,
        tagIds,
      },
      successfulProviders,
      failedProviders,
    );

    // Update tag status to ACTIVE
    await updateTagInternalStatus(
      getUpdateTagToActivePayload(cardNumber, cardUid, country, userId),
      logTraceId,
    );

    if (successfulProviders.length > 0) {
      const tag = await refetchUserTag(userId, country, logTraceId, cardNumber);

      await upsertRFIDTagProviderInternal(
        {
          tagId: tag.tagInternalId,
          tagProviderStatus: TagProviderStatus.ACTIVE,
          providers: successfulProviders,
        },
        logTraceId,
      );
    }

    logProviderResults(
      successfulProviders,
      failedProviders,
      userId,
      cardNumber,
    );

    if (successfulProviders.length === 0) {
      return {
        status: 422,
        message: 'Add RFID Unprocessable',
      };
    }

    return {
      status: 200,
      message: 'RFID successfully processed based on entitlements',
    };
  } catch (error) {
    const typedError = error as Error;
    logger.error(`Error in addRFIDAurora: ${typedError.message}`);
    throw new Error(`Error in addRFIDAurora: ${typedError.message}`);
  }
};

const processBlockingProviders = async (
  providers: string[],
  context: {
    cardUid: string;
    cardNumber: string;
    country: Countries;
    userType: UserTypes;
    logTraceId: string;
    tagStatus: TagStatus;
    userId: string;
  },
  successfulProviders: Providers[],
  failedProviders: { provider: Providers; error: string }[],
) => {
  const { cardUid, cardNumber, userType, logTraceId, tagStatus, userId } =
    context;

  const deleted = tagStatus === TagStatus.PENDING_TERMINATION;

  for (const providerName of providers) {
    try {
      await processIndividualBlockingProvider(
        providerName,
        {
          cardUid,
          cardNumber,
          userType,
          logTraceId,
          userId,
          deleted,
          country: context.country,
        },
        successfulProviders,
      );
    } catch (error) {
      const typedError = error as Error;
      logger.error(
        `${providerName} provider failed for user: ${userId}, cardNumber: ${cardNumber}, error: ${typedError.message}`,
      );
      failedProviders.push({
        provider: providerName as Providers,
        error: typedError.message,
      });
    }
  }
};

const processIndividualBlockingProvider = async (
  providerName: string,
  context: {
    cardUid: string;
    cardNumber: string;
    userType: UserTypes;
    logTraceId: string;
    userId: string;
    deleted: boolean;
    country: Countries;
  },
  successfulProviders: Providers[],
) => {
  const {
    cardUid,
    cardNumber,
    userType,
    logTraceId,
    userId,
    deleted,
    country,
  } = context;

  // HTB/HASTOBE Provider
  if (isHastobeProvider(providerName) && shouldProcessHASTOBE(country)) {
    await processHastobeBlocking(
      cardUid,
      cardNumber,
      logTraceId,
      deleted,
      userId,
      successfulProviders,
    );
    return;
  }

  // BPCM/CHARGEVISION Provider
  if (isChargevisionProvider(providerName)) {
    await processChargevisionBlocking(
      cardUid,
      userType,
      logTraceId,
      userId,
      successfulProviders,
    );
    return;
  }

  // DCS Provider
  if (providerName === Providers.DCS) {
    processDcsBlocking(userId);
  }
};

const isHastobeProvider = (providerName: string): boolean => {
  return providerName === Providers.HASTOBE || providerName === Providers.HTB;
};

const isChargevisionProvider = (providerName: string): boolean => {
  return (
    providerName === Providers.BPCM || providerName === Providers.CHARGEVISION
  );
};

const processHastobeBlocking = async (
  cardUid: string,
  cardNumber: string,
  logTraceId: string,
  deleted: boolean,
  userId: string,
  successfulProviders: Providers[],
) => {
  if (cardUid) {
    logger.info(
      `blockRFID: Processing ${Providers.HASTOBE} for user ${userId}`,
    );
    await updatePhysicalAuthMediaHtb(
      cardUid,
      '0',
      cardNumber,
      logTraceId,
      deleted,
    );
    successfulProviders.push(Providers.HASTOBE);
  } else {
    logger.info(
      `blockRFID: ${Providers.HASTOBE} processing skipped for user ${userId} - no cardUid provided`,
    );
  }
};

const processChargevisionBlocking = async (
  cardUid: string,
  userType: UserTypes,
  logTraceId: string,
  userId: string,
  successfulProviders: Providers[],
) => {
  logger.info(
    `blockRFID: Processing ${Providers.CHARGEVISION} for user ${userId}`,
  );
  const ocpiIdentifier = getOCPIIdentifier(userType);
  await updateToken({ ocpiIdentifier, uid: cardUid, valid: false }, logTraceId);
  successfulProviders.push(Providers.CHARGEVISION);
};

const processDcsBlocking = (userId: string) => {
  logger.info(`blockRFID: Processing ${Providers.DCS} for user ${userId}`);
  logger.info(
    `${Providers.DCS} blocking not yet implemented for user: ${userId}`,
  );
};

const processUnblockingProviders = async (
  providers: string[],
  context: {
    cvPaygVirtualRFID: TagId[];
    htbVirtualRFID: TagId[];
    dcsVirtualRFID: TagId[];
    cardUid: string;
    cardNumber: string;
    userId: string;
    country: Countries;
    type: UserTypes;
    logTraceId: string;
  },
  successfulProviders: Providers[],
  failedProviders: { provider: Providers; error: string }[],
) => {
  const {
    cvPaygVirtualRFID,
    htbVirtualRFID,
    dcsVirtualRFID,
    cardUid,
    cardNumber,
    userId,
    country,
    type,
    logTraceId,
  } = context;

  for (const providerName of providers) {
    try {
      // BPCM/CHARGEVISION Provider
      if (
        (providerName === Providers.BPCM ||
          providerName === Providers.CHARGEVISION) &&
        cvPaygVirtualRFID?.length > 0
      ) {
        logger.info(
          `unblockRFID: Processing ${Providers.CHARGEVISION} for user ${userId} with cardNumber ${cardNumber}`,
        );
        await handleBPCMUnblocking(
          cardUid,
          type,
          userId,
          cardNumber,
          logTraceId,
        );
        successfulProviders.push(Providers.CHARGEVISION);
      }

      // HTB/HASTOBE Provider
      if (
        (providerName === Providers.HTB ||
          providerName === Providers.HASTOBE) &&
        htbVirtualRFID.length > 0 &&
        shouldProcessHASTOBE(country)
      ) {
        logger.info(
          `unblockRFID: Processing ${Providers.HASTOBE} for user ${userId} with cardNumber ${cardNumber}`,
        );
        await handleHASTOBEUnblocking(
          cardUid,
          cardNumber,
          htbVirtualRFID,
          userId,
          country,
          logTraceId,
        );
        successfulProviders.push(Providers.HASTOBE);
      }

      // DCS Provider
      if (providerName === Providers.DCS && dcsVirtualRFID.length > 0) {
        logger.info(
          `unblockRFID: Processing ${Providers.DCS} for user ${userId} with cardNumber ${cardNumber}`,
        );
        await handleDCSUnblocking(
          dcsVirtualRFID,
          cardNumber,
          userId,
          country,
          logTraceId,
        );
        successfulProviders.push(Providers.DCS);
      }
    } catch (error) {
      const typedError = error as Error;
      logger.error(
        `${providerName} provider failed for user: ${userId}, cardNumber: ${cardNumber}, error: ${typedError.message}`,
      );
      failedProviders.push({
        provider: providerName as Providers,
        error: typedError.message,
      });
    }
  }
};

const handleBPCMUnblocking = async (
  cardUid: string,
  type: UserTypes,
  userId: string,
  cardNumber: string,
  logTraceId: string,
) => {
  logger.info(
    `unblockRFID: Updating OCPI token for UK user ${userId} with cardNumber ${cardNumber}`,
  );
  const ocpiIdentifier = getOCPIIdentifier(type);

  await updateToken(
    {
      uid: cardUid,
      ocpiIdentifier,
      valid: true,
    },
    logTraceId,
  ).catch((err) =>
    logger.error(
      `failed updateToken for ${userId}, ocpiIdentifier ${ocpiIdentifier}`,
      err,
    ),
  );
  logger.info(
    `onboardWithProvidersUK: called ocpi updateToken with ocpiIdentifier ${ocpiIdentifier}`,
  );
};

const handleHASTOBEUnblocking = async (
  cardUid: string,
  cardNumber: string,
  htbVirtualRFID: TagId[],
  userId: string,
  country: Countries,
  logTraceId: string,
) => {
  logger.info(
    `unblockRFID: Updating HASTOBE for user ${userId} with cardNumber ${cardNumber}`,
  );

  await notifyHASTOBEToUnblock(
    cardUid,
    cardNumber,
    htbVirtualRFID[0].tagCardNumber,
    country,
    logTraceId,
  );
};

const handleDCSUnblocking = async (
  dcsVirtualRFID: TagId[],
  cardNumber: string,
  userId: string,
  country: Countries,
  logTraceId: string,
) => {
  logger.info(
    `unblockRFID: Updating DCS for non-UK user ${userId} with cardNumber ${cardNumber}`,
  );
  await dcsUnblockRfid(
    dcsVirtualRFID[0]?.tagCardNumber,
    cardNumber,
    country,
    logTraceId,
  );
};

const processAllProviders = async (
  providers: string[],
  context: {
    revenuePlans: RevenuePlan[];
    cardUid: string;
    country: Countries;
    cardNumber: string;
    logTraceId: string;
    balance: number;
    userId: string;
    partnerType: SupportedPartners;
    type: UserTypes;
    tagIds: TagId[];
  },
  successfulProviders: Providers[],
  failedProviders: { provider: Providers; error: string }[],
) => {
  const {
    revenuePlans,
    cardUid,
    country,
    cardNumber,
    logTraceId,
    userId,
    partnerType,
    tagIds,
    type,
  } = context;

  // HTB/HASTOBE Provider
  // HAVE_TODO: Remove HTB condition after charger migration is complete
  if (
    (providers.includes(Providers.HASTOBE) ||
      providers.includes(Providers.HTB)) &&
    shouldProcessHASTOBE(country)
  ) {
    try {
      logger.info(
        `Processing ${Providers.HASTOBE} provider for user: ${userId}, cardNumber: ${cardNumber}`,
      );

      await processHASTOBEProvider(
        {
          revenuePlans,
          cardUid,
          country,
          cardNumber,
          logTraceId,
          userId,
          tagIds,
        },
        successfulProviders,
        failedProviders,
      );
    } catch (error) {
      const typedError = error as Error;
      logger.error(
        `${Providers.HASTOBE} provider failed for user: ${userId}, cardNumber: ${cardNumber}, error: ${typedError.message}`,
      );
      failedProviders.push({
        provider: Providers.HASTOBE,
        error: typedError.message,
      });
    }
  }

  // HTB/HASTOBE Provider
  // For testing purposes
  if (
    (providers.includes(Providers.HASTOBE) ||
      providers.includes(Providers.HTB)) &&
    !shouldProcessHASTOBE(country)
  ) {
    logger.info(
      `HASTOBE provider skipped for UK user: ${userId} - feature flag ENABLE_HTB_UK is disabled`,
    );
  }

  // BPCM/CHARGEVISION Provider
  // HAVE_TODO: Remove BPCM condition after charger migration is complete
  if (
    providers.includes(Providers.BPCM) ||
    providers.includes(Providers.CHARGEVISION)
  ) {
    logger.info(
      `Processing ${Providers.CHARGEVISION} provider for user: ${userId}, cardNumber: ${cardNumber}`,
    );
    await processCHARGEVISIONProvider(
      Providers.CHARGEVISION,
      partnerType,
      country,
      userId,
      cardUid,
      type,
      logTraceId,
      cardNumber,
      successfulProviders,
      failedProviders,
    );
  }

  // DCS Provider
  if (providers.includes(Providers.DCS)) {
    try {
      logger.info(
        `Processing ${Providers.DCS} provider for user: ${userId}, cardNumber: ${cardNumber}`,
      );
      await processDCSProvider(logTraceId, userId, cardNumber);
    } catch (error) {
      const typedError = error as Error;
      logger.error(
        `DCS provider failed for user: ${userId}, cardNumber: ${cardNumber}, error: ${typedError.message}`,
      );
      failedProviders.push({
        provider: Providers.DCS,
        error: typedError.message,
      });
    }
  }
};

const processCHARGEVISIONProvider = async (
  provider: Providers,
  partnerType: SupportedPartners,
  country: Countries,
  userId: string,
  cardUid: string,
  type: UserTypes,
  logTraceId: string,
  cardNumber: string,
  successfulProviders: Providers[],
  failedProviders: { provider: Providers; error: string }[],
) => {
  try {
    logger.info(
      `Processing ${provider} provider for user: ${userId}, cardNumber: ${cardNumber}`,
    );
    const result = await handleProvider(
      provider,
      () =>
        handleCHARGEVISIONProvider(
          partnerType,
          country,
          userId,
          cardUid,
          type,
          logTraceId,
        ),
      logTraceId,
      userId,
      cardNumber,
      country,
    );
    processProviderResult(
      result,
      provider,
      successfulProviders,
      failedProviders,
    );
  } catch (error) {
    const typedError = error as Error;
    logger.error(
      `${provider} provider failed for user: ${userId}, cardNumber: ${cardNumber}, error: ${typedError.message}`,
    );
    failedProviders.push({
      provider: provider,
      error: typedError.message,
    });
  }
};

const processHASTOBEProvider = async (
  context: {
    revenuePlans: RevenuePlan[];
    cardUid: string;
    country: Countries;
    cardNumber: string;
    logTraceId: string;
    userId: string;
    tagIds: TagId[];
  },
  successfulProviders: Providers[],
  failedProviders: { provider: Providers; error: string }[],
) => {
  const { revenuePlans, cardUid, country, cardNumber, logTraceId, userId } =
    context;

  try {
    await handleHASTOBEProvider(
      revenuePlans,
      cardUid,
      country,
      cardNumber,
      userId,
      logTraceId,
    );

    const tag = await refetchUserTag(userId, country, logTraceId, cardNumber);

    await upsertRFIDTagProviderInternal(
      {
        tagId: tag.tagInternalId,
        tagProviderStatus: TagProviderStatus.ACTIVE, // initial TagProviderStatus while addingRFID shld be ACTIVE
        providers: [Providers.HASTOBE],
      },
      logTraceId,
    );

    logger.info(
      `${Providers.HASTOBE} provider successfully processed for user: ${userId}, cardNumber: ${cardNumber}`,
    );
    successfulProviders.push(Providers.HASTOBE);
  } catch (error) {
    const typedError = error as Error;
    if (typedError.message.includes('Add RFID failed, balance:')) {
      throw error;
    }
    logger.error(
      `${Providers.HASTOBE} provider failed for user: ${userId}, cardNumber: ${cardNumber}, error: ${typedError.message}`,
    );
    failedProviders.push({
      provider: Providers.HASTOBE,
      error: typedError.message,
    });
  }
};

const processDCSProvider = async (
  logTraceId: string,
  userId: string,
  cardNumber: string,
) => {
  logger.info(
    `${Providers.DCS} provider processing not yet implemented for user: ${userId}, cardNumber: ${cardNumber}, logTraceId: ${logTraceId}`,
  );
};

const processProviderResult = (
  result: { success: boolean; error?: string },
  provider: Providers,
  successfulProviders: Providers[],
  failedProviders: { provider: Providers; error: string }[],
) => {
  if (result.success) {
    successfulProviders.push(provider);
  } else if (result.error) {
    failedProviders.push({ provider, error: result.error });
  }
};

const handleProvider = async (
  providerEnum: Providers,
  handlerFn: () => Promise<void>,
  logTraceId: string,
  userId: string,
  cardNumber: string,
  country: Countries,
): Promise<{ success: boolean; error?: string }> => {
  try {
    await handlerFn();

    const tag = await refetchUserTag(userId, country, logTraceId, cardNumber);

    await upsertRFIDTagProviderInternal(
      {
        tagId: tag.tagInternalId,
        tagProviderStatus: TagProviderStatus.ACTIVE, // initial TagProviderStatus while addingRFID shld be ACTIVE
        providers: [providerEnum],
      },
      logTraceId,
    );

    logger.info(
      `${providerEnum} provider successfully processed for user: ${userId}, cardNumber: ${cardNumber}`,
    );
    return { success: true };
  } catch (error) {
    const typedError = error as Error;

    if (typedError.message.includes('Add RFID failed, balance:')) {
      throw typedError;
    }

    logger.error(
      `${providerEnum} provider failed for user: ${userId}, cardNumber: ${cardNumber}, error: ${typedError.message}`,
    );
    return { success: false, error: typedError.message };
  }
};

const handleHASTOBEProvider = async (
  revenuePlans: RevenuePlan[],
  cardUid: string,
  country: Countries,
  cardNumber: string,
  userId: string,
  logTraceId: string,
) => {
  // HASTOBE Allowlisting
  const HTBProviders = revenuePlans?.filter((plan) =>
    plan?.provider?.includes(Providers.HASTOBE),
  );

  if (!HTBProviders.length) {
    throw new Error(HTB_ERRORS.NO_HTB_REVENUE_PLAN);
  }

  if (HTBProviders.length > 1) {
    throw new Error(HTB_ERRORS.MORE_THAN_ONE_HTB_REVENUE_PLAN);
  }

  await createPhysicalAuthMediaHtb(
    cardUid,
    country,
    cardNumber,
    HTBProviders[0].revenuePlanName,
    '1',
    logTraceId,
    currentTime,
  );
};

const handleCHARGEVISIONProvider = async (
  partnerType: SupportedPartners,
  country: Countries,
  userId: string,
  cardUid: string,
  type: UserTypes,
  logTraceId: string,
) => {
  const ocpiIdentifier =
    partnerType == SupportedPartners.UBER && country == Countries.UK
      ? getOCPIIdentifier(UserTypes.UBER)
      : getOCPIIdentifier(type);

  const tokenParams = {
    uid: cardUid,
    authId: cardUid,
    type: Type.RFID,
    ocpiIdentifier,
  };

  logger.info(
    `Using ocpiIdentifier: ${ocpiIdentifier} with tokenParams ${JSON.stringify(
      tokenParams,
    )}`,
  );
  const newToken = await addNewToken(tokenParams, logTraceId).catch((err) => {
    logger.error(
      `Failed to addNewToken for userId: ${userId}, ocpiIdentifier: ${ocpiIdentifier}, logTraceId: ${logTraceId}`,
      err,
    );
    throw err;
  });
  logger.info(
    `onboardWithProvidersUK: called ocpi addNewToken with ocpiIdentifier ${ocpiIdentifier}, tokenResponse ${JSON.stringify(
      newToken,
    )}`,
  );
};

const checkAndAddRfid = async ({
  tagIds,
  cardNumber,
  country,
  userId,
  logTraceId,
}: {
  tagIds: TagId[];
  cardNumber: string;
  country: Countries;
  userId: string;
  logTraceId: string;
}) => {
  let tagExists = false;
  for (const tag of tagIds) {
    if (tag.tagCardNumber === cardNumber) {
      tagExists = true;
      break;
    }
  }

  if (!tagExists) {
    // Insert tag if it doesn't exist
    const addTagProperitesToUpdate = getInsertPhysicalTagPayload(
      cardNumber,
      country,
      userId,
      TagStatus.ACTIVE,
    );

    try {
      await updateTagInternalStatus(addTagProperitesToUpdate, logTraceId);

      logger.info(
        `Successfully called updateTagInternalStatus for user: ${userId}, properties updated: ${JSON.stringify(
          addTagProperitesToUpdate,
          null,
          2,
        )}`,
      );
    } catch (e) {
      throw new Error(
        `Failed to call updateTagInternalStatus to store RFID details against userId, error: ${e}, properties to be updated: ${JSON.stringify(
          addTagProperitesToUpdate,
        )}`,
      );
    }
  }
};

export const shouldProcessHASTOBE = (country: string): boolean => {
  if (country !== 'UK') {
    return true;
  }

  return process.env.ENABLE_HTB_UK === 'true';
};

const validateRFIDEntitlements = (
  entitlements: Entitlements,
  tagIds: TagId[],
  userId: string,
  country: string,
) => {
  if (!entitlements?.rfidEnabled || entitlements.rfidEnabled !== true) {
    throw new Error(
      `RFID not enabled for user with userId: ${userId} in country: ${country}`,
    );
  }

  if (!tagIds.length) {
    throw new Error(
      `No tags present for userId: ${userId} in country: ${country}`,
    );
  }
};

const logProviderResults = (
  successfulProviders: Providers[],
  failedProviders: { provider: Providers; error: string }[],
  userId: string,
  cardNumber: string,
) => {
  const processedSuccessMessage =
    'RFID successfully processed based on entitlements';

  logger.info(
    `${processedSuccessMessage} for user: ${userId}, cardNumber: ${cardNumber}`,
  );

  if (successfulProviders.length > 0) {
    logger.info(`Successful providers: ${successfulProviders.join(', ')}`);
  }

  if (failedProviders.length > 0) {
    logger.warn(`Failed providers for user ${userId}:`, failedProviders);
  }

  if (successfulProviders.length === 0) {
    logger.error(
      `All providers failed. Failed providers: ${failedProviders
        .map((f) => `${f.provider}: ${f.error}`)
        .join(', ')}`,
    );
  }
};

const logBlockingResults = (
  successfulProviders: Providers[],
  failedProviders: { provider: Providers; error: string }[],
  userId: string,
  cardNumber: string,
) => {
  if (successfulProviders.length > 0) {
    logger.info(
      `Successfully blocked RFID with providers: ${successfulProviders.join(
        ', ',
      )} for user: ${userId}, cardNumber: ${cardNumber}`,
    );
  }

  if (failedProviders.length > 0) {
    logger.warn(
      `Failed to block RFID with some providers for user ${userId}:`,
      failedProviders,
    );
  }

  if (successfulProviders.length === 0) {
    logger.error(
      `All providers failed for blocking RFID. Failed providers: ${failedProviders
        .map((f) => `${f.provider}: ${f.error}`)
        .join(', ')}`,
    );
  }
};

const logUnblockingResults = (
  successfulProviders: Providers[],
  failedProviders: { provider: Providers; error: string }[],
  userId: string,
  cardNumber: string,
) => {
  if (successfulProviders.length > 0) {
    logger.info(
      `Successfully unblocked RFID with providers: ${successfulProviders.join(
        ', ',
      )} for user: ${userId}, cardNumber: ${cardNumber}`,
    );
  }

  if (failedProviders.length > 0) {
    logger.warn(
      `Failed to unblock RFID with some providers for user ${userId}:`,
      failedProviders,
    );
  }

  if (successfulProviders.length === 0) {
    logger.error(
      `All providers failed for unblocking RFID. Failed providers: ${failedProviders
        .map((f) => `${f.provider}: ${f.error}`)
        .join(', ')}`,
    );
  }
};

const refetchUserTag = async (
  userId: string,
  country: Countries,
  logTraceId: string,
  cardNumber: string,
) => {
  const updatedUserInfo = await getUserInfo(userId, country, logTraceId);

  const tag = updatedUserInfo.tagIds.find(
    (tag) => tag.tagCardNumber === cardNumber,
  );

  if (!tag) {
    throw new Error(
      `Tag with cardNumber ${cardNumber} not found for user ${userId}`,
    );
  }

  return tag;
};
