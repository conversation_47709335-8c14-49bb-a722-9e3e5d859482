import axios from 'axios';
import BPCM from '.';
import { Countries } from '../../common/enums';
import logger from '../../utils/logger';

jest.mock('axios');
jest.mock('../../env', () => ({ env: {} }));
jest.spyOn(logger, 'info').mockImplementation(jest.fn());
jest.spyOn(logger, 'error').mockImplementation(jest.fn());

const requestSuccessRFIDPayloadUK = {
  country: 'UK',
  userId: 'dummyuserID',
  cardPreference: 'Visa',
  address: {
    addressLine: 'dummy',
    addressCity: 'dummy',
    addressPostcode: 'dummy',
    addressCountry: 'dummy',
  },
};

const addOrUnblockRFIDSuccessPayload = {
  userId: '12345',
  cardNumber: '7777',
  cardUid: 'test',
  country: Countries.DE,
};

const expectedUserNotFoundResponse = {
  status: 200,
  data: {
    error: true,
    code: 'PU-2-4047',
    eventDetails: 'TAG REPLACEMENT RESPONSE',
    eventTime: '11:28:40, 12/5/2020',
    message: 'User with user_id=123 not found',
    salesforceId: '0050E000009RURI',
  },
};

const userNotFoundData = {
  data: {
    error: true,
    code: 'PU-2-4047',
    event_details: 'TAG REPLACEMENT RESPONSE',
    event_time: '11:28:40, 12/5/2020',
    message: 'User with user_id=123 not found',
    salesforce_ID: '0050E000009RURI',
  },
};

const successDataResponse = {
  data: {
    error: false,
    code: 'PU-4-2001',
    event_details: 'TAG REPLACEMENT RESPONSE',
    event_time: '11:28:40, 12/5/2020',
    message: 'Tag will be allocated manually',
    salesforce_ID: '0050E000009RURI',
  },
};

const expectedSuccessData = {
  status: 200,
  data: {
    error: false,
    code: 'PU-4-2001',
    eventDetails: 'TAG REPLACEMENT RESPONSE',
    eventTime: '11:28:40, 12/5/2020',
    message: 'Tag will be allocated manually',
    salesforceId: '0050E000009RURI',
  },
};

describe('bpcm', () => {
  describe('requestRFID', () => {
    it('Request RFID succesful scenario for UK country', async () => {
      await axios.patch.mockImplementationOnce(() =>
        Promise.resolve({
          status: 200,
          data: {
            event_details: '',
            event_time: '',
            salesforce_ID: '',
          },
        }),
      );
      const result = await BPCM.requestRFID(
        requestSuccessRFIDPayloadUK,
        'logId',
      );
      expect(result).toEqual({
        status: 200,
        data: { eventDetails: '', eventTime: '', salesforceID: '' },
      });
    });

    it('Request RFID fails for the UK line if the axios returns error', async () => {
      await axios.patch.mockImplementationOnce(() => {
        throw new Error('server not reached');
      });

      await expect(
        BPCM.requestRFID(requestSuccessRFIDPayloadUK, 'logId'),
      ).rejects.toThrow('server not reached');
    });
  });

  describe('replaceRFID', () => {
    it('should return replace rfid response from BPCM', async () => {
      axios.post.mockImplementationOnce(() =>
        Promise.resolve(successDataResponse),
      );
      const replaceRFIDRes = await BPCM.replaceRFID('0050E000009RURI', 'logId');
      expect(JSON.stringify(replaceRFIDRes)).toEqual(
        JSON.stringify(expectedSuccessData),
      );
    });

    it('should throw an error if the call to bpcm fails', async () => {
      const errorMessage = 'Network Error';
      axios.post.mockImplementationOnce(() =>
        Promise.reject(new Error(errorMessage)),
      );
      await expect(
        BPCM.replaceRFID('0050E000009RURI', 'lodId'),
      ).rejects.toThrow(errorMessage);
    });

    it('should return a 404 if user not found', async () => {
      axios.post.mockImplementationOnce(() =>
        Promise.resolve(userNotFoundData),
      );
      const replaceRFIDUserNotFound = await BPCM.replaceRFID(
        '0050E000009RURI',
        'logId',
      );
      expect(JSON.stringify(replaceRFIDUserNotFound)).toEqual(
        JSON.stringify(expectedUserNotFoundResponse),
      );
    });
  });

  describe('Unsupported BPCM operations', () => {
    it('should return an error status if attempting to use addRFID with BPCM', async () => {
      const response = await BPCM.addRFID(
        addOrUnblockRFIDSuccessPayload,
        'logId',
      );
      expect(response).toEqual({
        status: 400,
        message: '[ERROR] Unsupported operation for BPCM provider: addRFID',
      });
    });

    it('should return an error status if attempting to use unblockRFID with BPCM', async () => {
      const response = await BPCM.unblockRFID(
        addOrUnblockRFIDSuccessPayload,
        'logId',
      );
      expect(response).toEqual({
        status: 400,
        message: '[ERROR] Unsupported operation for BPCM provider: unblockRFID',
      });
    });
  });
});
