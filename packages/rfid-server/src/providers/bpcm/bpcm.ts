import axios from 'axios';
import moment from 'moment';

import {
  ReplaceRfidResponse,
  RequestRFID,
  RequestRFIDResponse,
  UserInfo,
} from '../../common/interfaces';
import { env } from '../../env';
import logger from '../../utils/logger';

const AWS_BASE_URL = env.BPCM_AWS_SERVICES_URL as string;
const DATETIME_FORMAT = 'HH:mm:ss, DD/MM/YYYY';

const axiosConfig = {
  headers: {
    'Content-Type': 'application/json',
    'x-api-key': env.BPCM_AWS_SERVICES_KEY as string,
  },
};

export const requestRFIDBPCM = async (
  payload: RequestRFID,
  _userInfo: UserInfo | null,
  logTraceId: string,
): Promise<RequestRFIDResponse | Error> => {
  const { userId, cardPreference, address } = payload;
  if (userId && cardPreference && address) {
    const queuePayload = {
      event_details: 'NEW RFID REQUEST',
      event_time: moment().utc().format(DATETIME_FORMAT),
      salesforce_ID: userId,
      card_preference: cardPreference,
      delivery_address_details: {
        address_line: address.addressLine,
        address_city: address.addressCity,
        address_postcode: address.addressPostcode,
        address_country: address.addressCountry,
      },
    };
    return axios
      .patch(
        `${AWS_BASE_URL}/customer/subscription-preferences`,
        queuePayload,
        axiosConfig,
      )
      .then((res) => {
        const data = (res && res.data) || {};
        return {
          status: 200,
          data: {
            eventDetails: data.event_details,
            eventTime: data.event_time,
            salesforceID: data.salesforce_ID,
          },
        };
      })
      .catch((e) => {
        logger.error('BPCM request rfid error', e);
        throw e;
      });
  }
  throw new Error(
    `🚨 logTraceId ${logTraceId} - for userId: ${userId}, cardPreference: ${cardPreference}, address: ${address}`,
  );
};

/**
 * The API will send the request for a new RFID to BPCM.
 * @param userId unique userId value for user issued by indentity provider
 */
export const replaceRFID = async (
  userId: string,
  logTraceId: string,
): Promise<ReplaceRfidResponse> => {
  const body = {
    event_details: 'REPLACEMENT RFID REQUEST',
    event_time: moment().utc().format(DATETIME_FORMAT),
    salesforce_ID: userId,
  };
  const path = `/customer/subscription/tag-replacement/${userId}`;
  return axios
    .post(`${AWS_BASE_URL}${path}`, body, axiosConfig)
    .then((res) => {
      const data = (res && res.data) || {};
      return {
        status: 200,
        data: {
          error: data.error,
          code: data.code,
          eventDetails: data.event_details,
          eventTime: data.event_time,
          message: data.message,
          salesforceId: data.salesforce_ID,
        },
      };
    })
    .catch((e) => {
      logger.error(
        `🚨 logTraceId ${logTraceId} - BPCM subscription rfid replacement request error: ${e.message}`,
      );
      throw e;
    });
};

/**
 * Deprecated (in use for UK app), should use requestRFIDBPCM
 */
export const deprecatedRequestRFIDBPCM = async (
  payload: RequestRFID,
  logTraceId: string,
): Promise<RequestRFIDResponse | Error> => {
  const { userId, cardPreference, address } = payload;
  if (userId && cardPreference && address) {
    const queuePayload = {
      event_details: 'NEW RFID REQUEST',
      event_time: moment().utc().format(DATETIME_FORMAT),
      salesforce_ID: userId,
      card_preference: cardPreference,
      delivery_address_details: {
        address_line: address.addressLine,
        address_city: address.addressCity,
        address_postcode: address.addressPostcode,
        address_country: address.addressCountry,
      },
    };
    return axios
      .patch(
        `${AWS_BASE_URL}/customer/subscription-preferences`,
        queuePayload,
        axiosConfig,
      )
      .then((res) => {
        const data = (res && res.data) || {};
        return {
          status: 200,
          data,
        };
      })
      .catch((e) => {
        logger.error('Deprecated BPCM request rfid error', e);
        throw e;
      });
  }
  throw new Error(
    `🚨 logTraceId ${logTraceId} - for userId: ${userId}, cardPreference: ${cardPreference}, address: ${address}`,
  );
};
