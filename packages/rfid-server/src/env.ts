import 'dotenv/config';

import variables from './variables.json';

const missingVars = variables.filter(
  (variable) => process.env[variable.name] === undefined,
);

if (missingVars.length > 0) {
  console.log('Aborting environment variables missing: ', missingVars);
  if (typeof jest === 'undefined') {
    process.exit();
  }
}

const {
  NODE_ENV,
  BPCM_AWS_SERVICES_KEY,
  BPCM_AWS_SERVICES_URL,
  DCS_FLEET_SERVICES_URL,
  APOLLO_INTERNAL_USER_ID,
  PRIVATE_GATEWAY_CLUSTER_URL,
  APOLLO_INTERNAL_SECRET,
  DYNAMO_DB_URL,
  DYNAMO_DB_REGION,
  DYNAMO_DB_ACCESS_KEY_ID,
  DYNAMO_DB_ACCESS_KEY,
  RFID_DB_TABLE_NAME,
  FLEET_GROUP_DE,
  FLEET_GROUP_NL,
  COMPANY_CODE_DE,
  COMPANY_CODE_NL,
  HTB_SERVICES_REST,
  HTB_API_KEY,
  DCS_TOKEN_CLIENT_ID,
  DCS_TOKEN_CLIENT_SECRET,
  DCS_TOKEN_RESOURCE,
  DCS_TOKEN_URL,
  DCS_SUBSCRIPTION_KEY,
  DCS_FLEET_ACCESS_KEY,
  HTB_NL_ENTITY_ID,
  HTB_DE_ENTITY_ID,
  DCS_TOKEN_REFRESH_LAMBDA_NAME,
  TOKEN_ELASTICACHE_HOST,
  ELASTICACHE_PORT,
  AWS_REGION,
  HTB_NL_CONTACT,
  HTB_DE_CONTACT,
  OCPI_IDENTIFIER,
  OPEN_ID_ACCESS_ROLE,
  ENABLE_HTB_UK,
  COMPANY_CODE_UK,
  FLEET_GROUP_UK,
  HTB_UK_ENTITY_ID,
  ENABLE_DE_XBORDER_RFID,
} = process.env;

const AVAILABLE_ENV_VARS = {
  NODE_ENV,
  DCS_FLEET_SERVICES_URL,
  BPCM_AWS_SERVICES_KEY,
  BPCM_AWS_SERVICES_URL,
  APOLLO_INTERNAL_USER_ID,
  PRIVATE_GATEWAY_CLUSTER_URL,
  APOLLO_INTERNAL_SECRET,
  DYNAMO_DB_URL,
  DYNAMO_DB_REGION,
  DYNAMO_DB_ACCESS_KEY_ID,
  DYNAMO_DB_ACCESS_KEY,
  RFID_DB_TABLE_NAME,
  FLEET_GROUP_DE,
  FLEET_GROUP_NL,
  COMPANY_CODE_DE,
  COMPANY_CODE_NL,
  HTB_SERVICES_REST,
  HTB_API_KEY,
  DCS_TOKEN_CLIENT_ID,
  DCS_TOKEN_CLIENT_SECRET,
  DCS_TOKEN_RESOURCE,
  DCS_TOKEN_URL,
  DCS_SUBSCRIPTION_KEY,
  DCS_FLEET_ACCESS_KEY,
  HTB_NL_ENTITY_ID,
  HTB_DE_ENTITY_ID,
  DCS_TOKEN_REFRESH_LAMBDA_NAME,
  TOKEN_ELASTICACHE_HOST,
  ELASTICACHE_PORT,
  AWS_REGION,
  HTB_NL_CONTACT,
  HTB_DE_CONTACT,
  OCPI_IDENTIFIER,
  OPEN_ID_ACCESS_ROLE,
  ENABLE_HTB_UK,
  COMPANY_CODE_UK,
  FLEET_GROUP_UK,
  HTB_UK_ENTITY_ID,
  ENABLE_DE_XBORDER_RFID,
};

export type EnvVars = Record<keyof typeof AVAILABLE_ENV_VARS, string>;

export const env = AVAILABLE_ENV_VARS as EnvVars;
