deployName: 'rfid-server'
serverName: 'rfid-server'
namespace: 'rfid-server'
imageRepo: rfid-server
versionNumber: 'x'
replicas: #{REPLICAS_SMALL}#
rolloutReplicas: #{HPA_REPLICAS_SMALL}#

shared:
  hpapdbName: rfid-server
  minReplicas: #{HPA_REPLICAS_MEDIUM}#
  maxReplicas: #{REPLICAS_MAX_MEDIUM}#
  averageUtilization: 95
  microservicePort: 4017

serviceAccount: rfid-server-service-account

rollouts:
  enabled: #{HELM_ENABLE_ROLLOUTS}#

includeSchema: true

service:
  clusterIp:
    enabled: true
  loadBalancer:
    enabled: false
    tags: 'placeholder'

deployment:
  hpa:
    enabled: false
  servers:
    enabled: true

secrets:
  - objectName: '{{ .Values.environment }}-v{{ .Values.versionNumber }}-Backend-Bpcm-ApiKey'
    alias: BpcmAwsServices-Api<PERSON>ey
    k8sSecretName: bpcm-aws-services-api-key
  - objectName: '{{ .Values.environment }}-v{{ .Values.versionNumber }}-Rfid-ApolloInternal-Secret'
    alias: ApolloInternal-Secret
    k8sSecretName: apollo-internal-secret
  - objectName: '{{ .Values.environment }}-v{{ .Values.versionNumber }}-Rfid-Htb-ApiKey'
    alias: Htb-ApiKey
    k8sSecretName: htb-api-key
  - objectName: '{{ .Values.environment }}-v{{ .Values.versionNumber }}-Rfid-DcsSubscription-ApiKey'
    alias: DcsSubscription-ApiKey
    k8sSecretName: dcs-subscription-api-key
  - objectName: '{{ .Values.environment }}-v{{ .Values.versionNumber }}-Rfid-DcsFleet-AccessKey'
    alias: DcsFleet-AccessKey
    k8sSecretName: dcs-fleet-access-key
  - objectName: '{{ .Values.environment }}-v{{ .Values.versionNumber }}-Rfid-DcsTokenClient-Secret'
    alias: DcsTokenClient-Secret
    k8sSecretName: dcs-token-client-secret

env:
  - name: OTEL_SERVICE_NAME
    value: 'rfid-server-{{ .Values.environment }}'
  - name: NODE_ENV
    value: '#{NODE_ENV}#'
  - name: BPCM_AWS_SERVICES_URL
    value: '#{BPCM_AWS_SERVICES_URL}#'
  - name: APOLLO_INTERNAL_USER_ID
    value: '#{APOLLO_INTERNAL_USER_ID}#'
  - name: PRIVATE_GATEWAY_CLUSTER_URL
    value: '#{PRIVATE_GATEWAY_CLUSTER_URL}#'
  - name: DCS_FLEET_SERVICES_URL
    value: '#{DCS_FLEET_SERVICES_URL}#'
  - name: RFID_DB_TABLE_NAME
    value: '#{RFID_DB_TABLE_NAME}#'
  - name: DYNAMO_DB_ACCESS_KEY_ID
    value: ''
  - name: DYNAMO_DB_ACCESS_KEY
    value: ''
  - name: DYNAMO_DB_URL
    value: '#{DYNAMO_DB_URL}#'
  - name: DYNAMO_DB_REGION
    value: '#{DYNAMO_DB_REGION}#'
  - name: 'FLEET_GROUP_DE'
    value: '#{FLEET_GROUP_DE}#'
  - name: 'FLEET_GROUP_NL'
    value: '#{FLEET_GROUP_NL}#'
  - name: 'FLEET_GROUP_ES'
    value: '#{FLEET_GROUP_ES}#'
  - name: 'FLEET_GROUP_UK'
    value: '#{FLEET_GROUP_UK}#'
  - name: 'COMPANY_CODE_DE'
    value: '#{COMPANY_CODE_DE}#'
  - name: 'COMPANY_CODE_NL'
    value: '#{COMPANY_CODE_NL}#'
  - name: 'COMPANY_CODE_ES'
    value: '#{COMPANY_CODE_ES}#'
  - name: 'COMPANY_CODE_UK'
    value: '#{COMPANY_CODE_UK}#'
  - name: 'HTB_SERVICES_REST'
    value: '#{HTB_SERVICES_REST}#'
  - name: 'DCS_TOKEN_CLIENT_ID'
    value: '#{DCS_TOKEN_CLIENT_ID}#'
  - name: 'DCS_TOKEN_RESOURCE'
    value: '#{DCS_TOKEN_RESOURCE}#'
  - name: 'DCS_TOKEN_URL'
    value: '#{DCS_TOKEN_URL}#'
  - name: 'HTB_NL_ENTITY_ID'
    value: '#{HTB_NL_ENTITY_ID}#'
  - name: 'HTB_DE_ENTITY_ID'
    value: '#{HTB_DE_ENTITY_ID}#'
  - name: 'HTB_ES_ENTITY_ID'
    value: '#{HTB_ES_ENTITY_ID}#'
  - name: 'HTB_UK_ENTITY_ID'
    value: '#{HTB_UK_ENTITY_ID}#'
  - name: 'TOKEN_ELASTICACHE_HOST'
    value: '#{TOKEN_ELASTICACHE_HOST}#'
  - name: 'ELASTICACHE_PORT'
    value: '#{ELASTICACHE_PORT}#'
  - name: 'AWS_REGION'
    value: '#{AWS_REGION}#'
  - name: 'DCS_TOKEN_REFRESH_LAMBDA_NAME'
    value: '#{DCS_TOKEN_REFRESH_LAMBDA_NAME}#'
  - name: 'HTB_NL_CONTACT'
    value: '#{HTB_NL_CONTACT}#'
  - name: 'HTB_DE_CONTACT'
    value: '#{HTB_DE_CONTACT}#'
  - name: 'HTB_ES_CONTACT'
    value: '#{HTB_ES_CONTACT}#'
  - name: 'OCPI_IDENTIFIER'
    value: '#{OCPI_IDENTIFIER}#'
  - name: 'ENABLE_HTB_UK'
    value: '#{ENABLE_HTB_UK}#'
  - name: 'ENABLE_DE_XBORDER_RFID'
    value: '#{ENABLE_DE_XBORDER_RFID}#'
  - name: BPCM_AWS_SERVICES_KEY
    value: null
    valueFrom:
      secretKeyRef:
        name: bpcm-aws-services-api-key
        key: secret
  - name: APOLLO_INTERNAL_SECRET
    value: null
    valueFrom:
      secretKeyRef:
        name: apollo-internal-secret
        key: secret
  - name: HTB_API_KEY
    value: null
    valueFrom:
      secretKeyRef:
        name: htb-api-key
        key: secret
  - name: DCS_SUBSCRIPTION_KEY
    value: null
    valueFrom:
      secretKeyRef:
        name: dcs-subscription-api-key
        key: secret
  - name: DCS_FLEET_ACCESS_KEY
    value: null
    valueFrom:
      secretKeyRef:
        name: dcs-fleet-access-key
        key: secret
  - name: DCS_TOKEN_CLIENT_SECRET
    value: null
    valueFrom:
      secretKeyRef:
        name: dcs-token-client-secret
        key: secret
  - name: 'OPEN_ID_ACCESS_ROLE'
    value: '#{OPEN_ID_ACCESS_ROLE}#'
