---
apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: rfid-server-aws-secrets
  namespace: rfid-server
spec:
  provider: aws
  parameters:
    ## Follow PascalCase for Service, SecretName and SecretType
    objects: |
      - objectName: '${env}-v${version_number}-Backend-Bpcm-ApiKey'
        objectType: "secretsmanager"
        jmesPath:
          - path: "secret"
            objectAlias: "BpcmAwsServices-ApiKey"
      - objectName: '${env}-v${version_number}-Rfid-ApolloInternal-Secret'
        objectType: "secretsmanager"
        jmesPath:
          - path: "secret"
            objectAlias: "ApolloInternal-Secret"
      - objectName: '${env}-v${version_number}-Rfid-Htb-ApiKey'
        objectType: "secretsmanager"
        jmesPath:
          - path: "secret"
            objectAlias: "Htb-ApiKey"
      - objectName: '${env}-v${version_number}-Rfid-DcsSubscription-Api<PERSON>ey'
        objectType: "secretsmanager"
        jmesPath:
          - path: "secret"
            objectAlias: "DcsSubscription-ApiKey"
      - objectName: '${env}-v${version_number}-Rfid-DcsFleet-AccessKey'
        objectType: "secretsmanager"
        jmesPath:
          - path: "secret"
            objectAlias: "DcsFleet-AccessKey"
      - objectName: '${env}-v${version_number}-Rfid-DcsTokenClient-Secret'
        objectType: "secretsmanager"
        jmesPath:
          - path: "secret"
            objectAlias: "DcsTokenClient-Secret"
  secretObjects:
    ## SECRET NAME MUST USE - rather than _ and must all be lowercase due to syntax requirements
    - secretName: bpcm-aws-services-api-key ##secretname seperated by - where its seperated by PascalCase
      type: Opaque
      data:
        - objectName: BpcmAwsServices-ApiKey
          key: secret
    - secretName: apollo-internal-secret
      type: Opaque
      data:
        - objectName: ApolloInternal-Secret
          key: secret
    - secretName: htb-api-key
      type: Opaque
      data:
        - objectName: Htb-ApiKey
          key: secret
    - secretName: dcs-subscription-api-key
      type: Opaque
      data:
        - objectName: DcsSubscription-ApiKey
          key: secret
    - secretName: dcs-fleet-access-key
      type: Opaque
      data:
        - objectName: DcsFleet-AccessKey
          key: secret
    - secretName: dcs-token-client-secret
      type: Opaque
      data:
        - objectName: DcsTokenClient-Secret
          key: secret
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rfid-server-deployment-v${version_number}
  annotations:
    reloader.stakater.com/auto: 'true'
spec:
  selector:
    matchLabels:
      app: rfid-server-v${version_number}
  replicas: #{REPLICAS_SMALL}#
  template:
    metadata:
      annotations:
        sidecar.opentelemetry.io/inject: 'true'
        instrumentation.opentelemetry.io/inject-nodejs: 'true'
      labels:
        app: rfid-server-v${version_number}
    spec:
      serviceAccountName: rfid-server-service-account
      containers:
        - name: rfid-server-v${version_number}
          image: ************.dkr.ecr.eu-west-1.amazonaws.com/rfid-server:${image_tag}
          imagePullPolicy: #{IMAGE_PULL_POLICY}#
          resources:
            requests:
              cpu: #{REPLICAS_CPU_REQUEST_MEDIUM}#
          ports:
            - containerPort: 4017
          readinessProbe:
            tcpSocket:
              port: 4017
            initialDelaySeconds: 60
            periodSeconds: 60
            timeoutSeconds: 50
          volumeMounts:
            - name: secrets-store-inline
              mountPath: /mnt/secrets-store
              readOnly: true
          env:
            - name: OTEL_SERVICE_NAME
              value: 'rfid-server-${env}'
            - name: NODE_ENV
              value: '#{NODE_ENV}#'
            - name: BPCM_AWS_SERVICES_URL
              value: '#{BPCM_AWS_SERVICES_URL}#'
            - name: APOLLO_INTERNAL_USER_ID
              value: '#{APOLLO_INTERNAL_USER_ID}#'
            - name: PRIVATE_GATEWAY_CLUSTER_URL
              value: '#{PRIVATE_GATEWAY_CLUSTER_URL}#'
            - name: DCS_FLEET_SERVICES_URL
              value: '#{DCS_FLEET_SERVICES_URL}#'
            - name: RFID_DB_TABLE_NAME
              value: '#{RFID_DB_TABLE_NAME}#'
            - name: DYNAMO_DB_ACCESS_KEY_ID
              value: ''
            - name: DYNAMO_DB_ACCESS_KEY
              value: ''
            - name: DYNAMO_DB_URL
              value: '#{DYNAMO_DB_URL}#'
            - name: DYNAMO_DB_REGION
              value: '#{DYNAMO_DB_REGION}#'
            - name: 'FLEET_GROUP_DE'
              value: '#{FLEET_GROUP_DE}#'
            - name: 'FLEET_GROUP_NL'
              value: '#{FLEET_GROUP_NL}#'
            - name: 'FLEET_GROUP_ES'
              value: '#{FLEET_GROUP_ES}#'
            - name: 'FLEET_GROUP_UK'
              value: '#{FLEET_GROUP_UK}#'
            - name: 'COMPANY_CODE_DE'
              value: '#{COMPANY_CODE_DE}#'
            - name: 'COMPANY_CODE_NL'
              value: '#{COMPANY_CODE_NL}#'
            - name: 'COMPANY_CODE_ES'
              value: '#{COMPANY_CODE_ES}#'
            - name: 'COMPANY_CODE_UK'
              value: '#{COMPANY_CODE_UK}#'
            - name: 'HTB_SERVICES_REST'
              value: '#{HTB_SERVICES_REST}#'
            - name: 'DCS_TOKEN_CLIENT_ID'
              value: '#{DCS_TOKEN_CLIENT_ID}#'
            - name: 'DCS_TOKEN_RESOURCE'
              value: '#{DCS_TOKEN_RESOURCE}#'
            - name: 'DCS_TOKEN_URL'
              value: '#{DCS_TOKEN_URL}#'
            - name: 'HTB_NL_ENTITY_ID'
              value: '#{HTB_NL_ENTITY_ID}#'
            - name: 'HTB_DE_ENTITY_ID'
              value: '#{HTB_DE_ENTITY_ID}#'
            - name: 'HTB_UK_ENTITY_ID'
              value: '#{HTB_UK_ENTITY_ID}#'
            - name: 'HTB_ES_ENTITY_ID'
              value: '#{HTB_ES_ENTITY_ID}#'
            - name: 'TOKEN_ELASTICACHE_HOST'
              value: '#{TOKEN_ELASTICACHE_HOST}#'
            - name: 'ELASTICACHE_PORT'
              value: '#{ELASTICACHE_PORT}#'
            - name: 'AWS_REGION'
              value: '#{AWS_REGION}#'
            - name: 'DCS_TOKEN_REFRESH_LAMBDA_NAME'
              value: '#{DCS_TOKEN_REFRESH_LAMBDA_NAME}#'
            - name: 'HTB_NL_CONTACT'
              value: '#{HTB_NL_CONTACT}#'
            - name: 'HTB_DE_CONTACT'
              value: '#{HTB_DE_CONTACT}#'
            - name: 'HTB_ES_CONTACT'
              value: '#{HTB_ES_CONTACT}#'
            - name: 'OCPI_IDENTIFIER'
              value: '#{OCPI_IDENTIFIER}#'
            - name: 'ENABLE_HTB_UK'
              value: '#{ENABLE_HTB_UK}#'
            - name: 'ENABLE_DE_XBORDER_RFID'
              value: '#{ENABLE_DE_XBORDER_RFID}#'
            - name: BPCM_AWS_SERVICES_KEY
              value: null
              valueFrom:
                secretKeyRef:
                  name: bpcm-aws-services-api-key
                  key: secret
            - name: APOLLO_INTERNAL_SECRET
              value: null
              valueFrom:
                secretKeyRef:
                  name: apollo-internal-secret
                  key: secret
            - name: HTB_API_KEY
              value: null
              valueFrom:
                secretKeyRef:
                  name: htb-api-key
                  key: secret
            - name: DCS_SUBSCRIPTION_KEY
              value: null
              valueFrom:
                secretKeyRef:
                  name: dcs-subscription-api-key
                  key: secret
            - name: DCS_FLEET_ACCESS_KEY
              value: null
              valueFrom:
                secretKeyRef:
                  name: dcs-fleet-access-key
                  key: secret
            - name: DCS_TOKEN_CLIENT_SECRET
              value: null
              valueFrom:
                secretKeyRef:
                  name: dcs-token-client-secret
                  key: secret
            - name: 'OPEN_ID_ACCESS_ROLE'
              value: '#{OPEN_ID_ACCESS_ROLE}#'
      volumes:
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: rfid-server-aws-secrets
