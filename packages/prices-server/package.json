{"name": "@bp/feature-prices-server", "version": "0.0.1", "private": true, "description": "Prices Server", "main": "./dist/index.js", "scripts": {"prebuild": "npm run clean", "build": "tsc --project tsconfig.build.json", "clean": "rm -rf dist/", "dev": "nodemon --config nodemon.json src/index.ts", "lint": "eslint . --ext .ts", "lint:fix": "npm run lint -- --fix", "start": "node ./dist/index.js", "stop": "npx kill-port 4013", "test": "jest", "test:ci": "jest --ci", "types:check": "tsc --noEmit", "types:generate": "graphql-codegen --config codegen.yml && npm run types:stage", "types:stage": "git add src/types/graphql.ts && git add src/networking/map-service/gql-client/* && git add src/networking/user-service/gql-client/*"}, "dependencies": {"@apollo/subgraph": "^2.7.1", "@aws-sdk/client-dynamodb": "^3.528.0", "@aws-sdk/lib-dynamodb": "^3.525.0", "@aws-sdk/util-dynamodb": "^3.528.0", "apollo-server": "^3.10.2", "apollo-server-core": "^3.10.2", "apollo-server-errors": "^3.3.1", "apollo-server-express": "^3.10.2", "axios": "^1.10.0", "dataloader": "^2.1.0", "dotenv": "^10.0.0", "graphql": "^16.6.0", "graphql-request": "^5.0.0", "ioredis": "^4.28.0", "schemaglue": "^4.1.0", "winston": "^3.8.2"}, "devDependencies": {"@babel/core": "^7.15.8", "@babel/preset-env": "^7.16.11", "@babel/preset-typescript": "^7.16.7", "@bp/eslint-plugin": "^0.0.3", "@graphql-codegen/cli": "^5.0.7", "@graphql-codegen/client-preset": "^4.3.3", "@graphql-codegen/typescript": "^2.8.7", "@types/ioredis": "^4.26.5", "@types/jest": "^29.0.0", "@types/jsonwebtoken": "^8.5.4", "aws-sdk-client-mock": "3.0.1", "aws-sdk-client-mock-jest": "3.0.1", "babel-jest": "^27.3.1", "eslint": "^8.57.0", "jest": "^29.0.0", "nodemon": "^3.1.10", "prettier": "2.7.1", "ts-node": "^10.9.1", "typescript": "^4.8.4"}}