import { contextMock } from '../../utils/mock';
import responseOk from './__mock-responses__/prices-ok.json';
import { fetchDcsGuestPrices, fetchDcsPrices } from './api';
import axios from 'axios';

jest.mock('../../utils/logger');
jest.mock('../../env');
jest.mock('axios', () => {
  const mockPost = jest.fn();
  const mockAxiosInstance = {
    post: mockPost,
    interceptors: {
      request: { use: jest.fn() },
      response: { use: jest.fn() },
    },
  };

  return {
    ...jest.requireActual('axios'),
    create: jest.fn().mockReturnValue(mockAxiosInstance),
    defaults: {
      timeout: 0,
    },
    get: jest.fn(),
    post: jest.fn(),
    __mockPost: mockPost,
  };
});

const mockedAxios = axios as any;

const chargepointId =
  'AT:DCS:CHARGE_POINT:6cab9537-f402-3e86-bf7e-9e899d77d1a9';

describe('fetchDcsPrices()', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  it('should return prices data ', async () => {
    mockedAxios.__mockPost.mockResolvedValueOnce({ data: responseOk });

    const prices = await fetchDcsPrices(
      contextMock(),
      [
        {
          charge_point: chargepointId,
          power_type: 'DC',
          power: 22,
        },
      ],
      '123',
    );

    expect(prices).toBe(responseOk);
  });

  it('should throw an error', async () => {
    jest
      .spyOn(axios, 'post')
      .mockRejectedValueOnce(new Error('Network issues'));

    await expect(
      fetchDcsPrices(
        contextMock(),
        [
          {
            charge_point:
              'AT:DCS:CHARGE_POINT:6cab9537-f402-3e86-bf7e-9e899d77d1a9',
            power_type: 'DC',
            power: 22,
          },
        ],
        '123',
      ),
    ).rejects.toThrow('Failed to fetch prices from DCS API.');
  });
});

describe('fetchDcsGuestPrices()', () => {
  it('should return prices data ', async () => {
    mockedAxios.__mockPost.mockResolvedValueOnce({ data: responseOk });

    const prices = await fetchDcsGuestPrices(contextMock(), [
      {
        charge_point: chargepointId,
        power_type: 'AC',
        power: 22,
      },
    ]);

    expect(prices).toBe(responseOk);
  });

  it('should throw an error', async () => {
    jest
      .spyOn(axios, 'post')
      .mockRejectedValueOnce(new Error('Network issues'));

    await expect(
      fetchDcsGuestPrices(contextMock(), [
        {
          charge_point: chargepointId,
          power_type: 'AC',
          power: 22,
        },
      ]),
    ).rejects.toThrow('Failed to fetch guest prices from DCS API.');
  });
});
