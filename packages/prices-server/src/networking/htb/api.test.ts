import axios from 'axios';

import { contextMock } from '../../utils/mock';
import responseOk from './__mock-responses__/prices-ok.json';
import { fetchHtbPrices } from './api';
jest.mock('../../utils/logger');
jest.mock('../../env');

const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('fetchHtbPrices()', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return price data', async () => {
    const mockInstance = (mockedAxios as any).__mockInstance;
    mockInstance.get.mockResolvedValueOnce({ data: responseOk });

    const prices = await fetchHtbPrices(
      contextMock(),
      ['DE*BPE*ETESTS*01*01', 'DE*BPE*E0F452*01*02'],
      '048955AA565F87',
    );

    expect(prices).toBe(responseOk);
  });

  it('should throw an error when network fails', async () => {
    const mockInstance = (mockedAxios as any).__mockInstance;
    mockInstance.get.mockRejectedValue(new Error('Network issues'));

    await expect(
      fetchHtbPrices(
        contextMock(),
        ['DE*BPE*ETESTS*01*01', 'DE*BPE*E0F452*01*02'],
        '048955AA565F87',
      ),
    ).rejects.toThrow(new Error('Failed to fetch prices from HTB API.'));
  });

  it('should throw an error when HTB API responds with error and message', async () => {
    const errorResponse = {
      response: {
        data: { message: 'HTB error' },
      },
      message: 'Request failed',
    };

    const mockInstance = (mockedAxios as any).__mockInstance;
    mockInstance.get.mockRejectedValue(errorResponse);

    await expect(
      fetchHtbPrices(
        contextMock(),
        ['DE*BPE*ETESTS*01*01', 'DE*BPE*E0F452*01*02'],
        '048955AA565F87',
      ),
    ).rejects.toThrow('Failed to fetch prices from HTB API.');
  });
});
