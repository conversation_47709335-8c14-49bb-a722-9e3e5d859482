import { contextMock } from '../../utils/mock';
import responseOk from './__mock-responses__/prices-ok.json';

jest.mock('../../utils/logger');
jest.mock('../../env');

jest.mock('axios', () => {
  const mockGet = jest.fn();
  const mockAxiosInstance = {
    get: mockGet,
    interceptors: {
      request: { use: jest.fn() },
      response: { use: jest.fn() },
    },
  };

  return {
    ...jest.requireActual('axios'),
    create: jest.fn().mockReturnValue(mockAxiosInstance),
    defaults: {
      timeout: 0,
    },
    get: jest.fn(),
    post: jest.fn(),
    // Export the mock so we can access it in tests
    __mockGet: mockGet,
  };
});

// Import after mocks are set up
import axios from 'axios';
import { fetchHtbPrices } from './api';

const mockedAxios = axios as any;

describe('fetchHtbPrices()', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return price data', async () => {
    mockedAxios.__mockGet.mockResolvedValueOnce({ data: responseOk });

    const prices = await fetchHtbPrices(
      contextMock(),
      ['DE*BPE*ETESTS*01*01', 'DE*BPE*E0F452*01*02'],
      '048955AA565F87',
    );

    expect(prices).toBe(responseOk);
  });

  it('should throw an error when network fails', async () => {
    mockedAxios.__mockGet.mockRejectedValue(new Error('Network issues'));

    await expect(
      fetchHtbPrices(
        contextMock(),
        ['DE*BPE*ETESTS*01*01', 'DE*BPE*E0F452*01*02'],
        '048955AA565F87',
      ),
    ).rejects.toThrow(new Error('Failed to fetch prices from HTB API.'));
  });

  it('should throw an error when HTB API responds with error and message', async () => {
    const errorResponse = {
      response: {
        data: { message: 'HTB error' },
      },
      message: 'Request failed',
    };

    mockedAxios.__mockGet.mockRejectedValue(errorResponse);

    await expect(
      fetchHtbPrices(
        contextMock(),
        ['DE*BPE*ETESTS*01*01', 'DE*BPE*E0F452*01*02'],
        '048955AA565F87',
      ),
    ).rejects.toThrow('Failed to fetch prices from HTB API.');
  });
});
