{"DE*BPE*ETESTS*01*01": {"rateName": "EMPSalesRate", "costId": 4029, "currency": "EUR", "localCurrency": "EUR", "costTotalLocalCurrency": 0.6, "currencyConversionRate": 1, "costTotal": 0.6, "costTime": 0, "costPower": 0.6, "priceStructure": {"energy": {"unit": "Wh", "elements": [{"intervalChange": 0, "intervalCosts": 0.595, "stepCosts": 0.595}], "tax": 1.19}, "time": {"unit": "min", "elements": [], "tax": 1.19}}}, "DE*BPE*E0F452*01*02": {"rateName": "EMPSalesRate", "costId": 5284, "currency": "EUR", "localCurrency": "EUR", "costTotalLocalCurrency": 0.56, "currencyConversionRate": 1, "costTotal": 0.56, "costTime": 0, "costPower": 0.56, "priceStructure": {"energy": {"unit": "Wh", "elements": [{"intervalChange": 0, "intervalCosts": 0.5565, "stepCosts": 0.5565}, {"intervalChange": 102, "intervalCosts": 0.5378, "stepCosts": 0.5378}], "tax": 1.19}, "time": {"unit": "min", "elements": [], "tax": 1.19}}}, "DE*BPE*ENOTAX*01*03": {"rateName": "EMPSalesRate", "costId": 4029, "currency": "EUR", "localCurrency": "EUR", "costTotalLocalCurrency": 0.6, "currencyConversionRate": 1, "costTotal": 0.6, "costTime": 0, "costPower": 0.6, "priceStructure": {"energy": {"unit": "Wh", "elements": [{"intervalChange": 0, "intervalCosts": 0.595, "stepCosts": 0.595}]}, "time": {"unit": "min", "elements": []}}}, "GB*BPE*ETESTS*01*01": {"rateName": "EMPSalesRate", "costId": 5284, "currency": "GBP", "localCurrency": "GBP", "costTotalLocalCurrency": 0.56, "currencyConversionRate": 1, "costTotal": 0.56, "costTime": 0, "costPower": 0.56, "priceStructure": {"energy": {"unit": "Wh", "elements": [{"intervalChange": 0, "intervalCosts": 0.5965, "stepCosts": 0.5965}], "tax": 1.2}, "time": {"unit": "min", "elements": [], "tax": 1.2}}}, "GB*BPE*E0F452*01*02": {"rateName": "EMPSalesRate", "costId": 5284, "currency": "GBP", "localCurrency": "GBP", "costTotalLocalCurrency": 0.56, "currencyConversionRate": 1, "costTotal": 0.56, "costTime": 0, "costPower": 0.56, "priceStructure": {"energy": {"unit": "Wh", "elements": [{"intervalChange": 0, "intervalCosts": 0.7532, "stepCosts": 0.7532}, {"intervalChange": 102, "intervalCosts": 0.8538, "stepCosts": 0.8538}], "tax": 1.2}, "time": {"unit": "min", "elements": [], "tax": 1.2}}}}