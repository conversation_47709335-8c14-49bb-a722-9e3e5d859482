import htbPricingResponse from '../../../networking/htb/__mock-responses__/prices-ok.json';
import htbChargepoint from '../../../networking/map-service/__mock-responses__/htb.json';
import htbChargepointUK from '../../../networking/map-service/__mock-responses__/htb_uk.json';
import { MapChargepoint } from '../../../networking/map-service/types';
import { UserInfo } from '../../../networking/user-service/types';
import { contextMock } from '../../../utils/mock';
import { calcGrossUnitCost, htbPricing } from './ev-prices';

jest.mock('../../../networking/htb/api');
jest.mock('../../../utils/logger');
jest.mock('../../../env');

describe('htbPricing()', () => {
  const chargepoint = htbChargepoint as MapChargepoint;
  const chargepointUK = htbChargepointUK as MapChargepoint;

  const guestUserInfo: UserInfo = {
    status: '200',
    type: 'PAYG',
    roamingEnabled: false,
    tagIds: [],
  };

  const userInfo: UserInfo = {
    status: '200',
    type: 'PAYG',
    roamingEnabled: false,
    tagIds: [{ tagStatus: 'ACTIVE', tagNotes: 'virtual-HTB' }],
  };

  it('should return prices for user without virtual-HTB tagId', async () => {
    const result = await htbPricing(
      contextMock(),
      [chargepoint],
      guestUserInfo,
    );

    expect(result).toEqual([
      {
        chargepointId: htbChargepoint.apolloInternalId,
        connectors: [
          {
            ...chargepoint.connectors[0],
            currency: 'EUR',
            grossUnitCost: 0.70805,
            unit: 'kWh',
          },
          {
            ...chargepoint.connectors[1],
            currency: 'EUR',
            grossUnitCost: 0.6664,
            unit: 'kWh',
          },
        ],
      },
    ]);
  });

  it('should return prices for user with virtual-HTB tagId', async () => {
    const result = await htbPricing(contextMock(), [chargepoint], userInfo);

    expect(result).toEqual([
      {
        chargepointId: htbChargepoint.apolloInternalId,
        connectors: [
          {
            ...chargepoint.connectors[0],
            currency: 'EUR',
            grossUnitCost: 0.70805,
            unit: 'kWh',
          },
          {
            ...chargepoint.connectors[1],
            currency: 'EUR',
            grossUnitCost: 0.6664,
            unit: 'kWh',
          },
        ],
      },
    ]);
  });

  it('should return prices for user without virtual-HTB tagId for UK chargepoint', async () => {
    const result = await htbPricing(
      contextMock(),
      [chargepointUK],
      guestUserInfo,
    );

    expect(result).toEqual([
      {
        chargepointId: htbChargepointUK.apolloInternalId,
        connectors: [
          {
            ...chargepointUK.connectors[0],
            currency: 'GBP',
            grossUnitCost: 0.7158,
            unit: 'kWh',
          },
          {
            ...chargepointUK.connectors[1],
            currency: 'GBP',
            grossUnitCost: 0.672,
            unit: 'kWh',
          },
        ],
      },
    ]);
  });
});

describe('calcGrossUnitCost()', () => {
  it('should use intervalCosts when there is a single price element defined', () => {
    const htbRate = htbPricingResponse['DE*BPE*ETESTS*01*01'];
    const result = calcGrossUnitCost(htbRate);

    expect(result).toEqual(0.70805);
  });

  it('should use costTotal when there is multiple price elements defined', () => {
    const htbRate = htbPricingResponse['DE*BPE*E0F452*01*02'];
    const result = calcGrossUnitCost(htbRate);

    expect(result).toEqual(0.6664);
  });

  it('should use costTotal when there no tax defined', () => {
    const htbRate = htbPricingResponse['DE*BPE*ENOTAX*01*03'];
    const result = calcGrossUnitCost(htbRate);

    expect(result).toEqual(0.595);
  });
});
