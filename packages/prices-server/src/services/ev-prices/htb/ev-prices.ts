import { env } from '../../../env';
import { fetchHtbPrices } from '../../../networking/htb/api';
import { HtbRate } from '../../../networking/htb/types';
import { SupportedCountry } from '../../../networking/user-service/gql-client/graphql';
import type { EvPricingFn } from '../ev-prices';

export const calcGrossUnitCost = (htbRate: HtbRate): number => {
  const { energy } = htbRate.priceStructure;
  const tax = energy.tax ?? 1;

  const netUnitCost =
    energy.elements.length === 1
      ? energy.elements[0].intervalCosts
      : htbRate.costTotal;

  return netUnitCost * tax;
};

export const htbPricing: EvPricingFn = async (ctx, chargepoints, userInfo?) => {
  const activeTag = userInfo?.tagIds?.find(
    (tag) => tag?.tagStatus === 'ACTIVE' && tag.tagNotes === 'virtual-HTB',
  );

  const connectorIds = chargepoints.flatMap(({ connectors }) =>
    connectors.map((c) => c.connectorExternalId),
  );

  // Default to EUR rates if not UK user (GBP rates)
  const guestUserTagId =
    chargepoints[0].site?.siteDetails?.country === SupportedCountry.Uk
      ? env.HTB_AUTHORISATION_IDENTIFIER_UK
      : env.HTB_AUTHORISATION_IDENTIFIER;

  const tagId = activeTag?.tagId ?? guestUserTagId;

  const response = connectorIds.length
    ? await fetchHtbPrices(ctx, connectorIds, tagId)
    : {};

  ctx.logger.debug(`HTB EV prices response`, { response });

  return chargepoints.map((chargepoint) => ({
    chargepointId: chargepoint.apolloInternalId,
    connectors: chargepoint.connectors.map((c) => {
      const htbPrice = response[c.connectorExternalId];

      return {
        connectorInternalId: c.connectorInternalId,
        connectorExternalId: c.connectorExternalId,
        grossUnitCost: htbPrice ? calcGrossUnitCost(htbPrice) : undefined,
        rating: c.rating,
        unit: 'kWh',
        currency: htbPrice?.currency,
      };
    }),
  }));
};
