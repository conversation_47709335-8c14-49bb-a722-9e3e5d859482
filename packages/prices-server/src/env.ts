import 'dotenv/config';

import variables from './variables.json';

const missingVars = variables.filter(
  (variable) => process.env[variable.name] === undefined,
);

if (missingVars.length > 0) {
  console.log('Aborting environment variables missing: ', missingVars);

  if (process.env.NODE_ENV !== 'test') {
    process.exit(-1);
  }
}

const {
  APP_ENV,
  NODE_ENV,
  PRIVATE_GATEWAY_CLUSTER_URL,
  APOLLO_INTERNAL_USER_ID,
  APOLLO_INTERNAL_SECRET,
  PRICES_ELASTICACHE_HOST,
  PRICES_ELASTICACHE_PORT,
  ARAL_STATION_API,
  ARAL_STATION_TOKEN,
  BEENERGISED_API,
  BEENERGISED_TOKEN,
  HTB_AUTHORISATION_IDENTIFIER,
  HTB_AUTHORISATION_IDENTIFIER_UK,
  DCS_TOKEN_URL,
  DCS_TOKEN_CLIENT_ID,
  DCS_TOKEN_CLIENT_SECRET,
  DCS_TOKEN_RESOURCE,
  DCS_USER_SERVICES_URL,
  DCS_SUBSCRIPTION_KEY,
  DCS_OEM_GROUP_ID,
  DCS_COMPANY_CODE_ID,
  DCS_GUEST_TARIFF_ID,
  DYNAMO_DB_ACCESS_KEY,
  DYNAMO_DB_ACCESS_KEY_ID,
  DYNAMO_DB_REGION,
  DYNAMO_DB_URL,
  TARIFF_DB_TABLE_NAME,
} = process.env;

const AVAILABLE_ENV_VARS = {
  APP_ENV,
  NODE_ENV,
  PRIVATE_GATEWAY_CLUSTER_URL,
  APOLLO_INTERNAL_USER_ID,
  APOLLO_INTERNAL_SECRET,
  PRICES_ELASTICACHE_HOST,
  PRICES_ELASTICACHE_PORT,
  ARAL_STATION_API,
  ARAL_STATION_TOKEN,
  BEENERGISED_API,
  BEENERGISED_TOKEN,
  HTB_AUTHORISATION_IDENTIFIER,
  HTB_AUTHORISATION_IDENTIFIER_UK,
  DCS_TOKEN_URL,
  DCS_TOKEN_CLIENT_ID,
  DCS_TOKEN_CLIENT_SECRET,
  DCS_TOKEN_RESOURCE,
  DCS_USER_SERVICES_URL,
  DCS_SUBSCRIPTION_KEY,
  DCS_OEM_GROUP_ID,
  DCS_COMPANY_CODE_ID,
  DCS_GUEST_TARIFF_ID,
  DYNAMO_DB_ACCESS_KEY,
  DYNAMO_DB_ACCESS_KEY_ID,
  DYNAMO_DB_REGION,
  DYNAMO_DB_URL,
  TARIFF_DB_TABLE_NAME,
};

export type EnvVars = Record<keyof typeof AVAILABLE_ENV_VARS, string>;

export const env = AVAILABLE_ENV_VARS as EnvVars;
