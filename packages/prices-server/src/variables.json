[{"name": "APP_ENV", "source": "ado-variable-group", "sourceName": "APP_ENV"}, {"name": "NODE_ENV", "source": "ado-variable-group", "sourceName": "NODE_ENV"}, {"name": "PRIVATE_GATEWAY_CLUSTER_URL", "source": "ado-variable-group", "sourceName": "PRIVATE_GATEWAY_CLUSTER_URL"}, {"name": "APOLLO_INTERNAL_USER_ID", "source": "ado-variable-group", "sourceName": "APOLLO_INTERNAL_USER_ID"}, {"name": "PRICES_ELASTICACHE_HOST", "source": "ado-variable-group", "sourceName": "PRICES_ELASTICACHE_HOST"}, {"name": "PRICES_ELASTICACHE_PORT", "source": "ado-variable-group", "sourceName": "PRICES_ELASTICACHE_PORT"}, {"name": "ARAL_STATION_API", "source": "ado-variable-group", "sourceName": "ARAL_STATION_API"}, {"name": "BEENERGISED_API", "source": "ado-variable-group", "sourceName": "BEENERGISED_API"}, {"name": "HTB_AUTHORISATION_IDENTIFIER_UK", "source": "ado-variable-group", "sourceName": "HTB_AUTHORISATION_IDENTIFIER_UK"}, {"name": "HTB_AUTHORISATION_IDENTIFIER", "source": "ado-variable-group", "sourceName": "HTB_AUTHORISATION_IDENTIFIER"}, {"name": "DCS_TOKEN_URL", "source": "ado-variable-group", "sourceName": "DCS_TOKEN_URL"}, {"name": "DCS_TOKEN_CLIENT_ID", "source": "ado-variable-group", "sourceName": "DCS_TOKEN_CLIENT_ID"}, {"name": "DCS_TOKEN_RESOURCE", "source": "ado-variable-group", "sourceName": "DCS_TOKEN_RESOURCE"}, {"name": "DCS_USER_SERVICES_URL", "source": "ado-variable-group", "sourceName": "DCS_USER_SERVICES_URL"}, {"name": "DCS_OEM_GROUP_ID", "source": "ado-variable-group", "sourceName": "DCS_OEM_GROUP_ID"}, {"name": "DCS_COMPANY_CODE_ID", "source": "ado-variable-group", "sourceName": "DCS_COMPANY_CODE_ID"}, {"name": "DCS_GUEST_TARIFF_ID", "source": "ado-variable-group", "sourceName": "DCS_GUEST_TARIFF_ID"}, {"name": "APOLLO_INTERNAL_SECRET", "source": "aws-secrets-manager", "sourceName": "Prices-ApolloInternal-Secret"}, {"name": "ARAL_STATION_TOKEN", "source": "aws-secrets-manager", "sourceName": "Prices-AralStation-Token"}, {"name": "BEENERGISED_TOKEN", "source": "aws-secrets-manager", "sourceName": "Prices-BeEnergised-Token"}, {"name": "DCS_SUBSCRIPTION_KEY", "source": "aws-secrets-manager", "sourceName": "Prices-DcsSubscription-Key"}, {"name": "DCS_TOKEN_CLIENT_SECRET", "source": "aws-secrets-manager", "sourceName": "Prices-DcsTokenClient-Secret"}, {"name": "TARIFF_DB_TABLE_NAME", "source": "ado-variable-group", "sourceName": "TARIFF_DB_TABLE_NAME"}, {"name": "DYNAMO_DB_ACCESS_KEY_ID", "source": "local", "sourceName": "DYNAMO_DB_ACCESS_KEY_ID"}, {"name": "DYNAMO_DB_ACCESS_KEY", "source": "local", "sourceName": "DYNAMO_DB_ACCESS_KEY"}, {"name": "DYNAMO_DB_URL", "source": "ado-variable-group", "sourceName": "DYNAMO_DB_URL"}, {"name": "DYNAMO_DB_REGION", "source": "ado-variable-group", "sourceName": "DYNAMO_DB_REGION"}]