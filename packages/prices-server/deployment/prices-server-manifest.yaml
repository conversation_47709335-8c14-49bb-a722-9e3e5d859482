---
apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: prices-server-aws-secrets
  namespace: prices-server
spec:
  provider: aws
  parameters:
    ## Follow PascalCase for Service, SecretName and SecretType
    objects: |
      - objectName: '${env}-v${version_number}-Prices-ApolloInternal-Secret'
        objectType: "secretsmanager"
        jmesPath:
          - path: "secret"
            objectAlias: "ApolloInternal-Secret"
      - objectName: '${env}-v${version_number}-Prices-AralStation-Token'
        objectType: "secretsmanager"
        jmesPath:
          - path: "secret"
            objectAlias: "AralStation-Token"
      - objectName: '${env}-v${version_number}-Prices-BeEnergised-Token'
        objectType: "secretsmanager"
        jmesPath:
          - path: "secret"
            objectAlias: "BeEnergised-Token"
      - objectName: '${env}-v${version_number}-Prices-DcsSubscription-Key'
        objectType: "secretsmanager"
        jmesPath:
          - path: "secret"
            objectAlias: "DcsSubscription-Key"
      - objectName: '${env}-v${version_number}-Prices-DcsTokenClient-Secret'
        objectType: "secretsmanager"
        jmesPath:
          - path: "secret"
            objectAlias: "DcsTokenClient-Secret"
  secretObjects:
    ## SECRET NAME MUST USE - rather than _ and must all be lowercase due to syntax requirements
    - secretName: apollo-internal-secret ##secretname seperated by - where its seperated by PascalCase
      type: Opaque
      data:
        - objectName: ApolloInternal-Secret
          key: secret
    - secretName: aral-station-token
      type: Opaque
      data:
        - objectName: AralStation-Token
          key: secret
    - secretName: be-energised-token
      type: Opaque
      data:
        - objectName: BeEnergised-Token
          key: secret
    - secretName: dcs-subscription-key
      type: Opaque
      data:
        - objectName: DcsSubscription-Key
          key: secret
    - secretName: dcs-token-client-secret
      type: Opaque
      data:
        - objectName: DcsTokenClient-Secret
          key: secret
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prices-server-deployment-v${version_number}
  annotations:
    reloader.stakater.com/auto: 'true'
spec:
  selector:
    matchLabels:
      app: prices-server-v${version_number}
  replicas: #{REPLICAS_SMALL}#
  template:
    metadata:
      annotations:
        sidecar.opentelemetry.io/inject: 'true'
        instrumentation.opentelemetry.io/inject-nodejs: 'true'
      labels:
        app: prices-server-v${version_number}
    spec:
      serviceAccountName: prices-server-service-account
      containers:
        - name: prices-server-v${version_number}
          image: ************.dkr.ecr.eu-west-1.amazonaws.com/prices-server:${image_tag}
          imagePullPolicy: #{IMAGE_PULL_POLICY}#
          resources:
            requests:
              cpu: #{REPLICAS_CPU_REQUEST_MEDIUM}#
          ports:
            - containerPort: 4013
          volumeMounts:
            - name: secrets-store-inline
              mountPath: /mnt/secrets-store
              readOnly: true
          readinessProbe:
            tcpSocket:
              port: 4013
            initialDelaySeconds: 60
            periodSeconds: 60
            timeoutSeconds: 50
          env:
            - name: OTEL_SERVICE_NAME
              value: 'prices-server-${env}'
            - name: APP_ENV
              value: '#{APP_ENV}#'
            - name: NODE_ENV
              value: '#{NODE_ENV}#'
            - name: PRIVATE_GATEWAY_CLUSTER_URL
              value: '#{PRIVATE_GATEWAY_CLUSTER_URL}#'
            - name: APOLLO_INTERNAL_USER_ID
              value: '#{APOLLO_INTERNAL_USER_ID}#'
            - name: PRICES_ELASTICACHE_HOST
              value: '#{PRICES_ELASTICACHE_HOST}#'
            - name: PRICES_ELASTICACHE_PORT
              value: '#{PRICES_ELASTICACHE_PORT}#'
            - name: ARAL_STATION_API
              value: '#{ARAL_STATION_API}#'
            - name: BEENERGISED_API
              value: '#{BEENERGISED_API}#'
            - name: HTB_AUTHORISATION_IDENTIFIER
              value: '#{HTB_AUTHORISATION_IDENTIFIER}#'
            - name: HTB_AUTHORISATION_IDENTIFIER_UK
              value: '#{HTB_AUTHORISATION_IDENTIFIER_UK}#'
            - name: DCS_TOKEN_URL
              value: '#{DCS_TOKEN_URL}#'
            - name: DCS_TOKEN_CLIENT_ID
              value: '#{DCS_TOKEN_CLIENT_ID}#'
            - name: DCS_TOKEN_RESOURCE
              value: '#{DCS_TOKEN_RESOURCE}#'
            - name: DCS_USER_SERVICES_URL
              value: '#{DCS_USER_SERVICES_URL}#'
            - name: DCS_OEM_GROUP_ID
              value: '#{DCS_OEM_GROUP_ID}#'
            - name: DCS_COMPANY_CODE_ID
              value: '#{DCS_COMPANY_CODE_ID}#'
            - name: DCS_GUEST_TARIFF_ID
              value: '#{DCS_GUEST_TARIFF_ID}#'
            - name: TARIFF_DB_TABLE_NAME
              value: '#{TARIFF_DB_TABLE_NAME}#'
            - name: DYNAMO_DB_REGION
              value: '#{DYNAMO_DB_REGION}#'
            - name: DYNAMO_DB_URL
              value: '#{DYNAMO_DB_URL}#'
            - name: DYNAMO_DB_ACCESS_KEY_ID
              value: ''
            - name: DYNAMO_DB_ACCESS_KEY
              value: ''
            - name: APOLLO_INTERNAL_SECRET
              value: null
              valueFrom:
                secretKeyRef:
                  name: apollo-internal-secret
                  key: secret
            - name: ARAL_STATION_TOKEN
              value: null
              valueFrom:
                secretKeyRef:
                  name: aral-station-token
                  key: secret
            - name: BEENERGISED_TOKEN
              value: null
              valueFrom:
                secretKeyRef:
                  name: be-energised-token
                  key: secret
            - name: DCS_SUBSCRIPTION_KEY
              value: null
              valueFrom:
                secretKeyRef:
                  name: dcs-subscription-key
                  key: secret
            - name: DCS_TOKEN_CLIENT_SECRET
              value: null
              valueFrom:
                secretKeyRef:
                  name: dcs-token-client-secret
                  key: secret
      volumes:
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: prices-server-aws-secrets
