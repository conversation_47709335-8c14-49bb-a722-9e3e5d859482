deployName: 'prices-server'
serverName: 'prices-server'
namespace: 'prices-server'
imageRepo: prices-server
versionNumber: 'x'
replicas: #{REPLICAS_SMALL}#
rolloutReplicas: #{HPA_REPLICAS_SMALL}#

shared:
  hpapdbName: prices-server
  minReplicas: #{HPA_REPLICAS_SMALL}#
  maxReplicas: #{REPLICAS_MAX_MEDIUM}#
  averageUtilization: 100
  microservicePort: 4013

serviceAccount: prices-server-service-account

rollouts:
  enabled: #{HELM_ENABLE_ROLLOUTS}#

includeSchema: true

service:
  clusterIp:
    enabled: true
  loadBalancer:
    enabled: false
    tags: 'placeholder'

deployment:
  hpa:
    enabled: false
  servers:
    enabled: true

secrets:
  - objectName: '{{ .Values.environment }}-v{{ .Values.versionNumber }}-Prices-ApolloInternal-Secret'
    alias: ApolloInternal-Secret
    k8sSecretName: apollo-internal-secret
  - objectName: '{{ .Values.environment }}-v{{ .Values.versionNumber }}-Prices-AralStation-Token'
    alias: AralStation-Token
    k8sSecretName: aral-station-token
  - objectName: '{{ .Values.environment }}-v{{ .Values.versionNumber }}-Prices-BeEnergised-Token'
    alias: BeEnergised-Token
    k8sSecretName: be-energised-token
  - objectName: '{{ .Values.environment }}-v{{ .Values.versionNumber }}-Prices-DcsSubscription-Key'
    alias: DcsSubscription-Key
    k8sSecretName: dcs-subscription-key
  - objectName: '{{ .Values.environment }}-v{{ .Values.versionNumber }}-Prices-DcsTokenClient-Secret'
    alias: DcsTokenClient-Secret
    k8sSecretName: dcs-token-client-secret

env:
  - name: OTEL_SERVICE_NAME
    value: 'prices-server-{{ .Values.environment }}'
  - name: APP_ENV
    value: '#{APP_ENV}#'
  - name: NODE_ENV
    value: '#{NODE_ENV}#'
  - name: PRIVATE_GATEWAY_CLUSTER_URL
    value: '#{PRIVATE_GATEWAY_CLUSTER_URL}#'
  - name: APOLLO_INTERNAL_USER_ID
    value: '#{APOLLO_INTERNAL_USER_ID}#'
  - name: PRICES_ELASTICACHE_HOST
    value: '#{PRICES_ELASTICACHE_HOST}#'
  - name: PRICES_ELASTICACHE_PORT
    value: '#{PRICES_ELASTICACHE_PORT}#'
  - name: ARAL_STATION_API
    value: '#{ARAL_STATION_API}#'
  - name: BEENERGISED_API
    value: '#{BEENERGISED_API}#'
  - name: HTB_AUTHORISATION_IDENTIFIER
    value: '#{HTB_AUTHORISATION_IDENTIFIER}#'
  - name: HTB_AUTHORISATION_IDENTIFIER_UK
    value: '#{HTB_AUTHORISATION_IDENTIFIER_UK}#'
  - name: DCS_TOKEN_URL
    value: '#{DCS_TOKEN_URL}#'
  - name: DCS_TOKEN_CLIENT_ID
    value: '#{DCS_TOKEN_CLIENT_ID}#'
  - name: DCS_TOKEN_RESOURCE
    value: '#{DCS_TOKEN_RESOURCE}#'
  - name: DCS_USER_SERVICES_URL
    value: '#{DCS_USER_SERVICES_URL}#'
  - name: DCS_OEM_GROUP_ID
    value: '#{DCS_OEM_GROUP_ID}#'
  - name: DCS_COMPANY_CODE_ID
    value: '#{DCS_COMPANY_CODE_ID}#'
  - name: DCS_GUEST_TARIFF_ID
    value: '#{DCS_GUEST_TARIFF_ID}#'
  - name: TARIFF_DB_TABLE_NAME
    value: '#{TARIFF_DB_TABLE_NAME}#'
  - name: DYNAMO_DB_REGION
    value: '#{DYNAMO_DB_REGION}#'
  - name: DYNAMO_DB_URL
    value: '#{DYNAMO_DB_URL}#'
  - name: DYNAMO_DB_ACCESS_KEY_ID
    value: ''
  - name: DYNAMO_DB_ACCESS_KEY
    value: ''
  - name: APOLLO_INTERNAL_SECRET
    value: null
    valueFrom:
      secretKeyRef:
        name: apollo-internal-secret
        key: secret
  - name: ARAL_STATION_TOKEN
    value: null
    valueFrom:
      secretKeyRef:
        name: aral-station-token
        key: secret
  - name: BEENERGISED_TOKEN
    value: null
    valueFrom:
      secretKeyRef:
        name: be-energised-token
        key: secret
  - name: DCS_SUBSCRIPTION_KEY
    value: null
    valueFrom:
      secretKeyRef:
        name: dcs-subscription-key
        key: secret
  - name: DCS_TOKEN_CLIENT_SECRET
    value: null
    valueFrom:
      secretKeyRef:
        name: dcs-token-client-secret
        key: secret
