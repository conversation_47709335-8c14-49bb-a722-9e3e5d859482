import { AxiosStatic } from 'axios';

const mockAxios = jest.createMockFromModule<AxiosStatic>('axios');

// Create a mock axios instance with interceptors
const mockAxiosInstance = {
  ...mockAxios,
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  patch: jest.fn(),
  interceptors: {
    request: { use: jest.fn() },
    response: { use: jest.fn() },
  },
};

mockAxios.create = jest.fn(() => mockAxiosInstance);

// Export the instance for easy access in tests
(mockAxios as any).__mockInstance = mockAxiosInstance;

export default mockAxios;
