import { AxiosStatic, AxiosInstance } from 'axios';

const mockAxios = jest.createMockFromModule<AxiosStatic>('axios');

const mockAxiosInstance = {
  ...mockAxios,
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  patch: jest.fn(),
  head: jest.fn(),
  options: jest.fn(),
  request: jest.fn(),
  getUri: jest.fn(),
  postForm: jest.fn(),
  putForm: jest.fn(),
  patchForm: jest.fn(),
  interceptors: {
    request: { use: jest.fn() },
    response: { use: jest.fn() },
  },
} as unknown as AxiosInstance;

mockAxios.create = jest.fn(() => mockAxiosInstance);

(mockAxios as any).__mockInstance = mockAxiosInstance;

export default mockAxios;
