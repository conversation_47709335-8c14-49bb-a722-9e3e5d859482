import axios from 'axios';
import { gql, request } from 'graphql-request';

import {
  SaveTransactionIdInternalArgs,
  SaveTransactionIdInternalResponse,
} from '../../common/interfaces';
import env from '../../env';
import { logger } from '../../utils/logger';

interface IPaymentDetails {
  paymentId: string;
  preAuthStatus: string;
  chargeStarted?: boolean;
  voidTransaction?: boolean;
}

interface IDetailsRes {
  paymentId: string;
  preAuthStatus: string;
  chargeStarted: boolean;
  voidTransaction: boolean;
}

// Internal BP Pay endpoint
// TO-DO this should be a graphql service endpoint not REST
const {
  BPPAY_CLUSTER_REST_URL,
  APOLLO_INTERNAL_SECRET = 'secret',
  PRIVATE_GATEWAY_CLUSTER_URL = 'http://localhost:4030/graphql',
} = env;

// Axios config
const config = {
  headers: {
    'Content-Type': 'application/json',
  },
};

/**
 * Returns a users payment details by querying their user Id
 * @param userId user Id to query for payment details
 */
export const getPaymentDetails = (userId: string): Promise<IPaymentDetails> =>
  axios
    .get<IDetailsRes>(`${BPPAY_CLUSTER_REST_URL}/userdata/${userId}`, config)
    .then((res) => {
      const { paymentId, preAuthStatus, chargeStarted, voidTransaction } =
        res.data;
      if (!paymentId) {
        throw new Error('No paymentId returned');
      }
      return { paymentId, preAuthStatus, chargeStarted, voidTransaction };
    })
    .catch((e) => {
      throw new Error(
        `Payments database request failure getting payment details ${e.message}`,
      );
    });

// SAVE TRANSACTION INTERNAL ID
export const SAVE_TRANSACTION_INTERNAL_ID = gql`
  mutation saveTransactionIdInternal(
    $paymentId: String
    $transactionId: String
    $transactionNumber: String
    $userId: String
    $authId: String
    $connectorId: String
    $chargeStatus: ChargeStatus
    $chargeSessionStart: String
    $chargeSessionId: String
  ) {
    saveTransactionIdInternal(
      paymentId: $paymentId
      transactionId: $transactionId
      transactionNumber: $transactionNumber
      userId: $userId
      authId: $authId
      connectorId: $connectorId
      chargeStatus: $chargeStatus
      chargeSessionStart: $chargeSessionStart
      chargeSessionId: $chargeSessionId
    ) {
      status
      message
    }
  }
`;

export const saveTransactionInternalId = async ({
  paymentId,
  userId,
  authId,
  transactionId,
  transactionNumber,
  chargeSessionId,
  chargeStatus,
  connectorId,
  chargeSessionStart,
}: SaveTransactionIdInternalArgs) => {
  try {
    const { saveTransactionIdInternal }: SaveTransactionIdInternalResponse =
      await request(
        PRIVATE_GATEWAY_CLUSTER_URL,
        SAVE_TRANSACTION_INTERNAL_ID,
        {
          userId,
          authId,
          paymentId,
          transactionId,
          transactionNumber,
          chargeSessionId,
          chargeStatus,
          connectorId,
          chargeSessionStart,
        },
        {
          'x-apollo-internal-secret': APOLLO_INTERNAL_SECRET,
        },
      );

    return {
      status: saveTransactionIdInternal.status,
      message: saveTransactionIdInternal.message,
    };
  } catch (e) {
    throw new Error(
      `SaveTransactionInternalId request failed with error: ${e}`,
    );
  }
};

export const getPaymentId = async (
  userId: string,
  logTraceId: string,
  chargeEventName: string,
) => {
  const { paymentId, preAuthStatus } = await getPaymentDetails(userId).catch(
    (e: Error) => {
      logger.error(
        `🚨 Error encountered: logTraceId ${logTraceId} - ${chargeEventName} failed to get paymentId from DynamoDB`,
        e,
      );
      throw e;
    },
  );

  if (!preAuthStatus) {
    const e = new Error(
      `Error encountered: logTraceId ${logTraceId} - ${chargeEventName} no preAuthStatus returned`,
    );
    logger.error(e);
    throw e;
  }

  return paymentId;
};
