/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  let subqueryDE = knex('customer.origin_type')
    .select('origin_type_id')
    .where('country', 'DE');

  return knex('customer.entitlements')
    .whereIn('origin_entitlement_id', subqueryDE)
    .update({
      rfid_default_providers: knex.raw(
        `CASE
           WHEN NOT ('DCS' = ANY(rfid_default_providers))
           THEN array_append(rfid_default_providers, 'DCS')
           ELSE rfid_default_providers
         END`,
      ),
    });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  let subqueryDE = knex('customer.origin_type')
    .select('origin_type_id')
    .where('country', 'DE');

  return knex('customer.entitlements')
    .whereIn('origin_entitlement_id', subqueryDE)
    .update({
      rfid_default_providers: knex.raw(
        `array_remove(rfid_default_providers, 'DCS')`,
      ),
    });
};
