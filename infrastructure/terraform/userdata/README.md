This is the userdata terraform stack built for this ticket: [USER STORY 8676933](https://dev.azure.com/bp-digital/bp_pulse/_sprints/backlog/bp%20pulse%20Global%20Platform%20Team/bp_pulse/Global%20Platform/2025/Q3%20-%202025/Sprint%2015?System.AssignedTo=wayne.tan%40bp.com&workitem=8676933)

This is part of a spike to convert the part of the userdata CloudFormation stack to Terraform. Currently it's already deployed to the DevOps envinronment only.

The Terraform backend state is stored on an S3 bucket: tfstate-052161107979-devops-platform
The following resources in the DevOps environment are controlled by Terraform at the moment as part of this stack:

- RDS Aurora cluster
- DB instances(one read and one write)
- DB Subnet Group
- DB Security Group
- DB Secret Rotation
- DB Cluster and Instance Parameter Groups
- Cluster roles association
