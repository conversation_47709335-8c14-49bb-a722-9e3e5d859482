
variable "cluster_identifier" {
  type        = string
  description = "The identifier for the RDS cluster."
}

variable "env" {
  type        = string
  description = "The environment for the RDS cluster (e.g., dev, prod)."
}

variable "spoke_name" {
  type        = string
  description = "The spoke name for the AWS account where the RDS cluster is deployed."
}

variable "service" {
  type        = string
  description = "The service name for the RDS cluster."
  default     = "user-data"
}

variable "default_kms_key" {
  type        = bool
  description = "Whether to use the default KMS key for encryption."
  default     = true
}

variable "service_security_group_ids" {
  type        = list(string)
  description = "The security group IDs for the service."
  default     = []
}

variable "allow_vpc_cidr_blocks_access" {
  type        = bool
  description = "Whether to allow ingress access to the database from the VPC cidr blocks"
  default     = true
}

variable "engine_version" {
  type        = string
  description = "The version of the database engine."
  default     = "15.10"
}

variable "database_name" {
  type        = string
  description = "The name of the database to create in the RDS cluster."
}
variable "port" {
  type        = number
  description = "The port on which the RDS cluster listens."
  default     = 5432
}

variable "backup_retention_period" {
  type        = number
  description = "The number of days to retain backups for the RDS cluster."
  default     = 10
}

variable "instance_count" {
  type        = number
  description = "The number of instances in the RDS cluster."
  default     = 2
}
variable "scaling_max_capacity" {
  type        = number
  description = "The maximum capacity for serverless v2 scaling."
  default     = 128

}
variable "scaling_min_capacity" {
  type        = number
  description = "The minimum capacity for serverless v2 scaling."
  default     = 0.5

}

variable "vpc_name" {
  type        = string
  description = "The name of the VPC where the RDS cluster will be created."
}

variable "cluster_feature_execution_roles" {
  description = "Map of IAM roles and supported feature names to associate with the cluster"
  type        = map(map(string))
  default     = {}
}
