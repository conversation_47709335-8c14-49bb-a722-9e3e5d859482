terraform {
  required_version = ">= 1.11.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 6.0"
    }
  }
}


provider "aws" {
  profile = "WS-00F0-role_DEVOPS"
  region  = "eu-west-2"
  default_tags {
    tags = {
      pod             = "emsp"
      team            = "platform"
      market          = "global"
      service         = "userData"
      component       = "database"
      environment     = "devops"
      iac             = "https://dev.azure.com/bp-digital/bp_pulse/_git/charge-master-mobile-backend path=/infrastructure/terraform/userdata"
      iac_commit_hash = "dirty"
    }
  }
}
