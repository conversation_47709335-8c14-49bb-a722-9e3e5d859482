module "rds_cluster_provisioned" {
  source  = "app.terraform.io/bppulse/rds-aurora/platform"
  version = "1.6.6"

  # Cluster Identification & Networking
  cluster_identifier              = var.cluster_identifier
  vpc_name                        = var.vpc_name
  env                             = var.env
  spoke_name                      = var.spoke_name
  service                         = var.service
  cluster_feature_execution_roles = var.cluster_feature_execution_roles
  allow_vpc_cidr_blocks_access    = var.allow_vpc_cidr_blocks_access

  # Security & Access
  service_security_group_ids = var.service_security_group_ids
  default_kms_key            = var.default_kms_key

  # Database Engine & Versioning
  engine_version = var.engine_version

  # Cluster & Instance Configuration
  instance_count = var.instance_count
  port           = var.port


  # Maintenance & Backups
  backup_retention_period = var.backup_retention_period
  # Logging & Monitoring

  # Serverless v2 Scaling

  scaling_min_capacity = var.scaling_min_capacity
  scaling_max_capacity = var.scaling_max_capacity

  # Database Credentials & Management
  database_name                                          = var.database_name
  master_user_password_rotation_automatically_after_days = 10
}

# Leaving the import statements commented out for later use in high environments.
# import {
#   to = module.rds_cluster_provisioned.aws_db_parameter_group.this[0]
#   id = "ws-00f0-userdata-stack-vx-devops-rdsdbinstanceparametergroup-v2p6qce4qbjz"
# }

# import {
#   to = module.rds_cluster_provisioned.aws_rds_cluster_parameter_group.this[0]
#   id = "ws-00f0-userdata-stack-vx-devops-rdsdbparametergroup-nxzl1yy0dmhj"
# }

# import {
#   to = module.rds_cluster_provisioned.aws_security_group.this[0]
#   id = "sg-067ef1709a78205fa"
# }

# import {
#   to = module.rds_cluster_provisioned.aws_db_subnet_group.this
#   id = "ws-00f0-user-data-subnet-group-devops"
# }

# import {
#   to = module.rds_cluster_provisioned.aws_rds_cluster.this[0]
#   id = "ws-00f0-user-data-rds-migrated-devops"
# }

# import {
#   to = module.rds_cluster_provisioned.aws_rds_cluster_instance.this[0]
#   id = "ws-00f0-userdata-stack-vx-devops-rdsinstance1-uiestghe44m1"
# }
# import {
#   to = module.rds_cluster_provisioned.aws_rds_cluster_instance.this[1]
#   id = "ws-00f0-userdata-stack-vx-devops-rdsinstance2-6dpqiaj1aygs"
# }
