cluster_identifier      = "ws-00f0-user-data-rds-migrated-devops"
database_name           = "UserDataAuroradevops"
backup_retention_period = 10
vpc_name                = "StandaloneV3-3-Tier-3-AZ-Non-Web"
env                     = "devops"
spoke_name              = "ws-00f0"
cluster_feature_execution_roles = {
  s3import_role = {
    feature_name = "s3Import"
    role_arn     = "arn:aws:iam::052161107979:role/WS-00F0_RDS_devops"
  },
  lambda_role = {
    feature_name = "Lambda"
    role_arn     = "arn:aws:iam::052161107979:role/WS-00F0-Rds-Producer-devops"
} }
