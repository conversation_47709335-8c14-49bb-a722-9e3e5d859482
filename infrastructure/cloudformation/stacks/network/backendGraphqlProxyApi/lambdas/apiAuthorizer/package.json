{"name": "authoriser", "private": true, "scripts": {"prebuild": "npm run clean", "build": "npm i && tsc && cp package.json dist/package.json && cp package-lock.json dist/package-lock.json && cd dist && npm ci --production", "clean": "rm -rf dist/", "start": "ts-node src/run-local.ts", "test": "jest", "test:dev": "jest --watch", "types:check": "tsc --noEmit"}, "dependencies": {"axios": "^1.10.0", "jsonwebtoken": "^8.5.1", "jwks-rsa": "^2.0.3"}, "devDependencies": {"@babel/core": "^7.23.2", "@babel/preset-env": "^7.16.11", "@babel/preset-typescript": "^7.16.7", "@types/aws-lambda": "^8.10.138", "@types/jest": "^29.5.12", "@types/node-jose": "^1.1.13", "jest": "^29.7.0", "lambda-local": "^2.2.0", "ts-node": "^10.9.2", "typescript": "^5.4.5"}}