AWSTemplateFormatVersion: 2010-09-09

Transform: AWS::Serverless-2016-10-31

Description: 'Creates the infrastructure for the user data functionality.'

Parameters:
  AwsLogicalAccountNameLowerCase:
    Type: String
  AwsLogicalAccountNameUpperCase:
    Type: String
  Environment:
    Type: String
  UnversionedApiVersion:
    Type: String
    Default: 'vx'
  Stack:
    Type: String
    Default: 'UserData'
  StackName:
    Type: String
  RDSInvokeLambdaRoleArn:
    Type: String
    Description: ARN of the IAM role for RDS to invoke Lambda
  EnableRDSPerformanceInsights:
    Type: String
    Default: 'false'
  EnableRDSPerformanceInsightsDays:
    Type: Number
    Default: 7
  VpcId:
    Type: String
  VpcCidr:
    Type: String
  Service:
    Type: String
    Default: 'userData'
  UserDataAuroraMasterUsername:
    NoEcho: 'true'
    Type: String
    MinLength: '1'
    MaxLength: '16'
    AllowedPattern: '[a-zA-Z][a-zA-Z0-9]*'
    ConstraintDescription: must begin with a letter and contain only alphanumeric characters.
  UserDataAuroraMasterPassword:
    Type: String
  RDSSubnetGroupIds:
    Type: List<String>
  LambdaSecurityGroup:
    Type: List<String>
  LambdaRoleArn:
    Type: String
  ExternalSubnetIds:
    Type: List<String>
  NodeEnv:
    Type: String
  TokenEndpoint:
    Type: String
  TokenApiKey:
    Type: String
  IdpBaseUrl:
    Type: String
  NotificationProducerLambdaARN:
    Type: String
  DESubsTariffBasedDiscount:
    Type: String
  BeEnergisedTestEnvironment:
    Type: String
  SecretRotationScheduleDays:
    Type: Number
    Default: 90
    Description: Number of days to wait before rotating the secret. The default is 90 days.

Conditions:
  UseProdCondition: !Or
    - !Equals [!Ref Environment, prod]
    - !Equals [!Ref Environment, us-prod]
  IsNotProd: !Not [!Condition UseProdCondition]
  IsPerformanceInsightsEnabled:
    !Equals [!Ref EnableRDSPerformanceInsights, 'true']
  IsNotDevops: !Not [!Equals [!Ref Environment, 'devops']]

Mappings:
  RDSEndpointMapping:
    WS-00CS:
      Endpoint: cn7uvmwfszrg
    WS-00F0:
      Endpoint: cwaxcjglhxaq
    WS-01PL:
      Endpoint: cyugevwqhh4g
    WS-02HD:
      Endpoint: clgyeo2yme79
    WS-02P1:
      Endpoint: cnk62ksqkuy1

Resources:
  RDSSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Condition: IsNotDevops
    Properties:
      GroupDescription: User Data RDS SG
      GroupName: !Sub ${AwsLogicalAccountNameUpperCase}-User-Data-RDS-SG-${Environment}
      SecurityGroupEgress:
        - CidrIp: !Ref VpcCidr
          Description: Open to VPC
          IpProtocol: -1
      SecurityGroupIngress:
        - CidrIp: !Ref VpcCidr
          Description: Open to VPC
          IpProtocol: tcp
          FromPort: 5432
          ToPort: 5432
      VpcId: !Ref VpcId
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: 'unversioned'
        - Key: 'stack'
          Value: !Ref StackName

  RDSSubnetGroup:
    Type: AWS::RDS::DBSubnetGroup
    Condition: IsNotDevops
    Properties:
      DBSubnetGroupDescription: A Subnet Group for RDS
      DBSubnetGroupName: !Sub ${AwsLogicalAccountNameLowerCase}-user-data-subnet-group-${Environment}
      SubnetIds: !Ref RDSSubnetGroupIds
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: 'unversioned'
        - Key: 'stack'
          Value: !Ref StackName

  RDSDBParameterGroup:
    Type: 'AWS::RDS::DBClusterParameterGroup'
    Condition: IsNotDevops
    Properties:
      Description: Postgresql Parameter Group
      Family: aurora-postgresql15
      Parameters:
        shared_preload_libraries: 'pg_stat_statements,pgaudit' # Ensure both extensions are loaded
        pgaudit.role: 'rds_pgaudit' # Required AWS role for pgaudit
        pgaudit.log: 'all' # Log all statements

  RDSDBInstanceParameterGroup:
    Type: 'AWS::RDS::DBParameterGroup'
    Condition: IsNotDevops
    Properties:
      Description: Postgresql Instance Parameter Group
      Family: aurora-postgresql15
      Parameters:
        shared_preload_libraries: 'pg_stat_statements,pgaudit' # Ensure both extensions are loaded
        pgaudit.role: 'rds_pgaudit' # Required AWS role for pgaudit
        pgaudit.log: 'all' # Log all statements

  RDSInstance1:
    Type: 'AWS::RDS::DBInstance'
    DeletionPolicy: Retain
    Condition: IsNotDevops
    Properties:
      DBClusterIdentifier: !Ref RDSClusterProvisioned
      Engine: aurora-postgresql
      EngineVersion: '15.10'
      DBParameterGroupName: !Ref RDSDBInstanceParameterGroup
      DBInstanceClass: db.serverless
      PubliclyAccessible: false
      EnablePerformanceInsights: !Ref EnableRDSPerformanceInsights
      PerformanceInsightsRetentionPeriod:
        !If [
          IsPerformanceInsightsEnabled,
          !Ref EnableRDSPerformanceInsightsDays,
          !Ref AWS::NoValue,
        ]
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'stack'
          Value: !Ref StackName

  RDSInstance2:
    Type: 'AWS::RDS::DBInstance'
    DeletionPolicy: Retain
    Condition: IsNotDevops
    Properties:
      DBClusterIdentifier: !Ref RDSClusterProvisioned
      Engine: aurora-postgresql
      EngineVersion: '15.10'
      DBParameterGroupName: !Ref RDSDBInstanceParameterGroup
      DBInstanceClass: db.serverless
      PubliclyAccessible: false
      EnablePerformanceInsights: !Ref EnableRDSPerformanceInsights
      PerformanceInsightsRetentionPeriod:
        !If [
          IsPerformanceInsightsEnabled,
          !Ref EnableRDSPerformanceInsightsDays,
          !Ref AWS::NoValue,
        ]
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'stack'
          Value: !Ref StackName

  MyDBClusterSecret:
    Type: 'AWS::SecretsManager::Secret'
    DeletionPolicy: Retain
    Condition: IsNotDevops
    Properties:
      Name: !Sub ${Environment}-${UnversionedApiVersion}-${Stack}-DBClusterSecret-Credential
      Description: Aurora DB Cluster Master Password and Credentials
      GenerateSecretString:
        PasswordLength: 16
        ExcludePunctuation: true
        GenerateStringKey: 'password'
        SecretStringTemplate:
          Fn::Sub:
            - |
              {
                "username": "${UserDataAuroraMasterUsername}",
                "engine": "postgresql",
                "host": "${HostValue}",
                "dbname": "postgres",
                "port": 5432
              }
            - UserDataAuroraMasterUsername: !Ref UserDataAuroraMasterUsername
              HostValue: !Sub
                - '${AwsLogicalAccountNameLowerCase}-user-data-rds-migrated-${Environment}.cluster-${RDSClusterIdentifier}.${AWS::Region}.rds.amazonaws.com'
                - RDSClusterIdentifier: !FindInMap
                    - RDSEndpointMapping
                    - !Ref AwsLogicalAccountNameUpperCase
                    - Endpoint
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: 'unversioned'
        - Key: 'stack'
          Value: !Ref StackName

  MyDBClusterSecretRotationSchedule:
    Type: AWS::SecretsManager::RotationSchedule
    Condition: IsNotDevops
    Properties:
      SecretId: !Ref MyDBClusterSecret
      RotationLambdaARN: !GetAtt SecretsManagerRotationFunction.Arn
      RotationRules:
        AutomaticallyAfterDays: !Ref SecretRotationScheduleDays
      # RotateImmediatelyOnCreate: true
    DependsOn:
      - MyDBClusterSecret
      - SecretsManagerRotationFunction
      - LambdaPermission

  SecretsManagerRotationFunction:
    Type: AWS::Serverless::Function
    Condition: IsNotDevops
    Properties:
      FunctionName: !Sub ${AwsLogicalAccountNameUpperCase}-${Stack}-SecretsManagerRotationFunction-${Environment}
      Role: !Ref LambdaRoleArn
      MemorySize: 256
      PackageType: Zip
      Runtime: python3.9
      Handler: lambda_function.lambda_handler
      Timeout: 300
      CodeUri: lambdas/secretrotation
      Policies:
        - AWSSecretsManagerRotationPolicy:
          FuncitonName: !Sub ${AwsLogicalAccountNameUpperCase}-${Stack}-SecretsManagerRotationFunction-${Environment}
      Environment:
        Variables:
          SECRETS_MANAGER_ENDPOINT: !Sub https://secretsmanager.${AWS::Region}.amazonaws.com
          EXCLUDE_PUNCTUATION: 'true'
      VpcConfig:
        SecurityGroupIds: !Ref LambdaSecurityGroup
        SubnetIds: !Ref RDSSubnetGroupIds

  LambdaPermission:
    Type: 'AWS::Lambda::Permission'
    Condition: IsNotDevops
    DependsOn: SecretsManagerRotationFunction
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: !GetAtt SecretsManagerRotationFunction.Arn
      Principal: secretsmanager.amazonaws.com
      SourceAccount: !Sub ${AWS::AccountId}

  RDSClusterProvisioned:
    Type: 'AWS::RDS::DBCluster'
    DeletionPolicy: Retain
    Condition: IsNotDevops
    Properties:
      MasterUsername: !Sub '{{resolve:secretsmanager:${MyDBClusterSecret}:SecretString:username::}}'
      MasterUserPassword: !Sub '{{resolve:secretsmanager:${MyDBClusterSecret}:SecretString:password::}}'
      DBClusterIdentifier: !Sub ${AwsLogicalAccountNameUpperCase}-User-Data-RDS-Migrated-${Environment}
      Engine: aurora-postgresql
      EngineMode: provisioned
      EnableCloudwatchLogsExports:
        - postgresql
      BackupRetentionPeriod:
        Fn::If:
          - UseProdCondition
          - 32 # desired retention period for prod
          - 10 #retention period for non-prod
      EngineVersion: '15.10'
      PreferredBackupWindow:
        Fn::If:
          - IsNotProd
          - '00:30-01:00'
          - !Ref AWS::NoValue
      PreferredMaintenanceWindow:
        Fn::If:
          - UseProdCondition
          - 'Thu:03:00-Thu:03:30' # Maintanence for prod
          - 'Tue:03:00-Tue:03:30' # Maintanence for non-prod
      DeletionProtection: true
      AutoMinorVersionUpgrade: true
      EnableHttpEndpoint: true
      DBSubnetGroupName: !Ref RDSSubnetGroup
      DatabaseName:
        !Join ['', !Split ['-', !Sub 'UserDataAurora${Environment}']]

      Port: 5432
      ServerlessV2ScalingConfiguration:
        MinCapacity: 0.5
        MaxCapacity: 128
      VpcSecurityGroupIds:
        - !Ref RDSSecurityGroup
      AssociatedRoles:
        - RoleArn: !Ref RDSInvokeLambdaRoleArn
          FeatureName: s3Import
      DBClusterParameterGroupName: !Ref RDSDBParameterGroup

      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: 'unversioned'
        - Key: 'stack'
          Value: !Ref StackName
        - !If
          - UseProdCondition
          - Key: backup
            Value: continuous
          - !Ref AWS::NoValue

  AuroraConfigBucket:
    Type: AWS::S3::Bucket
    Properties:
      AccessControl: 'Private'
      BucketName: !Sub ${AwsLogicalAccountNameLowerCase}-userdata-aurora-config-${Environment}
      VersioningConfiguration:
        Status: Enabled
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
      LifecycleConfiguration:
        Rules:
          - Status: Enabled
            ExpirationInDays: 30
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: 'userData'
        - Key: 'apiVersion'
          Value: 'unversioned'
        - Key: 'stack'
          Value: !Ref StackName
    DeletionPolicy: Retain

  AuroraConfigBucketPolicy:
    Type: AWS::S3::BucketPolicy
    DependsOn: AuroraConfigBucket
    Properties:
      Bucket: !Sub '${AwsLogicalAccountNameLowerCase}-userdata-aurora-config-${Environment}'
      PolicyDocument:
        Version: 2012-10-17
        Statement:
          - Sid: AllowSSLRequestsOnly
            Action:
              - 's3:*'
            Effect: Deny
            Resource:
              - !Join
                - ''
                - - 'arn:aws:s3:::'
                  - !Sub '${AwsLogicalAccountNameLowerCase}-userdata-aurora-config-${Environment}'
                  - '/*'
              - !Join
                - ''
                - - 'arn:aws:s3:::'
                  - !Sub '${AwsLogicalAccountNameLowerCase}-userdata-aurora-config-${Environment}'
            Principal: '*'
            Condition:
              Bool:
                aws:SecureTransport: false

  UpdateAuroraUserTableLambda:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${AwsLogicalAccountNameUpperCase}-Update-Aurora-User-Table-${Environment}
      Role: !Ref LambdaRoleArn
      MemorySize: 256
      PackageType: Zip
      Runtime: nodejs18.x
      CodeUri: lambdas/updateAuroraUserTable/dist
      Handler: src/index.handler
      Timeout: 300
      ReservedConcurrentExecutions: 1
      Tracing: Active
      VpcConfig:
        SecurityGroupIds: !Ref LambdaSecurityGroup
        SubnetIds: !Ref ExternalSubnetIds
      Environment:
        Variables:
          USER_DATA_AURORA_BUCKET_NAME: !Sub ${AwsLogicalAccountNameLowerCase}-userdata-aurora-config-${Environment}
          POSTGRES_USER_USERNAME: !Ref UserDataAuroraMasterUsername
          POSTGRES_USER_PASSWORD: !Sub ${Environment}-${UnversionedApiVersion}-${Stack}-DBClusterSecret-Credential
          POSTGRES_USER_HOST: !Sub
            - '${AwsLogicalAccountNameLowerCase}-user-data-rds-migrated-${Environment}.cluster-${RDSClusterIdentifier}.${AWS::Region}.rds.amazonaws.com'
            - RDSClusterIdentifier: !FindInMap
                - RDSEndpointMapping
                - !Ref AwsLogicalAccountNameUpperCase
                - Endpoint
          POSTGRES_USER_TABLE:
            !Join ['', !Split ['-', !Sub 'UserDataAurora${Environment}']]
          NOTIFICATION_PRODUCER_LAMBDA_ARN: !Ref NotificationProducerLambdaARN
          DE_SUBS_TARIFF_BASED_DISCOUNT: !Ref DESubsTariffBasedDiscount
          BE_ENERGISED_TEST_ENVIRONMENT: !Ref BeEnergisedTestEnvironment
          NODE_ENV: !Ref NodeEnv
      Tags:
        environment: !Ref Environment
        service: !Ref Service
        stack: !Ref StackName

  UpdateUserDeletionStatus:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${AwsLogicalAccountNameUpperCase}-Update-User-Deletion-Status-${Environment}
      Role: !Ref LambdaRoleArn
      MemorySize: 256
      PackageType: Zip
      Runtime: nodejs18.x
      Handler: index.handler
      Timeout: 600
      Tracing: Active
      VpcConfig:
        SecurityGroupIds: !Ref LambdaSecurityGroup
        SubnetIds: !Ref ExternalSubnetIds
      CodeUri: lambdas/updateUserDeletionStatus
      Events:
        ScheduleEvent:
          Type: Schedule
          Properties:
            Description: 'Lambda Trigger'
            Enabled: True
            Name: !Sub ${AwsLogicalAccountNameUpperCase}-Update-User-Deletion-Status-Trigger-${Environment}
            # End of week
            Schedule: cron(59 23 ? * 1 *)
      Environment:
        Variables:
          POSTGRES_USER_USERNAME: !Ref UserDataAuroraMasterUsername
          POSTGRES_USER_PASSWORD: !Sub ${Environment}-${UnversionedApiVersion}-${Stack}-DBClusterSecret-Credential
          POSTGRES_USER_HOST: !Sub
            - '${AwsLogicalAccountNameLowerCase}-user-data-rds-migrated-${Environment}.cluster-${RDSClusterIdentifier}.${AWS::Region}.rds.amazonaws.com'
            - RDSClusterIdentifier: !FindInMap
                - RDSEndpointMapping
                - !Ref AwsLogicalAccountNameUpperCase
                - Endpoint
          POSTGRES_USER_TABLE:
            !Join ['', !Split ['-', !Sub 'UserDataAurora${Environment}']]

          TOKEN_ENDPOINT: !Ref TokenEndpoint
          TOKEN_API_KEY: !Sub '{{resolve:secretsmanager:${Environment}-${UnversionedApiVersion}-${Stack}-${TokenApiKey}:SecretString:secret}}'
          IDP_BASE_URL: !Ref IdpBaseUrl
      Tags:
        environment: !Ref Environment
        service: !Ref Service
        stack: !Ref StackName

  RollbackAuroraDatabaseLambda:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${AwsLogicalAccountNameUpperCase}-Rollback-Aurora-Database-${Environment}
      Role: !Ref LambdaRoleArn
      MemorySize: 256
      PackageType: Zip
      Runtime: nodejs18.x
      Handler: rollbackAuroraDatabase/index.handler
      AutoPublishAlias: live
      Timeout: 300
      ReservedConcurrentExecutions: 1
      Tracing: Active
      VpcConfig:
        SecurityGroupIds: !Ref LambdaSecurityGroup
        SubnetIds: !Ref ExternalSubnetIds
      CodeUri: lambdas/rollbackAuroraDatabase/dist
      Environment:
        Variables:
          USER_DATA_AURORA_BUCKET_NAME: !Sub ${AwsLogicalAccountNameLowerCase}-userdata-aurora-config-${Environment}
          POSTGRES_USER_USERNAME: !Ref UserDataAuroraMasterUsername
          POSTGRES_USER_PASSWORD: !Sub ${Environment}-${UnversionedApiVersion}-${Stack}-DBClusterSecret-Credential
          POSTGRES_USER_HOST: !Sub
            - '${AwsLogicalAccountNameLowerCase}-user-data-rds-migrated-${Environment}.cluster-${RDSClusterIdentifier}.${AWS::Region}.rds.amazonaws.com'
            - RDSClusterIdentifier: !FindInMap
                - RDSEndpointMapping
                - !Ref AwsLogicalAccountNameUpperCase
                - Endpoint
          BE_ENERGISED_TEST_ENVIRONMENT: !Ref BeEnergisedTestEnvironment
          POSTGRES_USER_TABLE:
            !Join ['', !Split ['-', !Sub 'UserDataAurora${Environment}']]
          NOTIFICATION_PRODUCER_LAMBDA_ARN: !Ref NotificationProducerLambdaARN
          NODE_ENV: !Ref NodeEnv
      Tags:
        environment: !Ref Environment
        service: !Ref Service
        stack: !Ref StackName

Outputs:
  AuroraConfigS3Name:
    Value: !Ref 'AuroraConfigBucket'
