AWSTemplateFormatVersion: 2010-09-09

Description: >
  This template deploys the contract tests vm required.

Parameters:
  Environment:
    Type: String
  AwsLogicalAccountNameUpperCase:
    Type: String
  BastionSubnetId:
    Description: Choose the subnet (public) in which to host your bastion instance.
    Type: AWS::EC2::Subnet::Id
  VpcId:
    Type: String
  VpcCidr:
    Type: String
  VPCSecurityGroupId:
    Type: String
  ImageId:
    Type: 'AWS::SSM::Parameter::Value<AWS::EC2::Image::Id>'
    Default: '/aws/service/ami-amazon-linux-latest/al2023-ami-kernel-6.1-x86_64'

Resources:
  BastionInstance:
    Type: AWS::EC2::Instance
    Properties:
      ImageId: !Ref ImageId
      InstanceType: t3.medium
      SecurityGroupIds:
        - !Ref VPCSecurityGroupId
      SubnetId: !Ref BastionSubnetId
      IamInstanceProfile: !Sub ${AwsLogicalAccountNameUpperCase}-role_INSTANCE
      UserData:
        Fn::Base64: !Sub |
          #!/bin/bash
          yes | sudo yum update -y
          yes | sudo wget https://www.stunnel.org/downloads/stunnel-5.72.tar.gz
          yes | sudo tar -zxvf stunnel-5.72.tar.gz
          cd stunnel-5.72
          yes | sudo yum install gcc
          yes | sudo yum install openssl openssl-devel gcc
          yes | sudo ./configure
          yes | sudo make
          yes | sudo make install
          cd /usr/bin/
          sudo ln -s /usr/local/bin/stunnel stunnel
          cd /etc/
          sudo ln -s /usr/local/etc/stunnel stunnel

          yes | sudo yum install lsb-release curl gpg

          yes | sudo amazon-linux-extras install redis6

          yes | sudo yum install icu libicu libicu-devel openssl openssl-libs zlib zlib-devel

          sleep 600

          sudo mkdir -p /opt/aws/amazon-cloudwatch-agent/etc

          # Install PowerShell (pwsh) using wget
          yes | sudo wget https://github.com/PowerShell/PowerShell/releases/download/v7.4.5/powershell-7.4.5-linux-x64.tar.gz
          yes | sudo mkdir -p /opt/microsoft/powershell/7
          yes | sudo tar -xvf powershell-7.4.5-linux-x64.tar.gz -C /opt/microsoft/powershell/7
          sudo ln -s /opt/microsoft/powershell/7/pwsh /usr/bin/pwsh
          sudo chmod +x /opt/microsoft/powershell/7/pwsh

          # Install kubectl
          cd /home/<USER>/
          curl -LO https://dl.k8s.io/release/v1.31.0/bin/linux/amd64/kubectl
          chmod +x ./kubectl
          sudo mv ./kubectl /usr/local/bin/
          kubectl version


          # Install go first
          sudo yum install -y golang

          # Then install yq via go
          go install github.com/mikefarah/yq/v4@latest
          sudo cp ~/go/bin/yq /usr/local/bin/

      Tags:
        - Key: 'owner'
          Value: 'DevOps'
        - Key: 'Snooze-Schedule'
          Value: 'Auto-Scaling'
        - Key: 'Name'
          Value: 'ContractTests'
        - Key: 'Environment'
          Value: !Ref Environment
