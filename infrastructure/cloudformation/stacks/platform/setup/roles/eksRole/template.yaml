AWSTemplateFormatVersion: 2010-09-09

Description: >
  This template deploys the service role assumed by EKS clusters

Parameters:
  AwsLogicalAccountNameUpperCase:
    Type: String
    Description: Choose account
    AllowedValues:
      - WS-00F0
      - WS-00CS
      - WS-01PL
      - WS-02P1
      - WS-02HD
  AwsLogicalAccountNameLowerCase:
    Type: String
    Description: Choose account in lowercase
    AllowedValues:
      - ws-00f0
      - ws-00cs
      - ws-01pl
      - ws-02p1
      - ws-02hd
  Environment:
    Type: String
    Description: Choose environment
    AllowedValues:
      - pr
      - dev
      - test
      - preprod
      - performance
      - prod
      - us-prod
      - us-uat
      - devops
  EnvironmentCapitalized:
    Type: String
    Description: Choose environment
    AllowedValues:
      - Pr
      - Dev
      - Test
      - Preprod
      - Performance
      - Prod
      - US-Prod
      - Devops
      - Us-Uat
  OIDC:
    Type: String
    Description: OIDC provider hash

Conditions:
  OneResourcePerAccount:
    Fn::Or:
      - Fn::Equals: [!Ref Environment, pr]
      - Fn::Equals: [!Ref Environment, prod]
      - Fn::Equals: [!Ref Environment, preprod]
      - Fn::Equals: [!Ref Environment, us-uat]
      - Fn::Equals: [!Ref Environment, us-prod]
  IsEKSIaCEnv:
    Fn::Or:
      - Fn::Equals: [!Ref Environment, devops]
      - Fn::Equals: [!Ref Environment, performance]
      - Fn::Equals: [!Ref Environment, preprod]
      - Fn::Equals: [!Ref Environment, us-uat]
      - Fn::Equals: [!Ref Environment, us-prod]

Resources:
  # Policies
  DynamoDBAccessPolicy:
    Type: AWS::IAM::ManagedPolicy
    Condition: OneResourcePerAccount
    Properties:
      ManagedPolicyName: DynamoDB-Access-Policy
      Description: Grants dynamodb permissions to EKS roles.
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Resource: '*'
            Action:
              - dynamodb:BatchGetItem
              - dynamodb:BatchWriteItem
              - dynamodb:ConditionCheckItem
              - dynamodb:CreateTableReplica
              - dynamodb:DeleteItem
              - dynamodb:DeleteResourcePolicy
              - dynamodb:DeleteTableReplica
              - dynamodb:DescribeReservedCapacity
              - dynamodb:DescribeReservedCapacityOfferings
              - dynamodb:GetAbacStatus
              - dynamodb:GetItem
              - dynamodb:GetRecords
              - dynamodb:GetShardIterator
              - dynamodb:ListTagsOfResource
              - dynamodb:PartiQLDelete
              - dynamodb:PartiQLInsert
              - dynamodb:PartiQLSelect
              - dynamodb:PartiQLUpdate
              - dynamodb:PurchaseReservedCapacityOfferings
              - dynamodb:PutItem
              - dynamodb:PutResourcePolicy
              - dynamodb:Query
              - dynamodb:RestoreTableFromAwsBackup
              - dynamodb:Scan
              - dynamodb:StartAwsBackupJob
              - dynamodb:TagResource
              - dynamodb:UntagResource
              - dynamodb:UpdateAbacStatus
              - dynamodb:UpdateGlobalTableVersion
              - dynamodb:UpdateItem
          - Effect: 'Allow'
            Action: 'iam:PassRole'
            Resource: '*'
            Condition:
              StringLike:
                'iam:PassedToService':
                  - 'application-autoscaling.amazonaws.com'
                  - 'application-autoscaling.amazonaws.com.cn'
                  - 'dax.amazonaws.com'

  SSMAgentPolicy:
    Type: AWS::IAM::ManagedPolicy
    Condition: IsEKSIaCEnv
    Properties:
      ManagedPolicyName: !Sub ${AwsLogicalAccountNameUpperCase}-SSMAgentPermissions-${Environment}
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Action:
              - ssm:GetDeployablePatchSnapshotForInstance
              - ssm:GetManifest
              - ssm:ListInstanceAssociations
              - ssm:PutConfigurePackageResult
              - ssm:PutInventory
              - ssm:UpdateInstanceAssociationStatus
              - ssm:UpdateInstanceInformation
            Resource: '*'
          - Effect: Allow
            Action:
              - ssmmessages:*
            Resource: '*'
          - Effect: Allow
            Action:
              - ec2messages:*
            Resource: '*'

  EKSRolePermissionsPolicy:
    Type: AWS::IAM::ManagedPolicy
    Condition: IsEKSIaCEnv
    Properties:
      ManagedPolicyName: !Sub ${AwsLogicalAccountNameUpperCase}-EKSRolePermissionsPolicy-${Environment}
      Description: Managed Policy allowing various permissions for EKS roles
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Action:
              - ec2:DescribeSecurityGroups
              - ec2:DescribeSubnets
              - ec2:DescribeVpcs
              - kms:DescribeKey
              - ec2:DescribeInstanceTypes
              - ec2:DescribeInstances
              - ec2:DescribeVolumes
              - ec2:DescribeNetworkInterfaces
              - ec2:DescribeTags
              - autoscaling:DescribeAutoScalingGroups
              - ec2:DescribeAccountAttributes
              - ec2:DescribeAvailabilityZones
              - ec2:DescribeInstanceTopology
              - ec2:DescribeInternetGateways
              - elasticloadbalancing:DescribeListeners
              - elasticloadbalancing:DescribeLoadBalancerAttributes
              - elasticloadbalancing:DescribeLoadBalancers
              - elasticloadbalancing:DescribeTargetGroupAttributes
              - elasticloadbalancing:DescribeTargetGroups
              - elasticloadbalancing:DescribeTargetHealth
            Resource: '*'
          - Effect: Allow
            Action:
              - iam:PassRole
            Resource: '*'
            Condition:
              StringLike:
                iam:PassedToService:
                  - application-autoscaling.amazonaws.com
                  - application-autoscaling.amazonaws.com.cn
                  - dax.amazonaws.com
          - Effect: Allow
            Action:
              - ec2:AssignPrivateIpAddresses
              - ec2:AttachNetworkInterface
              - ec2:CreateNetworkInterface
              - ec2:DeleteNetworkInterface
              - ec2:DetachNetworkInterface
              - ec2:ModifyNetworkInterfaceAttribute
            Resource: '*'
          - Effect: Allow
            Action:
              - ec2:CreateTags
            Resource:
              - arn:aws:ec2:*:*:network-interface/*
          - Effect: Allow
            Action:
              - ec2:CreateTags
              - elasticloadbalancing:AddTags
              - elasticloadbalancing:CreateListener
              - elasticloadbalancing:CreateLoadBalancer
              - elasticloadbalancing:CreateTargetGroup
              - elasticloadbalancing:DeleteLoadBalancer
              - elasticloadbalancing:DeleteTargetGroup
              - elasticloadbalancing:DeregisterTargets
              - elasticloadbalancing:RegisterTargets
            Resource: '*'
          - Effect: Allow
            Action:
              - ec2:DeleteNetworkInterface
            Condition:
              StringEquals:
                'ec2:ResourceTag/eks:eni:owner': 'amazon-vpc-cni'
            Resource: '*'
          - Effect: Allow
            Action:
              - ecr:BatchGetImage
              - ecr:GetAuthorizationToken
              - ecr:GetDownloadUrlForLayer
              - ecr:ListTagsForResource
            Resource: '*'

  EKSOpenTelemetryPolicy:
    Type: AWS::IAM::ManagedPolicy
    Condition: IsEKSIaCEnv
    Properties:
      ManagedPolicyName: !Sub ${AwsLogicalAccountNameUpperCase}-SpecificEKSAndRelatedServicesPolicy-${Environment}
      Description: Policy providing detailed permissions for OpenTelemetry
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Action:
              - ec2:CreateTags
              - ec2:DeleteTags
              - logs:CreateLogStream
              - logs:DescribeLogStreams
            Resource:
              - arn:aws:cloudwatch:*:*:insight-rule/DynamoDBContributorInsights*
              - arn:aws:ec2:*:*:subnet/*
              - arn:aws:ec2:*:*:vpc/*
              - arn:aws:logs:*:*:log-group:/aws/eks/*:*
            Sid: ManageEC2AndLogs

          - Effect: Allow
            Action:
              - ec2:CreateTags
              - logs:PutLogEvents
            Resource:
              - arn:aws:ec2:*:*:network-interface/*
              - arn:aws:logs:*:*:log-group:/aws/eks/*:*:*
            Sid: ManageNetworkInterfacesAndLogs

          - Effect: Allow
            Action:
              - autoscaling:DescribeAutoScalingGroups
              - autoscaling:DescribeTags
              - autoscaling:SetDesiredCapacity
              - autoscaling:TerminateInstanceInAutoScalingGroup
              - ec2:AssignPrivateIpAddresses
              - ec2:AttachNetworkInterface
              - ec2:CreateNetworkInterface
              - ec2:DeleteNetworkInterface
              - ec2:DescribeInstanceStatus
              - ec2:DescribeInstanceTypes
              - ec2:DescribeInstances
              - ec2:DescribeLaunchTemplateVersions
              - ec2:DescribeNetworkInterfaces
              - ec2:DescribeSecurityGroups
              - ec2:DescribeSubnets
              - ec2:DescribeTags
              - ec2:DescribeVpcs
              - ec2:DetachNetworkInterface
              - ec2:ModifyNetworkInterfaceAttribute
              - ec2messages:*
              - ecr:BatchGetImage
              - ecr:GetAuthorizationToken
              - ecr:GetDownloadUrlForLayer
              - ecr:ListTagsForResource
              - kms:DescribeKey
              - logs:CreateLogGroup
              - logs:CreateLogStream
              - logs:DescribeLogStreams
              - logs:PutLogEvents
              - s3:AbortMultipartUpload
              - s3:GetEncryptionConfiguration
              - s3:GetObject
              - s3:ListBucket
              - s3:ListMultipartUploadParts
              - s3:PutObject
              - ssm:GetDeployablePatchSnapshotForInstance
              - ssm:GetManifest
              - ssm:ListInstanceAssociations
              - ssm:PutConfigurePackageResult
              - ssm:PutInventory
              - ssm:UpdateInstanceAssociationStatus
              - ssm:UpdateInstanceInformation
              - ssmmessages:*
            Resource: '*'
            Sid: FullEC2DynamoDBAndLogsAccess
  # Roles
  EKSRole:
    Type: AWS::IAM::Role
    Condition: IsEKSIaCEnv
    Properties:
      RoleName: !Sub ${AwsLogicalAccountNameUpperCase}_EKS_${Environment}
      AssumeRolePolicyDocument:
        Fn::Sub:
          - |
            {
              "Version": "2012-10-17",
              "Statement": [
                {
                  "Effect": "Allow",
                  "Principal": {
                    "Service": ["eks.amazonaws.com", "ec2.amazonaws.com", "cloudformation.amazonaws.com"]
                  },
                  "Action": "sts:AssumeRole"
                },
                {
                  "Effect": "Allow",
                  "Principal": {
                    "Federated": "arn:aws:iam::${AWS::AccountId}:oidc-provider/oidc.eks.${AWS::Region}.amazonaws.com/id/${OIDC}"
                  },
                  "Action": "sts:AssumeRoleWithWebIdentity",
                  "Condition": {
                    "StringEquals": {
                      "oidc.eks.${AWS::Region}.amazonaws.com/id/${OIDC}:sub":
                        [
                          "system:serviceaccount:kube-system:cluster-autoscaler-aws-cluster-autoscaler",
                          "system:serviceaccount:kube-system:csi-secrets-store-provider-aws",
                          "system:serviceaccount:kube-system:ebs-csi-controller-sa",
                          "system:serviceaccount:kube-system:aws-load-balancer-controller"
                        ],
                      "oidc.eks.${AWS::Region}.amazonaws.com/id/${OIDC}:aud":
                        [
                          "sts.amazonaws.com"
                        ]
                    }
                  }
                }
              ]
            }
          - OIDC: !Sub ${OIDC}
      ManagedPolicyArns:
        - !Sub arn:aws:iam::${AWS::AccountId}:policy/${AWS::AccountId}-pol_DsINSTANCE-Policy
        - !Ref SSMAgentPolicy
        - !Ref EKSRolePermissionsPolicy
        - !Ref EKSOpenTelemetryPolicy

      PermissionsBoundary: !Sub arn:aws:iam::${AWS::AccountId}:policy/${AwsLogicalAccountNameUpperCase}-pol_PlatformUserBoundary
      Policies:
        - PolicyName: CloudWatchAgentServerPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Sid: CloudWatchAgentServerPolicy
                Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:DescribeLogStreams
                  - ec2:DescribeTags
                  - ec2:DescribeVolumes
                  - logs:CreateLogGroup
                  - logs:PutLogEvents
                Resource: '*'
              - Sid: CloudWatchAgentServerPolicy2
                Effect: Allow
                Action: ssm:GetParameter
                Resource: arn:aws:ssm:*:*:parameter/AmazonCloudWatch-*
        - PolicyName: EKSClusterAutoscaler
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Sid: EKSClusterAutoscaler
                Effect: Allow
                Action:
                  - autoscaling:DescribeAutoScalingGroups
                  - autoscaling:DescribeTags
                  - autoscaling:SetDesiredCapacity
                  - autoscaling:TerminateInstanceInAutoScalingGroup
                  - ec2:DescribeLaunchTemplateVersions
                Resource: '*'
        - PolicyName: XRayPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Sid: XRay
                Effect: Allow
                Action:
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:DescribeLogStreams
                Resource: '*'
        - PolicyName: OpenTelemetryPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Sid: ManageEC2AndLogs
                Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - ec2:DeleteTags
                  - ec2:CreateTags
                  - logs:DescribeLogStreams
                Resource:
                  - 'arn:aws:cloudwatch:*:*:insight-rule/DynamoDBContributorInsights*'
                  - 'arn:aws:ec2:*:*:subnet/*'
                  - 'arn:aws:ec2:*:*:vpc/*'
                  - 'arn:aws:logs:*:*:log-group:/aws/eks/*:*'
              - Sid: ManageNetworkInterfacesAndLogs
                Effect: Allow
                Action:
                  - ec2:CreateTags
                  - logs:PutLogEvents
                Resource:
                  - 'arn:aws:ec2:*:*:network-interface/*'
                  - 'arn:aws:logs:*:*:log-group:/aws/eks/*:*:*'
              - Sid: FullEC2DynamoDBAndLogsAccess
                Effect: Allow
                Action:
                  - ec2:DescribeInstances
                  - ssm:ListInstanceAssociations
                  - autoscaling:TerminateInstanceInAutoScalingGroup
                  - ec2:ModifyNetworkInterfaceAttribute
                  - ecr:GetAuthorizationToken
                  - ec2messages:AcknowledgeMessage
                  - ec2:AssignPrivateIpAddresses
                  - ec2:CreateNetworkInterface
                  - s3:PutObject
                  - ec2messages:SendReply
                  - ec2:DescribeSubnets
                  - ec2messages:GetEndpoint
                  - ecr:ListTagsForResource
                  - ssmmessages:OpenControlChannel
                  - s3:ListBucket
                  - ssm:PutConfigurePackageResult
                  - ec2messages:DeleteMessage
                  - s3:AbortMultipartUpload
                  - ssmmessages:OpenDataChannel
                  - ssm:GetDocument
                  - ec2:DescribeInstanceStatus
                  - ssmmessages:CreateControlChannel
                  - ec2:DescribeSecurityGroups
                  - ssmmessages:CreateDataChannel
                  - ec2:DescribeVpcs
                  - ssm:UpdateInstanceAssociationStatus
                  - dynamodb:Batch*
                  - dynamodb:ConditionCheckItem
                  - dynamodb:CreateTableReplica
                  - dynamodb:DeleteItem
                  - dynamodb:DeleteResourcePolicy
                  - dynamodb:DeleteTableReplica
                  - dynamodb:DescribeReserved*
                  - dynamodb:GetItem
                  - dynamodb:GetRecords
                  - dynamodb:GetShardIterator
                  - dynamodb:ListTagsOfResource
                  - dynamodb:Parti*
                  - dynamodb:PurchaseReservedCapacityOfferings
                  - dynamodb:Put*
                  - dynamodb:Query
                  - dynamodb:RestoreTableFromAwsBackup
                  - dynamodb:Scan
                  - dynamodb:StartAwsBackupJob
                  - dynamodb:TagResource
                  - dynamodb:UntagResource
                  - dynamodb:UpdateGlobalTableVersion
                  - dynamodb:UpdateItem
                  - logs:CreateLogStream
                  - ssm:UpdateInstanceInformation
                  - autoscaling:DescribeAutoScalingGroups
                  - ssm:PutComplianceItems
                  - autoscaling:SetDesiredCapacity
                  - ecr:GetDownloadUrlForLayer
                  - autoscaling:DescribeTags
                  - ec2:DeleteNetworkInterface
                  - logs:CreateLogGroup
                  - s3:ListMultipartUploadParts
                  - s3:GetObject
                  - ecr:BatchGetImage
                  - ec2:DescribeInstanceTypes
                  - logs:DescribeLogStreams
                  - ec2messages:GetMessages
                  - ssm:GetManifest
                  - s3:GetEncryptionConfiguration
                  - ec2:DescribeNetworkInterfaces
                  - ec2messages:FailMessage
                  - kms:DescribeKey
                  - ec2:DetachNetworkInterface
                  - ssm:GetDeployablePatchSnapshotForInstance
                  - ec2:DescribeTags
                  - ec2:DescribeLaunchTemplateVersions
                  - logs:PutLogEvents
                  - ssm:PutInventory
                  - ec2:AttachNetworkInterface
                Resource: '*'
        - PolicyName: DsInstanceBinariesSecretsPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Sid: AllowGetObjectBinariesSecrets
                Effect: Allow
                Action: s3:GetObject
                Resource: arn:aws:s3:::dsr-infosec-prod-central-security-services-binary/*
              - Sid: AllowSSMOutputPolDsInstanceBinariesSecrets
                Effect: Allow
                Action:
                  - s3:PutObject
                  - s3:GetObject
                  - s3:GetEncryptionConfiguration
                  - s3:AbortMultipartUpload
                  - s3:ListMultipartUploadParts
                  - s3:ListBucket
                Resource:
                  - arn:aws:s3:::dsr-infosec-prod-central-sec-outputs-*
                  - arn:aws:s3:::dsr-infosec-prod-central-sec-outputs-*/SSMRun/*
              - Sid: AllowGetSecretPolDsInstanceBinariesSecrets
                Effect: Allow
                Action: secretsmanager:GetSecretValue
                Resource: arn:aws:secretsmanager:*:************:secret:DsHostsSharedSecret/*
              - Sid: AllowKMSDecryptPolDsInstanceBinariesSecrets
                Effect: Allow
                Action:
                  - kms:Decrypt
                  - kms:DescribeKey
                Resource:
                  - arn:aws:kms:eu-west-1:************:key/f31109cd-1bd5-4989-8b25-5a4411f70c46
                  - arn:aws:kms:us-east-2:************:key/c6943400-c33b-4afe-b93c-6c8ed3cd57e3
        - PolicyName: SecretsManagerAccessPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Sid: PolicySecretsManagerAccess
                Effect: Allow
                Action: secretsmanager:GetSecretValue
                Resource: '*'

  EKSSecretManagerRole:
    Type: AWS::IAM::Role
    DeletionPolicy: Retain
    Properties:
      RoleName: !Sub ${AwsLogicalAccountNameUpperCase}-role_EKS_SecretsManager_${EnvironmentCapitalized}
      AssumeRolePolicyDocument:
        Fn::Sub:
          - |
            {
              "Version": "2012-10-17",
              "Statement": [
                {
                  "Effect": "Allow",
                  "Principal": {
                    "Service": ["eks.amazonaws.com", "ec2.amazonaws.com", "cloudformation.amazonaws.com", "es.amazonaws.com"]
                  },
                  "Action": "sts:AssumeRole"
                },
                {
                  "Effect": "Allow",
                  "Principal": {
                    "Federated": "arn:aws:iam::${AWS::AccountId}:oidc-provider/oidc.eks.${AWS::Region}.amazonaws.com/id/${OIDC}"
                  },
                  "Action": "sts:AssumeRoleWithWebIdentity",
                  "Condition": {
                    "StringLike": {
                      "oidc.eks.${AWS::Region}.amazonaws.com/id/${OIDC}:sub":
                        [
                          "system:serviceaccount:*"
                        ]
                    }
                  }
                }
              ]
            }
          - OIDC: !Sub ${OIDC}
      ManagedPolicyArns:
        - !Sub arn:aws:iam::${AWS::AccountId}:policy/${AWS::AccountId}-pol_DsINSTANCE-Policy
        - !Sub arn:aws:iam::${AWS::AccountId}:policy/DynamoDB-Access-Policy
      PermissionsBoundary: !Sub arn:aws:iam::${AWS::AccountId}:policy/${AwsLogicalAccountNameUpperCase}-pol_PlatformUserBoundary
      Policies:
        - PolicyName: !Sub ${AwsLogicalAccountNameUpperCase}-CommonEksSecretsManagerPolicy
          PolicyDocument:
            Version: 2008-10-17
            Statement:
              - Effect: Allow
                Action:
                  - 's3:AbortMultipartUpload'
                  - 's3:GetObject'
                  - 's3:ListBucket'
                  - 's3:ListMultipartUploadParts'
                  - 's3:PutObject'
                  - 'secretsmanager:GetSecretValue'
                  - 'secretsmanager:DescribeSecret'
                  - 'lambda:DisableReplication'
                  - 'lambda:EnableReplication'
                  - 'lambda:Invoke*'
                  - 'lambda:ListTags'
                  - 'lambda:TagResource'
                  - 'lambda:UntagResource'
                  - 'lambda:UpdateFunctionCodeSigningConfig'
                Resource: '*'
              - Effect: Allow
                Action: 'ses:SendTemplatedEmail'
                Resource: '*'
        - PolicyName: !Sub ${AwsLogicalAccountNameUpperCase}-pol_DsINSTANCE-BINARIES-SECRETS
          PolicyDocument:
            Version: 2012-10-17
            Statement:
              - Sid: AllowGetObject
                Effect: Allow
                Action: 's3:GetObject'
                Resource: 'arn:aws:s3:::dsr-infosec-prod-central-security-services-binary/*'
              - Sid: AllowSSMOutput
                Effect: Allow
                Action:
                  - 's3:PutObject'
                  - 's3:GetObject'
                  - 's3:AbortMultipartUpload'
                  - 's3:ListMultipartUploadParts'
                  - 's3:ListBucket'
                Resource:
                  - 'arn:aws:s3:::dsr-infosec-prod-central-sec-outputs-*'
                  - 'arn:aws:s3:::dsr-infosec-prod-central-sec-outputs-*/SSMRun/*'
              - Sid: AllowGetSecret
                Effect: Allow
                Action: 'secretsmanager:GetSecretValue'
                Resource: 'arn:aws:secretsmanager:*:************:secret:DsHostsSharedSecret/*'
              - Sid: AllowKMSDecrypt
                Effect: Allow
                Action: 'kms:Decrypt'
                Resource:
                  - 'arn:aws:kms:eu-west-1:************:key/f31109cd-1bd5-4989-8b25-5a4411f70c46'
                  - 'arn:aws:kms:us-east-2:************:key/c6943400-c33b-4afe-b93c-6c8ed3cd57e3'

  EKSSecretManagerOCPIRole:
    Type: AWS::IAM::Role
    DeletionPolicy: Retain
    Properties:
      RoleName: !Sub ${AwsLogicalAccountNameUpperCase}-role_EKS_SecretsManager_OCPI_${EnvironmentCapitalized}
      AssumeRolePolicyDocument:
        Fn::Sub:
          - |
            {
              "Version": "2012-10-17",
              "Statement": [
                {
                  "Effect": "Allow",
                  "Principal": {
                    "Service": ["eks.amazonaws.com", "ec2.amazonaws.com", "cloudformation.amazonaws.com", "es.amazonaws.com"]
                  },
                  "Action": "sts:AssumeRole"
                },
                {
                  "Effect": "Allow",
                  "Principal": {
                    "Federated": "arn:aws:iam::${AWS::AccountId}:oidc-provider/oidc.eks.${AWS::Region}.amazonaws.com/id/${OIDC}"
                  },
                  "Action": "sts:AssumeRoleWithWebIdentity",
                  "Condition": {
                    "StringLike": {
                      "oidc.eks.${AWS::Region}.amazonaws.com/id/${OIDC}:sub":
                        [
                          "system:serviceaccount:*"
                        ]
                    }
                  }
                }
              ]
            }
          - OIDC: !Sub ${OIDC}
      ManagedPolicyArns:
        - !Sub arn:aws:iam::${AWS::AccountId}:policy/${AWS::AccountId}-pol_DsINSTANCE-Policy
      PermissionsBoundary: !Sub arn:aws:iam::${AWS::AccountId}:policy/${AwsLogicalAccountNameUpperCase}-pol_PlatformUserBoundary
      Policies:
        - PolicyName: !Sub ${AwsLogicalAccountNameUpperCase}-CommonEksSecretsManagerPolicyOCPI
          PolicyDocument:
            Statement:
              - Action:
                  - cloudwatch:GetMetricData
                  - cloudwatch:ListMetrics
                  - cloudwatch:PutMetricData
                Effect: Allow
                Resource: '*'
              - Action:
                  - iam:PassRole
                Condition:
                  StringLike:
                    iam:PassedToService:
                      - application-autoscaling.amazonaws.com
                      - application-autoscaling.amazonaws.com.cn
                      - dax.amazonaws.com
                Effect: Allow
                Resource: '*'

  AWSLoadBalancerControllerPolicy:
    Type: AWS::IAM::Policy
    Condition: IsEKSIaCEnv
    Properties:
      PolicyName: !Sub ${AwsLogicalAccountNameUpperCase}-AWSLoadBalancerControllerPolicy-${EnvironmentCapitalized}
      PolicyDocument:
        Version: 2012-10-17
        Statement:
          - Effect: Allow
            Action:
              - ec2:DescribeAccountAttributes
              - ec2:DescribeAvailabilityZones
              - ec2:DescribeInstances
              - ec2:DescribeInternetGateways
              - ec2:DescribeNetworkInterfaces
              - ec2:DescribeSecurityGroups
              - ec2:DescribeSubnets
              - ec2:DescribeTags
              - ec2:DescribeVpcs
              - elasticloadbalancing:DescribeListenerAttributes
              - elasticloadbalancing:DescribeListenerCertificates
              - elasticloadbalancing:DescribeListeners
              - elasticloadbalancing:DescribeLoadBalancerAttributes
              - elasticloadbalancing:DescribeLoadBalancers
              - elasticloadbalancing:DescribeRules
              - elasticloadbalancing:DescribeTags
              - elasticloadbalancing:DescribeTargetGroupAttributes
              - elasticloadbalancing:DescribeTargetGroups
              - elasticloadbalancing:DescribeTargetHealth
            Resource: '*'
          - Effect: Allow
            Action:
              - cognito-idp:DescribeUserPoolClient
              - shield:GetSubscriptionState
            Resource: '*'
          - Effect: Allow
            Action:
              - ec2:CreateTags
            Condition:
              'Null':
                'aws:RequestTag/elbv2.k8s.aws/cluster': 'false'
              StringEquals:
                'ec2:CreateAction': 'CreateSecurityGroup'
            Resource: 'arn:aws:ec2:*:*:security-group/*'
          - Effect: Allow
            Action:
              - ec2:CreateTags
              - ec2:DeleteTags
            Condition:
              'Null':
                'aws:RequestTag/elbv2.k8s.aws/cluster': 'true'
                'aws:ResourceTag/elbv2.k8s.aws/cluster': 'false'
            Resource: 'arn:aws:ec2:*:*:security-group/*'
          - Effect: Allow
            Action:
              - elasticloadbalancing:CreateLoadBalancer
              - elasticloadbalancing:CreateTargetGroup
            Condition:
              'Null':
                'aws:RequestTag/elbv2.k8s.aws/cluster': 'false'
            Resource: '*'
          - Effect: Allow
            Action:
              - elasticloadbalancing:CreateListener
              - elasticloadbalancing:CreateRule
            Resource: '*'
          - Effect: Allow
            Action:
              - elasticloadbalancing:AddTags
              - elasticloadbalancing:RemoveTags
            Condition:
              'Null':
                'aws:RequestTag/elbv2.k8s.aws/cluster': 'true'
                'aws:ResourceTag/elbv2.k8s.aws/cluster': 'false'
            Resource:
              - 'arn:aws:elasticloadbalancing:*:*:targetgroup/*/*'
              - 'arn:aws:elasticloadbalancing:*:*:loadbalancer/net/*/*'
              - 'arn:aws:elasticloadbalancing:*:*:loadbalancer/app/*/*'
          - Effect: Allow
            Action:
              - elasticloadbalancing:AddTags
              - elasticloadbalancing:RemoveTags
            Resource:
              - 'arn:aws:elasticloadbalancing:*:*:listener/net/*/*/*'
              - 'arn:aws:elasticloadbalancing:*:*:listener/app/*/*/*'
              - 'arn:aws:elasticloadbalancing:*:*:listener-rule/net/*/*/*'
              - 'arn:aws:elasticloadbalancing:*:*:listener-rule/app/*/*/*'
          - Effect: Allow
            Action:
              - elasticloadbalancing:DeleteLoadBalancer
              - elasticloadbalancing:DeleteTargetGroup
            Condition:
              'Null':
                'aws:ResourceTag/elbv2.k8s.aws/cluster': 'false'
            Resource: '*'
          - Effect: Allow
            Action:
              - elasticloadbalancing:DeregisterTargets
              - elasticloadbalancing:RegisterTargets
            Resource: 'arn:aws:elasticloadbalancing:*:*:targetgroup/*/*'
          - Effect: Allow
            Action:
              - elasticloadbalancing:ModifyRule
              - elasticloadbalancing:SetWebAcl
            Resource: '*'
      Roles:
        - !Ref EKSRole
