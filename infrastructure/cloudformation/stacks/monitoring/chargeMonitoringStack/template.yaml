AWSTemplateFormatVersion: 2010-09-09

Description: >
  This template is a nested stack which deploys monitoring resources for the Charge Service

Parameters:
  ApiVersion:
    Type: String
  AwsLogicalAccountNameUpperCase:
    Type: String
  EKSLogGroup:
    Type: String
  Environment:
    Type: String
  NotificationLambdaArn:
    Type: String
  Service:
    Type: String
    Default: 'charge'
  StackName:
    Type: String
  TeamsAlarmLambda:
    Type: String
  ServiceVersion:
    Type: String

Resources:
  # SNS Topic
  CloudwatchAlarmsSNSTopic:
    Type: AWS::SNS::Topic
    Properties:
      KmsMasterKeyId: !Sub alias/${AwsLogicalAccountNameUpperCase}-SNS-KMS-Key-${Environment}
      TopicName: !Sub ${AwsLogicalAccountNameUpperCase}-Charge-Cloudwatch-Alarms-${ApiVersion}-${Environment}
      Subscription:
        - Endpoint: !Ref NotificationLambdaArn
          Protocol: 'lambda'
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion

  LambdaInvokePermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref TeamsAlarmLambda
      Action: 'lambda:InvokeFunction'
      Principal: 'sns.amazonaws.com'
      SourceArn: !Ref CloudwatchAlarmsSNSTopic

  # Lambda Alarms
  HTBCdrEventsLambdaInvocationsAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking invocations of the htbCdrEvents Lambda over a 12hr period
        AlarmName: Charge-Monitoring-HTB-Cdr-Events-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: LessThanThreshold
        DimensionName: FunctionName
        DimensionValue: !Sub ${AwsLogicalAccountNameUpperCase}-HTB-CDR-Events-${ApiVersion}-${Environment}
        Environment: !Ref Environment
        EvaluationPeriods: 1
        MetricName: Invocations
        MissingDataResponse: missing
        Namespace: AWS/Lambda
        Period: !!int 43200
        Statistic: Sum
        Threshold: 1
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_alarm.yaml

  HTBChargeEventsLambdaInvocationsAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking invocations of the htbChargeEvents Lambda over a 12hr period
        AlarmName: Charge-Monitoring-HTB-Charge-Events-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: LessThanThreshold
        DimensionName: FunctionName
        DimensionValue: !Sub ${AwsLogicalAccountNameUpperCase}-HTB-Chargepoint-Events-${ApiVersion}-${Environment}
        Environment: !Ref Environment
        EvaluationPeriods: 1
        MetricName: Invocations
        MissingDataResponse: missing
        Namespace: AWS/Lambda
        Period: !!int 43200
        Statistic: Sum
        Threshold: 1
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_alarm.yaml

  HTBNotifyEventsLambdaInvocationsAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking invocations of the htbNotifyEvents Lambda over a 12hr period
        AlarmName: Charge-Monitoring-HTB-Notify-Events-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: LessThanThreshold
        DimensionName: FunctionName
        DimensionValue: !Sub ${AwsLogicalAccountNameUpperCase}-HTB-Notify-${ApiVersion}-${Environment}
        Environment: !Ref Environment
        EvaluationPeriods: 1
        MetricName: Invocations
        MissingDataResponse: missing
        Namespace: AWS/Lambda
        Period: !!int 43200
        Statistic: Sum
        Threshold: 1
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_alarm.yaml

  DCSBulkExportCDRLambdaInvocationErrorsMetricAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for DCS-Bulk-Export-CDR lambda invocations that result in error.
        AlarmName: Charge-Monitoring-DCS-Bulk-Export-CDR-Invocation-Errors-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanOrEqualToThreshold
        DimensionName: FunctionName
        DimensionValue: !Sub ${AwsLogicalAccountNameUpperCase}-DCS-Bulk-Export-CDR-${ApiVersion}-${Environment}
        Environment: !Ref Environment
        EvaluationPeriods: 1
        MetricName: Errors
        MissingDataResponse: ignore
        Namespace: AWS/Lambda
        Period: !!int 900
        Statistic: Sum
        Threshold: 1
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_alarm.yaml

  # Metric Alarms
  BPCMCPUUtilizationMetricAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for greater than 80% CPU Utilization
        AlarmName: Charge-Monitoring-BPCM-CPU-Utilization-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanThreshold
        DimensionName: ClusterName
        DimensionValue: !Sub ${AwsLogicalAccountNameUpperCase}-Pulse-Apps-Services-${ApiVersion}-${Environment}
        Environment: !Ref Environment
        EvaluationPeriods: 1
        MetricName: pod_cpu_utilization
        MissingDataResponse: notBreaching
        Namespace: ContainerInsights
        Period: !!int 3600
        Statistic: Maximum
        Threshold: 80
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_alarm.yaml

  BPCMMemoryUtilizationMetricAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for greater than 80% Memory Utilization
        AlarmName: Charge-Monitoring-BPCM-Memory-Utilization-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanThreshold
        Environment: !Ref Environment
        EvaluationPeriods: 1
        MetricName: pod_memory_utilization
        MissingDataResponse: notBreaching
        Namespace: ContainerInsights
        Period: !!int 3600
        Statistic: Maximum
        Threshold: 80
        DimensionName: ClusterName
        DimensionValue: !Sub ${AwsLogicalAccountNameUpperCase}-Pulse-Apps-Services-${ApiVersion}-${Environment}
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_alarm.yaml

  BPCMFailedNodeCountMetricAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for greater than 1 failed node count
        AlarmName: Charge-Monitoring-BPCM-Failed-Node-Count-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanThreshold
        Environment: !Ref Environment
        EvaluationPeriods: 1
        MetricName: cluster_failed_node_count
        MissingDataResponse: notBreaching
        Namespace: ContainerInsights
        Period: !!int 3600
        Statistic: Maximum
        Threshold: 1
        DimensionName: ClusterName
        DimensionValue: !Sub ${AwsLogicalAccountNameUpperCase}-Pulse-Apps-Services-${ApiVersion}-${Environment}
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_alarm.yaml

  DCSBulkExportCdrLambdaInvocationsAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for no executions over a 24h period
        AlarmName: Charge-Monitoring-DCS-Bulk-Export-CDR-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: LessThanThreshold
        DimensionName: FunctionName
        DimensionValue: !Sub ${AwsLogicalAccountNameUpperCase}-DCS-Bulk-Export-CDR-${ApiVersion}-${Environment}
        Environment: !Ref Environment
        EvaluationPeriods: 1
        MetricName: Invocations
        MissingDataResponse: notBreaching
        Namespace: AWS/Lambda
        Period: !!int 86400
        Statistic: Sum
        Threshold: 1
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'teamsAlarmLambda'
          Value: !Ref TeamsAlarmLambda
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_alarm.yaml

  # Metric Filter Alarms
  HTBChargeStartSessionMetricFilterAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for 10 unsuccessful Start Charge Requests over a 1hr period
        AlarmName: Charge-Monitoring-HTB-Charge-Start-Request-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanOrEqualToThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '{ ($.log = "*Call HTB for START_CHARGE_REQUEST: Error*") || (($.log = "*Error encountered: logTraceId*") && (($.log = "*- startCharge function call, details:*") || ($.log = "*- startCharge failed to validate start charge request, details:*")) && ($.log = "*hasToBe*")) }'
        LogGroupName: !Ref EKSLogGroup
        MetricName: HTB-Charge-Start-Request-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 3600
        Statistic: Sum
        Threshold: 10
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  HTBChargeStopSessionMetricFilterAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for 2 unsuccessful Stop Charge Requests over a 3hr period
        AlarmName: Charge-Monitoring-HTB-Charge-Stop-Request-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanOrEqualToThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '"*Call HTB for STOP_CHARGE_REQUEST: Error*"'
        LogGroupName: !Ref EKSLogGroup
        MetricName: HTB-Charge-Stop-Request-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 10800
        Statistic: Sum
        Threshold: 2
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  ChargePostProcessingRegisteredMetricFilterAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for 1 error in the chargePostProcessingRegistered lambda over a 30min period
        AlarmName: Charge-Monitoring-Charge-PostProcessing-Registered-Failure-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '"Error in charge post processing registered lambda"'
        LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-Charge-PostProcessing-Registered-${ApiVersion}-${Environment}
        MetricName: Charge-PostProcessing-Registered-Failure-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: 300
        Statistic: 'Sum'
        Threshold: 1
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  PreAuthMissingWhenExpectedMetricFilterAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Notifies when the PreAuth is not found or is voided before we receive the CDR at least 10 times within 30 minutes
        AlarmName: Charge-PostProcessing-Registered-PreAuthMissingWhenExpected-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanOrEqualToThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '"Expected PREAUTH payment but making simple payment instead"'
        LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-Charge-PostProcessing-Registered-${ApiVersion}-${Environment}
        MetricName: Charge-PostProcessing-Registered-PreAuthMissingWhenExpected-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 1800
        Statistic: Sum
        Threshold: 10
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  BPCMChargeStartSessionMetricFilterAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for 10 unsuccessful Start Charge Requests over an 1hr period
        AlarmName: Charge-Monitoring-BPCM-Charge-Start-Request-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanOrEqualToThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '{ ($.log = "*queueStartCharge request failed to BPCM start charge endpoint*") || ($.log = "*Error encountered: logTraceId*") && (($.log = "*- startCharge function call, details:*") || ($.log = "*- startCharge failed to validate start charge request, details:*")) && ($.log = "*BPCM*") }'
        LogGroupName: !Ref EKSLogGroup
        MetricName: BPCM-Charge-Start-Request-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 3600
        Statistic: Sum
        Threshold: 10
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  BPCMChargeStopSessionMetricFilterAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for 10 unsuccessful Stop Charge Requests over a 30 minute period
        AlarmName: Charge-Monitoring-BPCM-Charge-Stop-Request-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanOrEqualToThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '{ ($.log = "*Error encountered: logTraceId*") && (($.log = "*- stopCharge function call, details:*") || ($.log = "*- stopCharge failed to validate stop charge request, details:*")) }'
        LogGroupName: !Ref EKSLogGroup
        MetricName: BPCM-Charge-Stop-Request-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 1800
        Statistic: Sum
        Threshold: 10
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  GuestPostProcessingFailureMetricFilterAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for 1 error in the guest PostProcessing event over a 30 min period
        AlarmName: Charge-Monitoring-Guest-Postprocessing-Lambda-Failure-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: ?"error" ?  "Could not" ? "Error encountered:"
        LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-Charge-PostProcessing-Guest-${ApiVersion}-${Environment}
        MetricName: Guest-PostProcessing-Failure-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 1800
        Statistic: Sum
        Threshold: 3
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'teamsAlarmLambda'
          Value: !Ref TeamsAlarmLambda
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  DCSBulkExportCDRFailureMetricFilterAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for 1 error in DCS Bulk Export CDR lambda over a 30 min period
        AlarmName: Charge-Monitoring-DCS-Bulk-Export-CDR-Failure-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '"Error"'
        LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-DCS-Bulk-Export-CDR-${ApiVersion}-${Environment}
        MetricName: DCS-Bulk-Export-CDR-Failure-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 1800
        Statistic: Sum
        Threshold: 1
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'teamsAlarmLambda'
          Value: !Ref TeamsAlarmLambda
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  DcsBulkExportCdrNoFilesMetricFilterAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for 24 consecutive executions without files to process over a period of 24 hours
        AlarmName: Charge-Monitoring-Dcs-Bulk-Export-Cdr-No-Files-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanThreshold
        DatapointsToAlarm: 24
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 24
        FilterPattern: '"Alarm: No cdr files found for processing"'
        LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-DCS-Bulk-Export-CDR-${ApiVersion}-${Environment}
        MetricName: Dcs-Bulk-Export-Cdr-No-Files-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 3600
        Statistic: Sum
        Threshold: 24
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'teamsAlarmLambda'
          Value: !Ref TeamsAlarmLambda
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  DcsBulkExportCdrDryRunMetricFilterAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for 2 consecutive executions in dry run mode over a period of 2 hours
        AlarmName: Charge-Monitoring-Dcs-Bulk-Export-Cdr-Dry-Run-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanThreshold
        DatapointsToAlarm: 2
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 2
        FilterPattern: '"Alarm: RUNNING IN DRY-RUN MODE"'
        LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-DCS-Bulk-Export-CDR-${ApiVersion}-${Environment}
        MetricName: Dcs-Bulk-Export-Cdr-Dry-Run-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 3600
        Statistic: Sum
        Threshold: 2
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'teamsAlarmLambda'
          Value: !Ref TeamsAlarmLambda
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  DcsBulkExportCdrManualFileFailureMetricFilterAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for 1 failure to process manual cdr file over a period of 1 hour
        AlarmName: Charge-Monitoring-Dcs-Bulk-Export-Cdr-Manual-File-Failure-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '"Alarm: Failed to process manual cdr files"'
        LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-DCS-Bulk-Export-CDR-${ApiVersion}-${Environment}
        MetricName: Dcs-Bulk-Export-Cdr-Manual-File-Failure-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 3600
        Statistic: Sum
        Threshold: 1
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'teamsAlarmLambda'
          Value: !Ref TeamsAlarmLambda
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  DcsBulkExportCdrB2cFileFailureMetricFilterAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for 1 failure to process b2c cdr file over a period of 1 hour
        AlarmName: Charge-Monitoring-Dcs-Bulk-Export-Cdr-B2C-File-Failure-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '"Alarm: Failed to process b2c cdr files"'
        LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-DCS-Bulk-Export-CDR-${ApiVersion}-${Environment}
        MetricName: Dcs-Bulk-Export-Cdr-B2C-File-Failure-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 3600
        Statistic: Sum
        Threshold: 1
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'teamsAlarmLambda'
          Value: !Ref TeamsAlarmLambda
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  DcsBulkExportCdrDuplicateRecordMetricFilterAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for 1 duplicate cdr record within same file over a period of 1 hour
        AlarmName: Charge-Monitoring-Dcs-Bulk-Export-Cdr-Duplicate-Record-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '"Alarm: Found duplicate cdr"'
        LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-DCS-Bulk-Export-CDR-${ApiVersion}-${Environment}
        MetricName: Dcs-Bulk-Export-Cdr-Duplicate-Record-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 3600
        Statistic: Sum
        Threshold: 1
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'teamsAlarmLambda'
          Value: !Ref TeamsAlarmLambda
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  DCSCdrEventsFailureMetricFilterAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for one error in dcsCdrEvents over a 30 min period
        AlarmName: Charge-Monitoring-DCS-CDR-Failure-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanOrEqualToThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '"Error"'
        LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-DCS-CDR-Events-${ApiVersion}-${Environment}
        MetricName: DCS-CDR-Events-Failure-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 1800
        Statistic: Sum
        Threshold: 1
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'teamsAlarmLambda'
          Value: !Ref TeamsAlarmLambda
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  DCSCdrEventsReprocessedRecordMetricFilterAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for 1 previously processed cdr over a period of 1 hour
        AlarmName: Charge-Monitoring-Dcs-Cdr-Events-Reprocessed-Record-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '"Alarm: Skipping previously processed CDR"'
        LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-DCS-CDR-Events-${ApiVersion}-${Environment}
        MetricName: Dcs-Cdr-Events-Reprocessed-Record-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 3600
        Statistic: Sum
        Threshold: 1
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'teamsAlarmLambda'
          Value: !Ref TeamsAlarmLambda
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  DCSChargeStartSessionMetricFilterAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for 10 unsuccessful Start Charge Requests over an 1hr period
        AlarmName: Charge-Monitoring-DCS-Charge-Start-Request-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanOrEqualToThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '{ ($.log = "*The remote functionality for the requested charge point is not supported*") || ($.log = "*Unable to get DCS contract information from user*") || ($.log = "*request failed for DCS charge endpoint:*") || (($.log = "*Error encountered: logTraceId*") && (($.log = "*- startCharge function call, details:*") || ($.log = "*- startCharge failed to validate start charge request, details:*")) && ($.log = "*DCS*")) }'
        LogGroupName: !Ref EKSLogGroup
        MetricName: DCS-Charge-Start-Request-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 3600
        Statistic: Sum
        Threshold: 10
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  DCSChargeMonitoringMetricFilterAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for 10 unsuccessful DCS Charge Monitoring Requests over a 30 minute period
        AlarmName: Charge-Monitoring-DCS-Charge-Monitoring-Request-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanOrEqualToThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '{($.log = "*- failed to get monitoring data from DCS API:*") || ($.log = "*- enrichMonitoringDataDCS function call, details: dcsMonitoringData not received*") || ($.log = "*- enrichMonitoringDataDCS function call, details: received CPO error from DCS setting status*")}'
        LogGroupName: !Ref EKSLogGroup
        MetricName: DCS-Charge-Monitoring-Request-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 1800
        Statistic: Sum
        Threshold: 10
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  UpdateCreditOfferBalanceFilterAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Alert if the attempt to update the balance of an offer fails
        AlarmName: Charge-Monitoring-Update-Credit-Offer-Balance-Filter-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanOrEqualToThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '"[ALARM - ApplyDiscount]"'
        LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-Charge-PostProcessing-Registered-${ApiVersion}-${Environment}
        MetricName: Update-Credit-Offer-Balance-Filter-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 3600
        Statistic: Sum
        Threshold: 1
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  DCSChargeStopSessionMetricFilterAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for 10 unsuccessful DCS Stop Charge Requests over a 30 minute period
        AlarmName: Charge-Monitoring-DCS-Charge-Stop-Request-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanOrEqualToThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '{ ($.log = "*queueStopCharge - dcs contract id:*") && ($.log = "*request failed for DCS charge endpoint*") }'
        LogGroupName: !Ref EKSLogGroup
        MetricName: DCS-Charge-Stop-Request-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 1800
        Statistic: Sum
        Threshold: 10
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  ChargepointAvailabilityCheckInvalidInputAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for an invalid input of country/provider over a 1h period
        AlarmName: Chargepoint-Availability-Check-Invalid-Input-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanOrEqualToThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '"[ALARM chargepointAvailabilityCheck - INVALID INPUT]"'
        LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-chargePointAvailabilityCheck-${ApiVersion}-${Environment}
        MetricName: Chargepoint-Availability-Check-Invalid-Input-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 3600
        Statistic: Sum
        Threshold: 1
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  ChargepointAvailabilityCheckUnknownErrorAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for any error in the running of the processing lambdas, when 500 returned over a 1h period
        AlarmName: Chargepoint-Availability-Check-Unknown-Error-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanOrEqualToThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '"[ALARM chargepointAvailabilityCheck - EXECUTION FAILED]"'
        LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-chargePointAvailabilityCheck-${ApiVersion}-${Environment}
        MetricName: Chargepoint-Availability-Check-Unknown-Error-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 3600
        Statistic: Sum
        Threshold: 1
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  ChargepointAvailabilityCheckEmspSitesErrorAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for any error when calling our emsp apollo for the all sites list for a given batch over a 1h period
        AlarmName: Chargepoint-Availability-Check-EMSP-Sites-Error-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanOrEqualToThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '"[ALARM chargepointAvailabilityCheck - EMSP SITES ERROR]"'
        LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-chargePointAvailabilityCheck-${ApiVersion}-${Environment}
        MetricName: Chargepoint-Availability-Check-EMSP-Sites-Error-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 3600
        Statistic: Sum
        Threshold: 2
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  ChargepointAvailabilityCheckEmspChargepointApiErrorAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for any error when calling our emsp apollo for the a given CP, over a 1h period
        AlarmName: Chargepoint-Availability-Check-EMSP-Chargepoint-API-Error-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanOrEqualToThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '"[ALARM chargepointAvailabilityCheck - Apollo API Error]"'
        LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-chargePointAvailabilityCheck-${ApiVersion}-${Environment}
        MetricName: Chargepoint-Availability-Check-EMSP-Chargepoint-API-Error-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 3600
        Statistic: Sum
        Threshold: 5
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  ChargepointAvailabilityCheckEmspChargepointNotFoundAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for errors when no CP found with given ID for EMSP, over a 1h period
        AlarmName: Chargepoint-Availability-Check-EMSP-Chargepoint-Not-Found-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanOrEqualToThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '"[ALARM chargepointAvailabilityCheck - EMSP CP NOT FOUND]"'
        LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-chargePointAvailabilityCheck-${ApiVersion}-${Environment}
        MetricName: Chargepoint-Availability-Check-EMSP-Chargepoint-Not-Found-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 3600
        Statistic: Sum
        Threshold: 5
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  ChargepointAvailabilityCheckHtbChargepointApiErrorAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking if unable to call HTB for a given CP, over a 1h period
        AlarmName: Chargepoint-Availability-Check-HTB-Chargepoint-API-Error-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanOrEqualToThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '"[ALARM chargepointAvailabilityCheck - HTB API ERROR]"'
        LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-chargePointAvailabilityCheck-${ApiVersion}-${Environment}
        MetricName: Chargepoint-Availability-Check-HTB-Chargepoint-API-Error-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 3600
        Statistic: Sum
        Threshold: 5
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  ChargepointAvailabilityCheckHtbChargepointNotFoundAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for errors when no CP found with given ID in HTB, over a 1h period
        AlarmName: Chargepoint-Availability-Check-HTB-Chargepoint-Not-Found-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanOrEqualToThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '"[ALARM chargepointAvailabilityCheck - HTB CP NOT FOUND]"'
        LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-chargePointAvailabilityCheck-${ApiVersion}-${Environment}
        MetricName: Chargepoint-Availability-Check-HTB-Chargepoint-Not-Found-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 3600
        Statistic: Sum
        Threshold: 5
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  ChargepointAvailabilityCheckCvChargepointApiErrorAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking if unable to call CV for a given CP, over a 1h period
        AlarmName: Chargepoint-Availability-Check-CV-Chargepoint-API-Error-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanOrEqualToThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '"[ALARM chargepointAvailabilityCheck - CV API ERROR]"'
        LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-chargePointAvailabilityCheck-${ApiVersion}-${Environment}
        MetricName: Chargepoint-Availability-Check-CV-Chargepoint-API-Error-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 3600
        Statistic: Sum
        Threshold: 5
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  ChargepointAvailabilityCheckCvChargepointNotFoundAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for errors when no CP found with given ID in CV, over a 1h period
        AlarmName: Chargepoint-Availability-Check-CV-Chargepoint-Not-Found-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanOrEqualToThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '"[ALARM chargepointAvailabilityCheck - CV CP NOT FOUND]"'
        LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-chargePointAvailabilityCheck-${ApiVersion}-${Environment}
        MetricName: Chargepoint-Availability-Check-CV-Chargepoint-Not-Found-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 3600
        Statistic: Sum
        Threshold: 5
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  ChargepointAvailabilityCheckCsvUploadFailureAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for errors when uploading .csv file to S3, over a 1h period
        AlarmName: Chargepoint-Availability-Check-CSV-Upload-Failure-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanOrEqualToThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '"[ALARM chargepointAvailabilityCheck - CSV UPLOAD FAILURE]"'
        LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-chargePointAvailabilityCheck-${ApiVersion}-${Environment}
        MetricName: Chargepoint-Availability-Check-CSV-Upload-Failure-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 3600
        Statistic: Sum
        Threshold: 1
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  ChargepointAvailabilityCheckBucketNotFoundAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for S3 Buckets errors, over a 1h period
        AlarmName: Chargepoint-Availability-Check-Bucket-Not-Found-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanOrEqualToThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '"[ALARM chargepointAvailabilityCheck - S3 BUCKET NOT FOUND]"'
        LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-chargePointAvailabilityCheck-${ApiVersion}-${Environment}
        MetricName: Chargepoint-Availability-Check-Bucket-Not-Found-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 3600
        Statistic: Sum
        Threshold: 1
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  ChargepointAvailabilityCheckTriggerInvalidInputAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for S3 Buckets errors, over a 1h period
        AlarmName: Chargepoint-Availability-Check-Trigger-Invalid-Input-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanOrEqualToThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '"[ALARM chargepointAvailabilityCheckTrigger - INVALID INPUT]"'
        LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-chargePointAvailabilityCheckTrigger-${ApiVersion}-${Environment}
        MetricName: Chargepoint-Availability-Check-Trigger-Invalid-Input-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 3600
        Statistic: Sum
        Threshold: 1
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  ChargepointAvailabilityCheckTriggerInvocationErrorAlarm:
    Type: AWS::CloudFormation::Stack
    Properties:
      Parameters:
        AwsLogicalAccountNameUpperCase: !Ref AwsLogicalAccountNameUpperCase
        AlarmActions: !Ref CloudwatchAlarmsSNSTopic
        AlarmDescription: Checking for S3 Buckets errors, over a 1h period
        AlarmName: Chargepoint-Availability-Check-Trigger-Invocation-Error-Alarm
        ApiVersion: !Ref ApiVersion
        ComparisonOperator: GreaterThanOrEqualToThreshold
        DatapointsToAlarm: 1
        DefaultValue: 0
        Environment: !Ref Environment
        EvaluationPeriods: 1
        FilterPattern: '"[ALARM chargePointAvailabilityCheckTrigger - INVOCATION ERROR]"'
        LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-chargePointAvailabilityCheckTrigger-${ApiVersion}-${Environment}
        MetricName: Chargepoint-Availability-Check-Trigger-Invocation-Error-Metric
        MetricValue: 1
        MissingDataResponse: notBreaching
        Namespace: !Sub Charge-Metrics-${ApiVersion}-${Environment}
        Period: !!int 3600
        Statistic: Sum
        Threshold: 1
      Tags:
        - Key: 'environment'
          Value: !Ref Environment
        - Key: 'service'
          Value: !Ref Service
        - Key: 'apiVersion'
          Value: !Ref ApiVersion
        - Key: 'stack'
          Value: !Ref StackName
        - Key: 'serviceVersion'
          Value: !Ref ServiceVersion
      TemplateURL: ../../../nestedStacks/cloudwatch/metric_filter_alarm.yaml

  # Composite Alarms
  ChargepointAvailabilityCheckCompositeAlarm:
    Type: AWS::CloudWatch::CompositeAlarm
    Properties:
      AlarmActions:
        - !Ref CloudwatchAlarmsSNSTopic
      AlarmName: !Sub ${AwsLogicalAccountNameUpperCase}-Chargepoint-Availability-Check-Composite-Alarm-${ApiVersion}-${Environment}
      AlarmRule: !Sub
        - 'ALARM(${TriggerInvocationError}) OR ALARM(${TriggerInvalidInput}) OR ALARM(${LambdaBucketNotFound}) OR ALARM(${LambdaCsvFailure}) OR ALARM(${LambdaCvCpNotFound}) OR ALARM(${LambdaCvApiError}) OR ALARM(${LambdaHtbCpNotFound}) OR ALARM(${LambdaHtbApiError}) OR ALARM(${LambdaEmspCpNotFound}) OR ALARM(${LambdaEmspApiError}) OR ALARM(${LambdaEmspSitesError}) OR ALARM(${LambdaInvalidInput})'
        - TriggerInvocationError:
            !GetAtt [
              ChargepointAvailabilityCheckTriggerInvocationErrorAlarm,
              Outputs.MetricFilterAlarmArn,
            ]
          TriggerInvalidInput:
            !GetAtt [
              ChargepointAvailabilityCheckTriggerInvalidInputAlarm,
              Outputs.MetricFilterAlarmArn,
            ]
          LambdaBucketNotFound:
            !GetAtt [
              ChargepointAvailabilityCheckBucketNotFoundAlarm,
              Outputs.MetricFilterAlarmArn,
            ]
          LambdaCsvFailure:
            !GetAtt [
              ChargepointAvailabilityCheckCsvUploadFailureAlarm,
              Outputs.MetricFilterAlarmArn,
            ]
          LambdaCvCpNotFound:
            !GetAtt [
              ChargepointAvailabilityCheckCvChargepointNotFoundAlarm,
              Outputs.MetricFilterAlarmArn,
            ]
          LambdaCvApiError:
            !GetAtt [
              ChargepointAvailabilityCheckCvChargepointApiErrorAlarm,
              Outputs.MetricFilterAlarmArn,
            ]
          LambdaHtbCpNotFound:
            !GetAtt [
              ChargepointAvailabilityCheckHtbChargepointNotFoundAlarm,
              Outputs.MetricFilterAlarmArn,
            ]
          LambdaHtbApiError:
            !GetAtt [
              ChargepointAvailabilityCheckHtbChargepointApiErrorAlarm,
              Outputs.MetricFilterAlarmArn,
            ]
          LambdaEmspCpNotFound:
            !GetAtt [
              ChargepointAvailabilityCheckEmspChargepointNotFoundAlarm,
              Outputs.MetricFilterAlarmArn,
            ]
          LambdaEmspApiError:
            !GetAtt [
              ChargepointAvailabilityCheckEmspChargepointApiErrorAlarm,
              Outputs.MetricFilterAlarmArn,
            ]
          LambdaEmspSitesError:
            !GetAtt [
              ChargepointAvailabilityCheckEmspSitesErrorAlarm,
              Outputs.MetricFilterAlarmArn,
            ]
          LambdaInvalidInput:
            !GetAtt [
              ChargepointAvailabilityCheckInvalidInputAlarm,
              Outputs.MetricFilterAlarmArn,
            ]

  ChargeStartCompositeAlarm:
    Type: AWS::CloudWatch::CompositeAlarm
    Properties:
      AlarmActions:
        - !Ref CloudwatchAlarmsSNSTopic
      AlarmName: !Sub ${AwsLogicalAccountNameUpperCase}-Charge-Start-Composite-Alarm-${ApiVersion}-${Environment}
      AlarmRule: !Sub
        - 'ALARM(${HTBChargeStartSessionAlarmARN}) OR ALARM(${DCSChargeStartSessionAlarmARN}) OR ALARM(${BPCMChargeStartSessionAlarmARN})'
        - HTBChargeStartSessionAlarmARN:
            !GetAtt [
              HTBChargeStartSessionMetricFilterAlarm,
              Outputs.MetricFilterAlarmArn,
            ]
          DCSChargeStartSessionAlarmARN:
            !GetAtt [
              DCSChargeStartSessionMetricFilterAlarm,
              Outputs.MetricFilterAlarmArn,
            ]
          BPCMChargeStartSessionAlarmARN:
            !GetAtt [
              BPCMChargeStartSessionMetricFilterAlarm,
              Outputs.MetricFilterAlarmArn,
            ]

  ChargePostProcessingCompositeAlarm:
    Type: AWS::CloudWatch::CompositeAlarm
    Properties:
      AlarmActions:
        - !Ref CloudwatchAlarmsSNSTopic
      AlarmName: !Sub ${AwsLogicalAccountNameUpperCase}-Charge-Post-Processing-Composite-Alarm-${ApiVersion}-${Environment}
      AlarmRule: !Sub
        - 'ALARM(${ChargePostProcessingRegisteredAlarmARN}) OR ALARM(${ChargePostProcessingGuestAlarmARN}) OR ALARM(${UpdateCreditOfferBalanceAlarmARN})'
        - ChargePostProcessingRegisteredAlarmARN:
            !GetAtt [
              ChargePostProcessingRegisteredMetricFilterAlarm,
              Outputs.MetricFilterAlarmArn,
            ]
          ChargePostProcessingGuestAlarmARN:
            !GetAtt [
              GuestPostProcessingFailureMetricFilterAlarm,
              Outputs.MetricFilterAlarmArn,
            ]
          UpdateCreditOfferBalanceAlarmARN:
            !GetAtt [
              UpdateCreditOfferBalanceFilterAlarm,
              Outputs.MetricFilterAlarmArn,
            ]

  # Metric filters
  MissingPreauthMetricFilter:
    Type: AWS::Logs::MetricFilter
    Properties:
      LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-Charge-PostProcessing-Registered-${ApiVersion}-${Environment}
      FilterPattern: '"Expected PREAUTH payment but making simple payment instead" "preAuthStatus: false" "voidTransaction: false"'
      MetricTransformations:
        - MetricName: MissingPreauth
          MetricNamespace: !Sub Grafana/${Environment}
          MetricValue: '1'
          DefaultValue: 0
          Unit: Count

  VoidedPreauthMetricFilter:
    Type: AWS::Logs::MetricFilter
    Properties:
      LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-Charge-PostProcessing-Registered-${ApiVersion}-${Environment}
      FilterPattern: '"Expected PREAUTH payment but making simple payment instead" "voidTransaction: true"'
      MetricTransformations:
        - MetricName: VoidedPreauth
          MetricNamespace: !Sub Grafana/${Environment}
          MetricValue: '1'
          DefaultValue: 0
          Unit: Count

  PreAuthServerFailureMetricFilter:
    Type: AWS::Logs::MetricFilter
    Properties:
      LogGroupName: !Sub /aws/containerinsights/${AwsLogicalAccountNameUpperCase}-Pulse-Apps-Services-${ApiVersion}-${Environment}/application
      FilterPattern: '"payments-bppay-server.registeredPreAuthResolver" "Code:500"'
      MetricTransformations:
        - MetricName: PreAuthServerFailure
          MetricNamespace: !Sub Grafana/${Environment}
          MetricValue: '1'
          DefaultValue: 0
          Unit: Count

  PreAuthCaptureFailureMetricFilter:
    Type: AWS::Logs::MetricFilter
    Properties:
      LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-Charge-PostProcessing-Registered-${ApiVersion}-${Environment}
      FilterPattern: '"charge-service.chargePostProcessingRegistered lambda" "Pre-auth payment failed"'
      MetricTransformations:
        - MetricName: PreAuthCaptureFailure
          MetricNamespace: !Sub Grafana/${Environment}
          MetricValue: '1'
          DefaultValue: 0
          Unit: Count

  PreAuthCaptureSuccessMetricFilter:
    Type: AWS::Logs::MetricFilter
    Properties:
      LogGroupName: !Sub /aws/lambda/${AwsLogicalAccountNameUpperCase}-Charge-PostProcessing-Registered-${ApiVersion}-${Environment}
      FilterPattern: '"charge-service.chargePostProcessingRegistered lambda" "Pre-auth payment captured"'
      MetricTransformations:
        - MetricName: PreAuthCaptureSuccess
          MetricNamespace: !Sub Grafana/${Environment}
          MetricValue: '1'
          DefaultValue: 0
          Unit: Count

  PreAuthVoidOrderCaptureSuccessMetricFilter:
    Type: AWS::Logs::MetricFilter
    Properties:
      LogGroupName: !Sub /aws/containerinsights/${AwsLogicalAccountNameUpperCase}-Pulse-Apps-Services-${ApiVersion}-${Environment}/application
      FilterPattern: '"payments-bppay-server.voidOrderResolver" "Code:200" "voidOrderDpaas success"'
      MetricTransformations:
        - MetricName: VoidOrderCaptureSuccess
          MetricNamespace: !Sub Grafana/${Environment}
          MetricValue: '1'
          DefaultValue: 0
          Unit: Count

  PreAuthVoidOrderCaptureFailureMetricFilter:
    Type: AWS::Logs::MetricFilter
    Properties:
      LogGroupName: !Sub /aws/containerinsights/${AwsLogicalAccountNameUpperCase}-Pulse-Apps-Services-${ApiVersion}-${Environment}/application
      FilterPattern: '"payments-bppay-server.voidOrderResolver" "Code:400" "voidOrderDpaas error"'
      MetricTransformations:
        - MetricName: VoidOrderCaptureFailure
          MetricNamespace: !Sub Grafana/${Environment}
          MetricValue: '1'
          DefaultValue: 0
          Unit: Count
