{"name": "push-notifications-trigger", "private": true, "main": "dist/pushNotificationsTrigger/index.js", "scripts": {"prebuild": "npm run clean", "build": "npm install && tsc && cp package.json dist/package.json && cp package-lock.json dist/package-lock.json && cd dist && npm ci --production", "clean": "rm -rf dist/", "start": "ts-node run-local.ts", "test": "jest", "types:check": "tsc --noEmit"}, "dependencies": {"aws-lambda": "^1.0.7", "axios": "^1.10.0"}, "devDependencies": {"@types/aws-lambda": "^8.10.119", "@types/jest": "^27.5.2", "@types/node": "^18.11.2", "@types/uuid": "^8.3.4", "jest": "^29.0.3", "lambda-local": "^2.0.2", "ts-jest": "^29.4.1", "ts-node": "^10.9.1", "typescript": "^4.8.4"}}