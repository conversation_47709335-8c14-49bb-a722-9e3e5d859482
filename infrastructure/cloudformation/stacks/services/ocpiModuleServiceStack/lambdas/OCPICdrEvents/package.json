{"name": "ocpicdrevent", "private": true, "main": "dist/OCPICdrEvents/index.js", "scripts": {"prebuild": "npm run clean", "build": "npm i && tsc --project tsconfig.build.json && cp package.json dist/package.json && cp package-lock.json dist/package-lock.json && cd dist && npm ci --production", "clean": "rm -rf dist/", "removeMock": "find dist -type f -name '*.mock.*' -delete", "removeTest": "find dist -type f -name '*.test.*' -delete", "start": "ts-node run-local.ts", "test": "jest", "types:check": "tsc --noEmit"}, "dependencies": {"axios": "^1.10.0", "graphql-request": "^3.5.0", "ioredis": "^5.4.1", "lambda-local": "^2.2.0", "ts-node": "^10.9.2", "winston": "^3.11.0", "zod": "^3.23.8"}, "devDependencies": {"@aws-sdk/client-lambda": "^3.454.0", "@types/jest": "^27.5.2", "@types/node": "^18.11.2", "jest": "^29.7.0", "ts-jest": "^29.0.3", "typescript": "^4.8.4"}}