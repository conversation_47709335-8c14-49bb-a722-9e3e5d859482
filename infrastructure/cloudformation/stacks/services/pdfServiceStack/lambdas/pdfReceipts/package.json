{"name": "receipt_generation", "private": true, "scripts": {"prebuild": "rm -rf dist/", "build": "npm i && tsc && cp package.json dist/package.json && cp -R assets dist/pdfReceipts/assets && cp -R templates/. dist/pdfReceipts/templates && cp package-lock.json dist/package-lock.json && cd dist && npm ci --production"}, "dependencies": {"@puppeteer/browsers": "^1.8.0", "@sparticuz/chromium": "^118.0.0", "aws-sdk": "^2.1138.0", "axios": "^1.10.0", "graphql": "^16.3.0", "graphql-request": "^4.2.0", "handlebars": "^4.7.7", "is-eu-member": "^1.0.6", "moment": "^2.29.4", "moment-timezone": "^0.5.41", "nock": "^13.2.4", "os": "^0.1.2", "pdf-lib": "^1.17.1", "puppeteer-core": "^21.4.0", "uuidv4": "^6.2.13", "winston": "^3.7.2"}, "devDependencies": {"@types/jest": "^27.5.2", "@types/node": "^18.15.11", "jest": "^29.0.3", "puppeteer": "^21.4.0", "ts-jest": "^29.1.0", "typescript": "^4.9.5"}}