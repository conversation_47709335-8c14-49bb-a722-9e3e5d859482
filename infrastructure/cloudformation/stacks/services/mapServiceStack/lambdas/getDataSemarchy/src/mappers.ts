import {
  Fuel,
  Provider,
  Service,
  SiteBrand,
} from '../../shared/elastic-search.types';
import { toSiteBrand } from '../../shared/mappers';
import { PutMapDataHours, SiteData } from '../../shared/put-map-data-api.types';
import { uniq } from '../../shared/utils';
import {
  OperatingHour,
  RetailSite,
  RetailSiteFeature,
  RetailSiteProduct,
  SemarchySite,
} from './semarchy.types';

export function toSerial(site: SemarchySite): string {
  if (site.RetailSites[0]?.RetailSiteBrand === SiteBrand.AR) {
    const code = site.LocationSourceCodes.find(
      (code) => code.SourceSystem === 'EXT',
    );
    if (code) {
      return code.ExternalLocationCode;
    }
  }

  return site.LocationID;
}

export function toProvider(site: SemarchySite): Provider {
  if (site.RetailSites[0]?.RetailSiteBrand === SiteBrand.AR) {
    return Provider.ARAL;
  }

  return Provider.SEMARCHY;
}

export function toSiteData(site: SemarchySite): SiteData {
  const retailSite = site.RetailSites.length ? site.RetailSites[0] : null;
  const contact = site.Contacts.length ? site.Contacts[0] : null;

  const brandName = toSiteBrand(retailSite?.RetailSiteBrand);

  // TODO: revert after AralMe 7.0.0 release
  const isAral = brandName === SiteBrand.AR;
  const fuels = isAral ? toFuels(retailSite?.RetailSiteProducts || []) : [];

  // Required becasue Semarchy stores data as GB, but emsp uses UK
  const validatedCountryCode =
    site.CountryCode === 'GB' ? 'UK' : site.CountryCode;

  return {
    brandName,
    hasFuel: !!fuels?.length,
    services: toServices(site, fuels, brandName),
    fuels,
    sapId: site.LocationCode,
    details: {
      siteName: site.LocationName1 || undefined,
      address: site.Street1 || undefined,
      city: site.City || undefined,
      postcode: site.PostalCode || site.PostalCodeZip4 || undefined,
      country: validatedCountryCode,
      location: {
        lat: parseFloat(site.Latitude),
        lon: parseFloat(site.Longitude),
      },
      email: contact?.WorkEmailAddress || undefined,
      fax: contact?.FaxNumber || undefined,
      phone: contact?.WorkPhoneNumber || undefined,
      hours: toProviderHours(retailSite?.RetailSiteOperatingHours, brandName),
      siteProviderIds: [{ type: 'SAP_ID', id: site.LocationCode }],
    },
  };
}

export function fuelsToServices(
  fuels: Array<Fuel>,
  brand?: SiteBrand,
): Array<Service> {
  const services: Array<Service> = [];

  if (brand === SiteBrand.AR) {
    const hasUltimate =
      fuels.includes(Fuel.ULTIMATE_102) || fuels.includes(Fuel.ULTIMATE_DIESEL);
    if (hasUltimate) {
      services.push(Service.ARAL_ULTIMATE);
    }
  }

  if (fuels.includes(Fuel.LPG)) {
    services.push(Service.LPG);
  }

  if (fuels.includes(Fuel.AD_BLUE)) {
    services.push(Service.AD_BLUE);
  }

  if (fuels.includes(Fuel.LKW_DIESEL)) {
    services.push(Service.TRUCK_DIESEL);
  }

  if (fuels.includes(Fuel.ERDGAS)) {
    services.push(Service.NATURAL_GAS);
  }

  return services;
}

export function retailSiteProductsToServices(
  retailSite: RetailSite,
): Array<Service> {
  const products = retailSite.RetailSiteProducts ?? [];

  return products
    .map((product) => PRODUCT_TO_SERVICE_MAP[product.Product.ProductID])
    .filter((product) => product !== undefined);
}

export function retailSiteFeaturesToServices(
  retailSite: RetailSite,
): Array<Service> {
  const features = retailSite.RetailSiteFeatures ?? [];

  return features
    .filter(isFeatureAvailable)
    .map((feature) => FEATURE_TO_SERVICE_MAP[feature.FeatureName])
    .filter((service) => service !== undefined);
}

export function retailSiteCharcteristicsToServices(
  retailSite: RetailSite,
): Array<Service> {
  const characteristics = retailSite.RetailSiteCharcteristics ?? [];
  const services: Array<Service> = [];

  if (characteristics.some((c) => c.StoreShopOffer === 'RTG')) {
    services.push(Service.REWE_TO_GO);
  }

  return services;
}

export function retailSiteChannelOfTradesToServices(
  retailSite: RetailSite,
): Array<Service> {
  const channelOfTrades = retailSite.RetailChannelOfTrades ?? [];
  const services: Array<Service> = [];

  if (
    channelOfTrades.some((channel) => channel.RetailChannelOfTrade === 'UNCO')
  ) {
    services.push(Service.ADS);
  }

  return services;
}

export function toServices(
  site: SemarchySite,
  fuels: Array<Fuel>,
  brand?: SiteBrand,
): Array<Service> {
  const retailSite = site.RetailSites.length ? site.RetailSites[0] : null;
  const services = fuelsToServices(fuels, brand);

  if (retailSite) {
    services.push(...retailSiteFeaturesToServices(retailSite));
    services.push(...retailSiteProductsToServices(retailSite));
    services.push(...retailSiteCharcteristicsToServices(retailSite));
    services.push(...retailSiteChannelOfTradesToServices(retailSite));

    if (retailSite.BPMePlus_Participating_Site) {
      services.push(Service.BP_ME);
    }

    if (isSiteOpen24Hours(retailSite)) {
      services.push(Service.TWENTY_FOUR_HOURS);
    }
  }

  return services.filter(uniq);
}

export function toFuels(products: Array<RetailSiteProduct>): Array<Fuel> {
  return products
    .map((p) => PRODUCT_TO_FUEL_MAP[p.Product.ProductID])
    .filter((p) => p !== undefined);
}

export function toProviderHours(
  operatingHours?: Array<OperatingHour>,
  brand?: SiteBrand,
): PutMapDataHours | undefined {
  if (!operatingHours || !operatingHours.length) {
    return undefined;
  }

  const timeFor = (
    dayOfWeek: string,
    timeProp: 'OpeningTime' | 'ClosingTime',
  ): string => {
    const day = operatingHours.find((day) => day.DayOfWeek === dayOfWeek);

    if (day?.[timeProp]) {
      const time = String(day[timeProp]);
      return time.split(':').slice(0, 2).join(':');
    }

    if (day && isDayOpen24Hours(day, brand)) {
      return timeProp === 'OpeningTime' ? '00:00' : '23:59';
    }

    return '';
  };

  return {
    monOpening: timeFor('Mon', 'OpeningTime'),
    monClosing: timeFor('Mon', 'ClosingTime'),
    tueOpening: timeFor('Tue', 'OpeningTime'),
    tueClosing: timeFor('Tue', 'ClosingTime'),
    wedOpening: timeFor('Wed', 'OpeningTime'),
    wedClosing: timeFor('Wed', 'ClosingTime'),
    thuOpening: timeFor('Thur', 'OpeningTime'),
    thuClosing: timeFor('Thur', 'ClosingTime'),
    friOpening: timeFor('Fri', 'OpeningTime'),
    friClosing: timeFor('Fri', 'ClosingTime'),
    satOpening: timeFor('Sat', 'OpeningTime'),
    satClosing: timeFor('Sat', 'ClosingTime'),
    sunOpening: timeFor('Sun', 'OpeningTime'),
    sunClosing: timeFor('Sun', 'ClosingTime'),
  };
}

function isSiteOpen24Hours(retailSite: RetailSite): boolean {
  return retailSite.RetailSiteOperatingHours.filter(
    (h) => h.DayOfWeek !== 'Hol', // exclude holidays
  )?.every((h) => isDayOpen24Hours(h, retailSite.RetailSiteBrand));
}

function isDayOpen24Hours(
  h: OperatingHour,
  brand: string | undefined,
): boolean {
  return brand === SiteBrand.AR
    ? isAralOperating24Hours(h)
    : isOperating24Hours(h);
}

function isAralOperating24Hours(h: OperatingHour): boolean {
  const isOpeningMatching24Hours =
    h.OpeningTime === null || h.OpeningTime === '00:00:00';
  const isClosingMatching24Hours = h.ClosingTime === '23:59:00';

  return isOpeningMatching24Hours && isClosingMatching24Hours;
}

function isOperating24Hours(h: OperatingHour): boolean {
  return h.Site24Hours === 'Yes';
}

function isFeatureAvailable(f: RetailSiteFeature): boolean {
  return (
    typeof f.FeatureValue === 'string' &&
    f.FeatureValue.toUpperCase() !== 'N' &&
    f.FeatureValue.toUpperCase() !== 'NO' &&
    f.FeatureValue.toUpperCase() !== 'NONE' &&
    f.FeatureValue.toUpperCase() !== 'NO DIESEL'
  );
}

// ---> DATA MAPPINGS <---

const PRODUCT_TO_FUEL_MAP: Record<string, Fuel> = {
  // BP - TEST
  PFE000000000100003251: Fuel.SUPER_E5,
  PFE000000000100002952: Fuel.SUPER_PLUS,
  PFE000000000100002963: Fuel.DIESEL,
  PFE000000000100002964: Fuel.ULTIMATE_DIESEL,

  // BP - PROD
  PRE000000000100003251: Fuel.SUPER_E5,
  PRE000000000100002952: Fuel.SUPER_PLUS,
  PRE000000000100002963: Fuel.DIESEL,
  PRE000000000100002964: Fuel.ULTIMATE_DIESEL,

  // ARAL - TEST
  PFE000000000100002652: Fuel.SUPER_E5,
  PFE000000000100002650: Fuel.SUPER_E10,
  PFE000000000100002654: Fuel.SUPER_PLUS,
  PFE000000000100002656: Fuel.ULTIMATE_102,
  PFE000000000100002658: Fuel.DIESEL,
  PFE000000000100002657: Fuel.ULTIMATE_DIESEL,
  PFE000000000100002659: Fuel.LKW_DIESEL,

  // ARAL - PROD
  PRE000000000100002652: Fuel.SUPER_E5,
  PRE000000000100002650: Fuel.SUPER_E10,
  PRE000000000100002654: Fuel.SUPER_PLUS,
  PRE000000000100002656: Fuel.ULTIMATE_102,
  PRE000000000100002658: Fuel.DIESEL,
  PRE000000000100002657: Fuel.ULTIMATE_DIESEL,
  PRE000000000100002659: Fuel.LKW_DIESEL,

  // COMMON - TEST
  PFE000000000100000589: Fuel.ERDGAS,
  PFE000000000100000026: Fuel.LPG,
  PFE000000000100002168: Fuel.AD_BLUE,

  // COMMON - PROD
  PRE000000000100000589: Fuel.ERDGAS,
  PRE000000000100000026: Fuel.LPG,
  PRE000000000100002168: Fuel.AD_BLUE,
};

const PRODUCT_TO_SERVICE_MAP: Record<string, Service> = {
  // ARAL - TEST
  PFE000000000160000648: Service.WASH,
  PFE000000000160000652: Service.TRUCK_WASH,
  PFE000000000160000655: Service.SUPER_BOX,

  // ARAL - PROD
  PRE000000000160000648: Service.WASH,
  PRE000000000160000652: Service.TRUCK_WASH,
  PRE000000000160000655: Service.SUPER_BOX,
};

const FEATURE_TO_SERVICE_MAP: Record<string, Service> = {
  //'': Service.TWENTY_FOUR_HOURS,      // inferred from hours
  //'': Service.NEAR_MOTORWAY,
  //'': Service.CAR_COURT,
  //'': Service.TOLL_STATION,           // no longer used contract is closed
  ATM: Service.ATM_MACHINE,
  //'': Service.ARAL_STORE,
  'Bistro Offer Type 1': Service.PETIT_BISTRO,
  'Bistro Offer Type 2': Service.PETIT_BISTRO,
  'Bistro Build Type 1': Service.PETIT_BISTRO,
  'Bistro Build Type 2': Service.PETIT_BISTRO,
  //'': Service.PAYBACK,
  //'': Service.ARAL_ULTIMATE,          // inferred from fuels
  //'': Service.LPG,                    // inferred from fuels
  //'': Service.NATURAL_GAS,            // inferred from fuels
  //'': Service.TRUCK_DIESEL,           // inferred from fuels
  //'': Service.AD_BLUE,                // inferred from fuels
  //'': Service.CAR_WASH_PLANT,
  //'': Service.SUPER_WASH,
  'SuperBox Wash': Service.SUPER_BOX, // also inferred from products
  //'': Service.CABRIO_CARE,
  //'': Service.SUV_WASH,
  //'': Service.VDA,
  //'': Service.ARAL_FUEL_CARD,
  //'': Service.REWE_TO_GO,             // inferred from StoreShopOffer
  CarWash: Service.WASH, // also inferred from products
  'E55 Carwash': Service.WASH, // also inferred from products
  'Carwash Plant': Service.CAR_WASH_PLANT,
  //'': Service.CAR_RENTAL,             // no longer used, no entries
  //'': Service.RESTAURANT,
  //'': Service.TRUCK_WASH,
  //'': Service.BP_ME,                  // inferred from BPMePlus_Participating_Site
  //'': Service.PAYBACK_FUEL_AND_GO,
  //'': Service.EV,                     // inferred from hasEvCharging
  //'': Service.RECUP,
  //'': Service.TOO_GOOD_TO_GO,
  //'': Service.ADS,                    // inferred from RetailChannelOfTrade
};
