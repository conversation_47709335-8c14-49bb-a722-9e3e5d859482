import { Handler, ScheduledEvent } from 'aws-lambda';
import { AxiosError } from 'axios';
import { Provider } from '../../shared/elastic-search.types';
import {
  Operation,
  PutMapDataUpsertRequest,
  SiteData,
  SiteRequest,
} from '../../shared/put-map-data-api.types';
import { chunk } from '../../shared/utils';
import { fetchAllAralStations } from './aral-api';
import { buildSiteRequests } from './builders';
import { decorateAral } from './decorators';
import { searchAllSitesByProviderAndCountry } from './elastic-search';
import { env } from './env';
import { putMapDataApi } from './put-map-data-api';
import { fetchRegions } from './semarchy-api';
import { SemarchySite } from './semarchy.types';
import { isValidRequest, parseRegions, updateRegionCodes } from './utils';

export const handler: <PERSON>ler<ScheduledEvent, void> = async (event, ctx) => {
  const countries = parseRegions(env.SEMARCHY_REGIONS);
  const sites = await fetchRegions(countries);
  console.info(`Fetched ${sites.length} Semarchy sites`);

  console.info(`Searching all Semarchy sites from ElasticSearch`);
  updateRegionCodes(countries);
  const existingSites = await searchAllSitesByProviderAndCountry(
    [Provider.ARAL, Provider.SEMARCHY],
    countries,
  );

  const existingSemarchy = existingSites.filter(
    (s) => s._source?.provider === Provider.SEMARCHY,
  );
  const existingAral = existingSites.filter(
    (s) => s._source?.provider === Provider.ARAL,
  );
  console.info(
    `Found ${existingSites.length} sites on ElasticSearch => ${existingSemarchy.length} Semarchy | ${existingAral.length} Aral`,
  );

  const reqs = buildSiteRequests(sites, existingSites);
  const validReqs = reqs.filter(isValidRequest);

  // enhance Semarchy data with Antwerpes data
  const aralUpsertReqs = validReqs.filter(
    (r) => r.operation !== Operation.DELETE && r.provider === Provider.ARAL,
  ) as Array<PutMapDataUpsertRequest<SiteData>>;

  if (aralUpsertReqs.length) {
    const stations = await fetchAllAralStations();
    console.info(`Finished to fetch ${stations.length} Aral stations`);
    decorateAral(aralUpsertReqs, stations);
  }

  printSummary(sites, reqs);

  const chunks = chunk(validReqs, 50);
  let failed = 0;

  for (const chunk of chunks) {
    try {
      const result = await putMapDataApi(chunk, ctx);
      console.info(`PutMapData Api @requestId=${result.requestId}`);

      if (result.failed.length) {
        failed += result.failed.length;
        console.warn(
          'PutMapData Api reported errors:',
          JSON.stringify(result.failed),
        );
      }
    } catch (e) {
      failed += chunk.length;
      const err = e as AxiosError;
      console.error(
        `PutMapData Api failed due:`,
        err.response?.data || err.response || err,
        err.response?.headers,
        chunk,
      );
    }
  }

  if (failed > 0) {
    console.warn(`Failed to process ${failed} sites.`);
  }
};

function printSummary(
  sites: Array<SemarchySite>,
  reqs: Array<SiteRequest>,
): void {
  const valid = reqs.filter((req) => isValidRequest(req));
  const skipped = reqs.filter((req) => !isValidRequest(req)) as Array<
    PutMapDataUpsertRequest<SiteData>
  >;

  let summary: Record<string, Array<string>> = {};

  summary = valid.reduce((acc, req) => {
    const key = `${req.provider.toUpperCase()} - ${req.operation}`;

    if (!acc[key]) {
      acc[key] = [];
    }

    acc[key].push(req.serial);

    return acc;
  }, summary);

  summary = skipped.reduce((acc, req) => {
    const key = `${req.provider.toUpperCase()} - SKIP`;

    if (!acc[key]) {
      acc[key] = [];
    }

    acc[key].push(req.serial);

    return acc;
  }, summary);

  for (const key of Object.keys(summary).sort()) {
    console.debug(
      `### ${key}: ${summary[key].length} => ${summary[key].sort().join(', ')}`,
    );
  }

  console.debug(`TOTAL SITES: ${sites.length}`);
  console.debug(`TOTAL SKIP: ${skipped.length}`);
  console.debug(
    `TOTAL CREATE: ${
      valid.filter((r) => r.operation === Operation.CREATE).length
    }`,
  );
  console.debug(
    `TOTAL UPDATE: ${
      valid.filter((r) => r.operation === Operation.UPDATE).length
    }`,
  );
  console.debug(
    `TOTAL DELETE: ${
      valid.filter((r) => r.operation === Operation.DELETE).length
    }`,
  );
}
