import { Provider } from '../../shared/elastic-search.types';
import { SiteRequest } from '../../shared/put-map-data-api.types';
import {
  isValidRequest,
  parseRegions,
  reverseToSerial,
  updateRegionCodes,
} from './utils';

describe('getDataSemarchy/utils', () => {
  describe('reverseToSerial()', () => {
    it('should reverse the site id to the provider serial number', () => {
      const result = reverseToSerial({
        _index: 'index',
        _id: 'provider-123',
        _source: {
          provider: 'provider' as Provider,
        },
      });

      expect(result).toBe('123');
    });
  });

  describe('isValidRequest()', () => {
    it('should return true when site location is not 0,0', () => {
      const result = isValidRequest({
        payload: {
          details: {
            location: {
              lat: 1,
              lon: 1,
            },
          },
        },
      } as SiteRequest);

      expect(result).toStrictEqual(true);
    });

    it('should return false when site location is 0,0', () => {
      const result = isValidRequest({
        payload: {
          details: {
            location: {
              lat: 0,
              lon: 0,
            },
          },
        },
      } as SiteRequest);

      expect(result).toStrictEqual(false);
    });
  });

  describe('parseRegions()', () => {
    it('should convert comma separated string of country codes to array of regions', () => {
      const result = parseRegions('EN,DE');
      expect(result).toEqual(['EN', 'DE']);
    });

    it('should convert to uppercase', () => {
      const result = parseRegions('nl,es');
      expect(result).toEqual(['NL', 'ES']);
    });

    it('should trim spaces', () => {
      const result = parseRegions(' EN , ES ');
      expect(result).toEqual(['EN', 'ES']);
    });

    it('should throw if no regions are provided', () => {
      expect(() => parseRegions('')).toThrow('Invalid regions format.');
    });

    it('should throw if region format is invalid', () => {
      expect(() => parseRegions('DEU,AUS')).toThrow('Invalid regions format.');
    });
  });

  describe('updateRegionCodes()', () => {
    it('should replace GB with UK', () => {
      const result = updateRegionCodes(['GB', 'ES']);
      expect(result).toEqual(['UK', 'ES']);
    });
  });
});
