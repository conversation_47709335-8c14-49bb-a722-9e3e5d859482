import { estypes } from '@elastic/elasticsearch';
import {
  Operation,
  PutMapDataUpsertRequest,
  SiteData,
  SiteRequest,
} from '../../shared/put-map-data-api.types';
import { ObjectWithProvider } from './elastic-search';

export function reverseToSerial(site: estypes.Hit<ObjectWithProvider>): string {
  const prefix = `${site._source?.provider}-`;
  return site._id.replace(prefix, '');
}

export function isValidRequest(req: SiteRequest): boolean {
  return req.operation === Operation.DELETE || isValidLocation(req);
}

export function isValidLocation(
  req: PutMapDataUpsertRequest<SiteData>,
): boolean {
  const { lat, lon } = req.payload.details.location;
  return lat !== 0 || lon !== 0;
}

export function parseRegions(regions: string): Array<string> {
  const result = regions
    .split(',')
    .map((region) => region.trim().toUpperCase());

  if (!result.length || result.some((r) => r.length !== 2)) {
    throw new Error('Invalid regions format.');
  }

  return result;
}

export function updateRegionCodes(regions: Array<string>): Array<string> {
  // Required becasue Semarchy stores data as GB, but emsp uses UK,
  // therefore it will never find a matching site as it cannot search for GB
  if (regions.indexOf('GB') !== -1) {
    const index = regions.indexOf('GB');
    regions[index] = 'UK';
  }
  return regions;
}
