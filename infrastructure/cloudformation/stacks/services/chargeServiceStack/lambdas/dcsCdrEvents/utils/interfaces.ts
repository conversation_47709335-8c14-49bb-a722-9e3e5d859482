import { AxiosResponse } from 'axios';

/*Main index interfaces*/
export interface RecordInterfaceI {
  kinesis: KinesisType;
}

export type KinesisType = {
  data: string; //infered, to be verified,
};

export interface CdrEventDataI {
  transactionId: string;
  cdr_filename?: string;
  cdrType: string;
  cdr_reference: string;
  sessionId: string;
  timestamp: TimestampI;
  chargepoint: {
    cpoName?: string;
    providerExternalId: string;
    connector: { connectorExternalId: string };
  };
  chargeDetails: {
    providerChargeIds?: Array<null | { type: string; value: string | null }>;
    tagId: string;
    currency: string;
    chargeGross: string;
    chargeNet: string;
    chargeTax: string;
    chargeTaxPercentage: string;
    totalGross: string;
    energyConsumption: number;
    chargeBaseFee?: string;
    chargeAdditionalFees?: string;
    unitOfMeasure: string;
  };
}

interface TimestampI {
  dateStart: string;
  dateEnd: string;
}

interface SubmitRecordAndUpdateTagStatusI {
  submitRecordStatus: number;
  updateTagResponse?: number;
}

interface RecordHandlerCdrI extends SubmitRecordAndUpdateTagStatusI {
  chargeProcessingStatus: number;
}

interface RecordHandlerCreditI extends SubmitRecordAndUpdateTagStatusI {
  updateHistoryRecordResponse: number;
  refundResult: number;
}

export type SingleRecordHandler =
  | StatusAndMessage
  | RecordHandlerCdrI
  | RecordHandlerCreditI;
/*Main index interfaces*/

/*User interfaces*/
export type TagDataType =
  | {
      tagInfo: TagInfoType;
      sessionType: string;
    }
  | {
      tagInfo?: never;
      sessionType?: never;
    };

export type TagInfoType = {
  //to be checked
  userId: string;
  tagNotes: string;
  tagCardNumber: string;
  country: string;
  tagStatus: string;
};

export interface UpdateTagResponseInterfaceI extends StatusAndMessage {
  /* error handler means message will never be undefined anymore in UpdateTagInternal*/
}

export interface GetUserInfoParamsI {
  userInfo: {
    type: string;
    tagIds: Array<{
      tagCardNumber: string;
      tagId: string;
      tagTypeName: string;
    }>;
  };
}
/*User interfaces*/

/*Charge interfaces*/
export type CatchedChargeI = ChargeCacheData | null;

export interface ChargeCacheData {
  chargeMonitoringPayload: {
    stateOfCharge: null | {
      value: string | null;
      units: string | null;
    }; //schema for this?, value units options are only inferences from what's expected in further functions
    power: null | {
      value: string | null;
      units: string | null;
    };
  };
  eventTime: string; //custom date interface?
}
/*Charge interfaces*/

/*History interfaces*/
export interface NewHistoryRecordI extends StatusAndOptionalMessage {
  chargeSessionId: string | null;
}

export interface MapChargeRecordParametersI {
  userId: string;
  timestamp: TimestampI;
  sessionId: string;
  cdrEventData: CdrEventDataI;
  chargeCacheData: CatchedChargeI;
  chargepointData: ChargeRecordChargepointDataI;
}

export interface MappedHistoryRecordI {
  eventTime: string | undefined;
  userId: string;
  dateStart: string;
  sessionId: string;
  dateEnd: string;
  // sessionId: string,
  /* sessionId to be added when this branch is merged in*/
  chargepoint: MappedHistoryRecordChargepointDataI;
  chargeDetails: {
    providerChargeIds: Array<{
      type: string;
      value: string | null;
    }>;
    tagId: string;
    currency: string;
    /*Can any of these next few also be null?*/
    chargeGross: number;
    chargeNet: number;
    chargeTax: number;
    chargeTaxPercentage: number;
    unitCost: number | null;
    totalGross: number;
    unitOfMeasure: string;
    chargeBaseFee: number;
    chargeAdditionalFees: number | null;
    energyConsumed: {
      value: number | null;
      units: string;
    };
    stateOfCharge: {
      value: number | null;
      units: string | null;
    };
    power: {
      value: number | null;
      units: string | null;
    };
  };
}

interface MappedHistoryRecordChargepointDataI
  extends ChargeRecordChargepointDataI {
  provider: string;
}

interface ChargeRecordChargepointDataI extends ChargePointDataI, EvPricesI {}

export interface HistoryRecordParamI extends MappedHistoryRecordI {
  transactionId: string;
  userId: string;
}
/*History interfaces*/

export interface ChargePointDataI
  extends SiteDetailsI,
    ApolloIds,
    OtherChargePointAttributes {
  //also used in history
  displayAddress: string | null;
  connector: ConnectorIdAndType;
}

/*Map interfaces*/
export type ChargepointI = {} | ChargePointDataI;

export interface ConnectorI extends ConnectorIdAndType {
  //should there be `| null` for all of connectorI's properties, as the graphql request might return null for properties if there's an error
  //(or should I just do what we would usually expect from the normal result of a gql request?)
  chargepoint: ConnectorChargePoint;
}

interface ConnectorIdAndType {
  connectorExternalId: string;
  connectorInternalId: string;
  type: string;
}

interface ConnectorChargePoint extends ApolloIds, OtherChargePointAttributes {
  provider: string;
  site: { siteDetails: SiteDetailsI };
  providerInternalId: string;
}

interface SiteDetailsI {
  address: string;
  country: string;
  city: string;
  postcode: string;
}

interface OtherChargePointAttributes {
  publicName: null | string;
  providerExternalId: string;
}

interface ApolloIds {
  apolloInternalId: string;
  apolloExternalId: string;
}
/*Map interfaces*/

/*Charge processing interfaces*/
export type ChargeProcessing = StatusAndOptionalMessage;

export interface LambdaError {
  errorType: string;
  errorMessage: string;
  trace: Array<string>;
}
/*Charge processing interfaces*/

/*Prices interfaces*/
export interface EvPricesQueryI {
  getEvPrices: [
    {
      chargepointId: string;
      connectors: [
        {
          connectorInterfnalId: string;
          connectorExternalId: string;
          grossUnitCost: number;
          unit: string;
          currency: string;
          blockingFee: {
            price: number;
            duration: number;
          };
        },
      ];
      error: null | string;
    },
  ];
}

export interface EvPricesI {
  currency: StringOrUndefined;
  unit: StringOrUndefined;
  grossUnitCost: Number | undefined;
  blockingFee:
    | { price: string | number; duration: string | number }
    | undefined;
}
/*Prices interfaces*/

/*utility types/interfaces */
export type StringOrUndefined = string | undefined;

export type CaughtError = Error | unknown;

export type EnumsI = { [key: string]: string };

export type standardObject<T = string> = { [key: string]: T };

export { AxiosResponse };

export interface SessionAndPaymentId {
  chargeSessionId: string;
  paymentId: string;
}

export interface StatusAndMessage<T = number> {
  status: T;
  message: string;
}
export interface StatusAndOptionalMessage<T = number> {
  status: T;
  message?: string;
}
