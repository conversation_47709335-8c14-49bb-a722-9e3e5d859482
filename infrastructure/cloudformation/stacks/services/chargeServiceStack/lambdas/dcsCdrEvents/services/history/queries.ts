import { gql } from 'graphql-request';

/**
 * Mutation used for the creation of a history record by using the history micro-service
 * Will return an chargeSessionId
 */
const CREATE_HISTORY_RECORD = gql`
  mutation createHistoryRecord(
    $historyRecord: History_HistoryRecordInput!
    $paymentStatus: PaymentStatus!
    $sessionType: SessionType
    $userId: String
    $cdr_filename: String
    $paymentId: String
    $chargeSessionId: String
    $referenceChargeSessionId: String
    $cdrType: String
    $userType: String
  ) {
    createHistoryRecord(
      historyRecord: $historyRecord
      paymentStatus: $paymentStatus
      sessionType: $sessionType
      userId: $userId
      chargeSessionId: $chargeSessionId
      cdr_filename: $cdr_filename
      paymentId: $paymentId
      referenceChargeSessionId: $referenceChargeSessionId
      cdrType: $cdrType
      userType: $userType
    ) {
      message
      status
      chargeSessionId
    }
  }
`;

const GET_HISTORY_RECORD = gql`
  query getHistoryRecord($userId: String, $sessionId: String) {
    getHistoryRecord(userId: $userId, sessionId: $sessionId) {
      userId
      chargeSessionId
      paymentId
      chargeDetails {
        isStandardInvoice
        chargeGross
      }
    }
  }
`;

const UPDATE_HISTORY_RECORD = gql`
  mutation updateHistoryRecord(
    $chargeSessionId: String!
    $referenceChargeSessionId: String
  ) {
    updateHistoryRecord(
      chargeSessionId: $chargeSessionId
      referenceChargeSessionId: $referenceChargeSessionId
    ) {
      status
      message
    }
  }
`;

export { CREATE_HISTORY_RECORD, GET_HISTORY_RECORD, UPDATE_HISTORY_RECORD };
