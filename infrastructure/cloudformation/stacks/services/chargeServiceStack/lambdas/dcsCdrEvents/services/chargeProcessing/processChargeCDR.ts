import triggerChargeProcessing<PERSON><PERSON>bda from './index';
import { getUserInfoData, submitHistoryRecord } from '../index';
import { CDR_TYPE } from '../../utils/enums';
import { CdrEventDataI, ChargeProcessing } from '../../utils/interfaces';
import { getTransactionByUserAndSession } from '../bppay';

export const processChargeCDR = async ({
  cdrEventData,
  sessionType,
  userId,
}: {
  cdrEventData: CdrEventDataI;
  userId: string;
  sessionType: string;
}) => {
  const { type } = await getUserInfoData({
    userId,
  });

  const getTransactionResult = await getTransactionByUserAndSession(
    userId,
    cdrEventData.timestamp.dateStart,
    cdrEventData.chargepoint.connector.connectorExternalId,
  );

  console.info(`User type is ${type} for userID: ${userId}`);
  //create new history record with the dcs cdr event data
  const recordSubmissionResponse = await submitHistoryRecord({
    cdrEventData: { ...cdrEventData, cdrType: CDR_TYPE.CHARGE },
    sessionType,
    userId,
    userType: type,
    chargeSessionId: getTransactionResult?.chargeSessionId,
  });

  console.info(
    `History record created for DCS with status ${recordSubmissionResponse.status}`,
  );

  const { chargeGross } = cdrEventData.chargeDetails;
  const { chargeSessionId } = recordSubmissionResponse;
  console.info(
    `ChargeSessionId generated for DCS history record: ${chargeSessionId}`,
  );

  const { REGISTERED_CHARGE_PROCESSING_LAMBDA_NAME } = process.env;

  const chargeProcessingResponse: ChargeProcessing = parseFloat(chargeGross)
    ? REGISTERED_CHARGE_PROCESSING_LAMBDA_NAME
      ? await triggerChargeProcessingLambda(
          REGISTERED_CHARGE_PROCESSING_LAMBDA_NAME,
          { chargeSessionId, paymentId: getTransactionResult?.paymentId },
        )
      : { status: 400, message: 'Lambda name not supplied' }
    : { status: 412 };

  return { recordSubmissionResponse, chargeProcessingResponse };
};
