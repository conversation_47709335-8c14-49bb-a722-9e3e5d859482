import { processChargeCDR } from './processChargeCDR';
import { SESSION_TYPE } from '../../utils/enums';
import { CdrEventDataI } from '../../utils/interfaces';
const originalEnv = process.env;

jest.useFakeTimers();
jest.spyOn(global.console, 'log').mockImplementation(jest.fn());

const dcsChargeCdrEvent: CdrEventDataI = {
  transactionId: '66cb39f9-1d1d-4274-99cd-2359794e61a7',
  sessionId: 'AT*HTB:dd1c2f08-ad5e-4f7d-b4e6-1aa3327bd89fe',
  timestamp: {
    dateStart: '2023-02-13T17:20:46.000Z',
    dateEnd: '2023-02-13T17:20:46.000Z',
  },
  chargepoint: {
    cpoName: 'EnBWEnergieBaden-WürttembergAG',
    providerExternalId: 'DEALLEGO0002393',
    connector: { connectorExternalId: 'DEALLEGO0002393' },
  },
  chargeDetails: {
    providerChargeIds: [
      { type: 'sessionId', value: 'dd1c2f08-ad5e-4f7d-b4e6-1aa3327bd89fe' },
      { type: 'transactionId', value: '66cb39f9-1d1d-4274-99cd-2359794e61a7' },
      { type: 'cdrId', value: 'AT*HTB:dd1c2f08-ad5e-4f7d-b4e6-1aa3327bd89fe' },
    ],
    tagId: '8c81e787*57af*4',
    currency: 'EUR',
    chargeGross: '23.**********',
    chargeNet: '19.**********',
    chargeTax: '3.**********',
    chargeTaxPercentage: '19',
    totalGross: '23.**********',
    energyConsumption: 30.031,
    unitOfMeasure: 'kWh',
    chargeBaseFee: '19.**********',
    chargeAdditionalFees: '0.**********',
  },
  cdrType: 'cdr',
  cdr_reference: '',
};

const mockParams = {
  cdrEventData: dcsChargeCdrEvent,
  sessionType: SESSION_TYPE.Mobile,
  userId: '0058E00000AEt9cQAD',
};

const mockSubmitHistoryRecord = jest.fn().mockResolvedValue({
  status: 200,
  message: 'history record created',
  chargeSessionId: 'AMDE-483f6f6f-393b-4354-9909-c9ae503c4d3',
});

const mockGetUserInfo = jest.fn().mockResolvedValue({
  userId: '123',
  type: 'PAYG-Wallet',
});

const mockGetTransactionByUserAndSession = jest.fn();

const mockUserID = 'mock-user-id';

jest.mock('../../services', () => ({
  submitHistoryRecord: jest.fn((...args) => mockSubmitHistoryRecord(...args)),
  getUserInfoData: () => mockGetUserInfo(),
}));

jest.mock('../bppay', () => ({
  getTransactionByUserAndSession: jest.fn((...args) =>
    mockGetTransactionByUserAndSession(...args),
  ),
}));

const mockTriggerChargeProcessingLambda = jest.fn();

jest.mock('./index', () =>
  jest.fn((...args) => mockTriggerChargeProcessingLambda(...args)),
);

const successfulResponse = {
  recordSubmissionResponse: {
    status: 200,
    message: 'history record created',
    chargeSessionId: 'AMDE-483f6f6f-393b-4354-9909-c9ae503c4d3',
  },
  chargeProcessingResponse: {
    status: 200,
    message: 'Registered Charge Processing Completed',
  },
};

describe('Trigger processChargeCDR function', () => {
  beforeEach(() => {
    jest.resetModules();
    jest.clearAllMocks();
    process.env = {
      ...originalEnv,
      REGISTERED_CHARGE_PROCESSING_LAMBDA_NAME: 'mockLambdaName',
    };
  });
  jest.setTimeout(20000);
  describe('Successfully process received Charge CDR', () => {
    it('Record is submitted and processed', async () => {
      mockTriggerChargeProcessingLambda.mockResolvedValue({
        status: 200,
        message: 'Registered Charge Processing Completed',
      });
      const result = await processChargeCDR(mockParams);
      expect(result).toMatchObject(successfulResponse);
    });
  });
  it('should call all dependencies with correct params and return expected result', async () => {
    mockGetUserInfo.mockResolvedValue({
      type: 'REGISTERED',
    });
    mockGetTransactionByUserAndSession.mockResolvedValue({
      chargeSessionId: 'session-123',
      paymentId: 'pay-456',
    });
    mockSubmitHistoryRecord.mockResolvedValue({
      status: 201,
      chargeSessionId: 'session-123',
    });
    mockTriggerChargeProcessingLambda.mockResolvedValue({
      status: 200,
      message: 'ok',
    });

    const result = await processChargeCDR({
      cdrEventData: dcsChargeCdrEvent,
      userId: mockUserID,
      sessionType: SESSION_TYPE.Mobile,
    });

    expect(mockGetTransactionByUserAndSession).toHaveBeenCalledWith(
      mockUserID,
      dcsChargeCdrEvent.timestamp.dateStart,
      dcsChargeCdrEvent.chargepoint.connector.connectorExternalId,
    );
    expect(mockSubmitHistoryRecord).toHaveBeenCalledWith({
      cdrEventData: { ...dcsChargeCdrEvent, cdrType: 'charge' },
      sessionType: SESSION_TYPE.Mobile,
      userId: mockUserID,
      userType: 'REGISTERED',
      chargeSessionId: 'session-123',
    });
    expect(mockTriggerChargeProcessingLambda).toHaveBeenCalledWith(
      'mockLambdaName',
      {
        chargeSessionId: 'session-123',
        paymentId: 'pay-456',
      },
    );
    expect(result).toEqual({
      recordSubmissionResponse: { status: 201, chargeSessionId: 'session-123' },
      chargeProcessingResponse: { status: 200, message: 'ok' },
    });
  });
});

afterEach(() => {
  process.env = originalEnv;
});
