import AWS, { AWSError } from 'aws-sdk';
import { InvocationRequest, InvocationResponse } from 'aws-sdk/clients/lambda';

import axios from 'axios';
import { ChargeProcessing, LambdaError } from '../../utils/interfaces';

AWS.config.logger = console;
const { NODE_ENV } = process.env;

const lambda = new AWS.Lambda({});

const localUrl = 'http://localhost:4040/htb/charge';
const registeredChargeEndpoint = '/registeredCharge';

const lambdaInvoker = async (
  name: string,
  request: { chargeSessionId: string | null },
) => {
  let response: InvocationResponse | AWSError;

  try {
    const params: InvocationRequest = {
      FunctionName: name,
      Payload: JSON.stringify(request),
    };
    response = await lambda.invoke(params).promise();
    return response;
  } catch (excp: any) {
    if (excp.code && excp.message) {
      return {
        status: 500,
        message: `LambdaCallError:'${name}​​':code'${excp.code}​​':message'${excp.message}​​'`,
      };
    }
    return {
      status: 500,
      message: `LambdaCallError:'Unknown \nMore Info: ${excp}`,
    };
  }
};

const processRequest = async (name: string, response: any) => {
  try {
    // we always expect a 200 status if the function managed to start
    if (response.StatusCode !== 200) {
      return {
        status: response.StatusCode || 500,
        message: `LambdaUnexpectedStatus:'${name}​​':message'Lambdareturnedstatus${response.StatusCode}​​'`,
      };
    }

    // presence of this implies a runtime error has occured, timeout, memory, unhandled exception
    if (response.FunctionError) {
      const errorDetails: LambdaError =
        response.Payload && JSON.parse(response.Payload.toString());
      // have to check the error message to see if it is a time out ... not ideal
      if (errorDetails.errorMessage.includes('Task timed out')) {
        return {
          status: 500,
          message: `LambdaTimeout:'${name}​​':message'${errorDetails.errorMessage}​​'`,
        };
      }
      return {
        status: 500,
        message: `LambdaRuntimeError:'${name}​​':message:'${errorDetails.errorMessage}​​'`,
      };
    }

    console.info(
      'Post Processing Successfully Triggered',
      response.StatusCode,
      response.Payload.toString(),
    );

    return {
      status: response.StatusCode,
      message: response.Payload && JSON.parse(response.Payload.toString()),
    };
  } catch (excp: any) {
    // this is to catch any errors in processing the response
    if (excp.code && excp.message) {
      return {
        status: 500,
        message: `LambdaCallError:'${name}​​':code'${excp.code}​​':message'${excp.message}​​'`,
      };
    } else {
      return {
        status: 500,
        message: `LambdaCallError:'Unknown \nMore Info: ${excp}`,
      };
    }
  }
};

export default async (
  name: string,
  request: { chargeSessionId: string | null; paymentId?: string },
): Promise<ChargeProcessing> => {
  try {
    if (NODE_ENV) {
      const response = await lambdaInvoker(name, request);
      return await processRequest(name, response);
    }

    const body = { ...request };

    const axiosResponse = await axios
      .post(`${localUrl}${registeredChargeEndpoint}`, body)
      .catch((error) => {
        console.log(`Error on triggering the lambda with axios: ${error}`);
        throw error;
      });
    return { status: axiosResponse.status, message: axiosResponse.statusText };
  } catch (error: any) {
    if (!error?.message) {
      //makes the error message the original caught error if not originally present
      error.message = error;
      const { message, ...origErr } = error.message;
      error.message = `UnknownError \nMore Info: ${origErr}`;
    }

    return {
      status: 500,
      message: `Error on invoking ${name}: ${error?.message}`,
    };
  }
};
