import { gql } from 'graphql-request';

export const REFUND_ORDER = gql`
  mutation refundPayment(
    $userId: String
    $paymentId: String
    $amount: Float
    $currency: String
  ) {
    refundOrder(
      userId: $userId
      paymentId: $paymentId
      amount: $amount
      currency: $currency
    ) {
      paymentId
      status
    }
  }
`;

export const GET_TRANSACTION_BY_USER_AND_SESSION = gql`
  query getTransactionByUserAndSession(
    $userId: String!
    $chargeSessionStart: String!
    $connectorExternalId: String!
  ) {
    getTransactionByUserAndSession(
      userId: $userId
      chargeSessionStart: $chargeSessionStart
      connectorExternalId: $connectorExternalId
    ) {
      paymentId
      chargeSessionId
    }
  }
`;
