import { request } from 'graphql-request';
import {
  CREATE_HISTORY_RECORD,
  GET_HISTORY_RECORD,
  UPDATE_HISTORY_RECORD,
} from './queries';
import { getEvPrices } from './../prices';
import { getChargepoint } from './../map';
import getCachedCharge from './../charge';

import { mapChargeRecord } from './utils';
import {
  CaughtError,
  CdrEventDataI,
  ChargePointDataI,
  HistoryRecordParamI,
  NewHistoryRecordI,
  StatusAndMessage,
  StringOrUndefined,
} from '../../utils/interfaces';
import { isInterface } from '../../utils/helpers';

const {
  PRIVATE_GATEWAY_SERVER_HTTP = 'http://localhost:4030/graphql',
  APOLLO_INTERNAL_SECRET = 'secret',
} = process.env;

export const updateHistoryRecord = ({
  chargeSessionId,
  referenceChargeSessionId,
}: {
  chargeSessionId: StringOrUndefined;
  referenceChargeSessionId: string | null;
}): Promise<StatusAndMessage> =>
  request(
    PRIVATE_GATEWAY_SERVER_HTTP,
    UPDATE_HISTORY_RECORD,
    {
      chargeSessionId,
      referenceChargeSessionId,
    },
    { 'x-apollo-internal-secret': APOLLO_INTERNAL_SECRET },
  )
    .then((res) => res.updateHistoryRecord)
    .catch((err) => {
      console.error('Error updating history record', err);
      throw err;
    });

export const getHistoryRecord = ({
  sessionId,
  userId,
}: {
  sessionId: string;
  userId: string;
}) =>
  request(
    PRIVATE_GATEWAY_SERVER_HTTP,
    GET_HISTORY_RECORD,
    {
      sessionId,
      userId,
    },
    { 'x-apollo-internal-secret': APOLLO_INTERNAL_SECRET },
  )
    .then(
      (data: {
        getHistoryRecord: {
          userId: string;
          chargeSessionId: string;
          paymentId: string;
          chargeDetails: {
            isStandardInvoice: boolean;
            chargeGross: string;
          };
        };
      }) => data.getHistoryRecord,
    )
    .catch((e) => {
      console.error('Error fetching history record', e);
      throw e;
    });

const createHistoryRecord = async ({
  historyRecord,
  paymentStatus,
  sessionType,
  userId,
  cdr_filename,
  paymentId,
  referenceChargeSessionId,
  cdrType,
  userType,
  chargeSessionId,
}: {
  historyRecord: HistoryRecordParamI;
  paymentStatus: string;
  sessionType: string;
  userId: string;
  cdr_filename: StringOrUndefined;
  paymentId: StringOrUndefined;
  referenceChargeSessionId: StringOrUndefined;
  cdrType: string;
  userType: string;
  chargeSessionId?: string | null;
}): Promise<NewHistoryRecordI> => {
  try {
    const {
      createHistoryRecord: {
        message,
        chargeSessionId: returnedChargeSessionId,
      },
    } = await request(
      PRIVATE_GATEWAY_SERVER_HTTP,
      CREATE_HISTORY_RECORD,
      {
        historyRecord,
        paymentStatus,
        sessionType,
        userId,
        cdr_filename,
        paymentId,
        referenceChargeSessionId,
        cdrType,
        userType,
        chargeSessionId,
      },

      { 'x-apollo-internal-secret': APOLLO_INTERNAL_SECRET },
    );
    if (returnedChargeSessionId) {
      console.log(
        `History record successfully created with charge session ID ${returnedChargeSessionId}`,
      );
      return {
        status: 200,
        message,
        chargeSessionId: returnedChargeSessionId,
      };
    }
    throw new Error(`Error creating history record: ${message}`);
  } catch (error) {
    console.error(
      'Error calling charge history service createHistoryRecord mutation: ',
      { error },
    );
    throw new Error(`
    {
      status: ${500},
      chargeSessionId: ${null},
      message:
        'Error calling charge history service createHistoryRecord mutation',
    }
    `);
  }
};

export const submitHistoryRecord = async (
  {
    cdrEventData,
    userId,
    sessionType,
    paymentId,
    referenceChargeSessionId,
    userType,
    chargeSessionId,
  }: {
    cdrEventData: CdrEventDataI;
    userId: string;
    sessionType: string;
    paymentId?: string;
    referenceChargeSessionId?: string;
    userType: string;
    chargeSessionId?: string | null;
  },
  paymentStatus = 'Processing',
): Promise<NewHistoryRecordI> => {
  try {
    const {
      transactionId,
      sessionId,
      chargepoint: { providerExternalId, connector },
      timestamp,
      cdr_filename,
      cdrType,
    } = cdrEventData;
    const chargepointPayload = {
      providerExternalId,
      connectorId: connector.connectorExternalId,
    };
    console.log('Saving history record...');
    const { connectorId } = chargepointPayload;
    const chargepointData = await getChargepoint(connectorId);

    const chargeCacheData = await getCachedCharge(userId);
    console.log(
      `Charge cache data for userId ${userId}`,
      JSON.stringify(chargeCacheData),
    );

    console.log(
      `Chargepoint data found for connector ID ${connectorId}`,
      JSON.stringify(chargepointData),
    );

    //if no chargepoint data found
    if (isInterface<ChargePointDataI>(chargepointData, 'displayAddress')) {
      //displayAddress is a property, any property of ChargePointDataI would suffice here, in the current state

      const evPrices = await getEvPrices(
        [chargepointData.apolloExternalId],
        userId,
      );

      console.log(
        `EvPrices found for ${chargepointData.apolloExternalId}`,
        JSON.stringify(evPrices),
      );

      console.log(`Mapping the history record...`);

      const mappedRecord = mapChargeRecord({
        userId,
        timestamp,
        sessionId,
        cdrEventData,
        chargeCacheData,
        chargepointData: {
          ...chargepointData,
          currency: evPrices?.currency,
          unit: evPrices?.unit,
          grossUnitCost: evPrices?.grossUnitCost,
          blockingFee: evPrices?.blockingFee,
        },
      });

      console.log(`Creating the history record...`);
      return await createHistoryRecord({
        historyRecord: {
          ...mappedRecord,
          transactionId,
          userId,
        },
        paymentStatus,
        sessionType,
        userId,
        cdr_filename,
        paymentId,
        referenceChargeSessionId,
        cdrType,
        userType,
        chargeSessionId,
      });
    } else {
      throw ReferenceError(
        `ChargePoint data is empty, lacking the sufficient contents to proceed\n\tCANNOT retrieve EV prices\n\tCANNOT create the history record`,
      );
    }
  } catch (error: CaughtError) {
    console.error(
      `Error caught processing history job: userId ${userId}, transactionId ${cdrEventData?.transactionId}, error ${error}`,
    );
    throw new Error(`{ status: ${500}, chargeSessionId: ${null}}`);
  }
};
