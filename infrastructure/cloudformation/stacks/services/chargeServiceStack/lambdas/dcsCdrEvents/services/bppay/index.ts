import { request } from 'graphql-request';
import {
  SessionAndPaymentId,
  StatusAndMessage,
  StringOrUndefined,
} from '../../utils/interfaces';
import { GET_TRANSACTION_BY_USER_AND_SESSION, REFUND_ORDER } from './queries';

const {
  PRIVATE_GATEWAY_SERVER_HTTP = 'http://localhost:4030/graphql',
  APOLLO_INTERNAL_SECRET = 'secret',
} = process.env;

export const refundOrder = async (
  userId: string,
  paymentId: StringOrUndefined,
  amount: number,
  currency: string,
): Promise<StatusAndMessage> =>
  request(
    PRIVATE_GATEWAY_SERVER_HTTP,
    REFUND_ORDER,
    {
      userId,
      paymentId,
      amount,
      currency,
    },
    { 'x-apollo-internal-secret': APOLLO_INTERNAL_SECRET },
  )
    .then((data) => {
      if (data.refundOrder) {
        return {
          message: `Order successfully refunded for paymentId: ${data.refundOrder?.paymentId}`,
          status: 200,
        };
      } else {
        throw Error('Error getting refund order from graphQL Server');
      }
    })
    .catch((e) => {
      return { status: 500, message: `Error refunding order: ${e.message}` };
    });

export const getTransactionByUserAndSession = async (
  userId: string,
  chargeSessionStart: string,
  connectorExternalId: string,
): Promise<SessionAndPaymentId | null> =>
  request(
    PRIVATE_GATEWAY_SERVER_HTTP,
    GET_TRANSACTION_BY_USER_AND_SESSION,
    {
      userId,
      chargeSessionStart,
      connectorExternalId,
    },
    { 'x-apollo-internal-secret': APOLLO_INTERNAL_SECRET },
  )
    .then(({ getTransactionByUserAndSession }) => {
      console.info(
        `getTransactionByUserAndSession - retrieved transaction for userId: ${userId} and session time: ${chargeSessionStart}, for connectorExternalId: ${connectorExternalId},  data: ${JSON.stringify(
          getTransactionByUserAndSession,
        )}`,
      );

      return {
        chargeSessionId: getTransactionByUserAndSession?.chargeSessionId,
        paymentId: getTransactionByUserAndSession?.paymentId,
      };
    })
    .catch((e) => {
      console.error(
        `getTransactionByUserAndSession - getting transaction by userId: ${userId} and session time: ${chargeSessionStart}, for connectorExternalId: ${connectorExternalId}, ERROR: ${e.message}`,
      );
      return null;
    });
