import { v4 } from 'uuid';
import { env } from './env';
import {
  capturePayment,
  createOrder,
  createPaymentRecord,
  makeSimplePayment,
  voidOrder,
} from './services/bppay';
import { getHistoryRecord, updateHistoryRecord } from './services/history';
import {
  getOffersByUser,
  updateOfferCreditBalance,
} from './services/offer/api';
import { blockRfid } from './services/rfid';
import { getTagInfo } from './services/user';
import { getDefaultPaymentMethodId } from './services/wallet';
import { AppType, PaymentStatus, PaymentType } from './types';
import { attempt } from './utils/attempt';
import {
  floatingPoint,
  getAppCountry,
  getAppType,
  getPreAuthAmountMessage,
  retry,
} from './utils/helpers';
import { pushNotifications } from './utils/requests';
import { LOG_PREFIX } from './utils/constants';
import { History_OfferCode } from './services/history/gql-client/graphql';
import { OfferStatus } from './services/offer/gql-client/graphql';
import { PaymentAuthType, UserInfo } from './services/user/gql-client/graphql';

const localNotificationsUrl = '/notifications';
const PHYSICAL_RFID = 'PHYSICAL-RFID';

export const getHistoryRecordData = async (chargeSessionId: string) => {
  const historyRecord = await getHistoryRecord(chargeSessionId);
  const totalGross = historyRecord?.chargeDetails?.totalGross;

  return {
    userId: historyRecord?.userId,
    totalGross:
      typeof totalGross === 'string' ? parseFloat(totalGross) : undefined,
    currency: historyRecord?.chargeDetails?.currency,
    paybackId: historyRecord?.chargeDetails?.payback,
    receivedTagId: historyRecord?.chargeDetails?.tagId ?? '',
    isStandardInvoice: historyRecord?.chargeDetails?.isStandardInvoice,
    homeCountry: historyRecord?.homeCountry,
  };
};

const processBlockRfidWhenValid = async (
  chargeSessionId: string,
  userId: string,
  userInfo: UserInfo | null | undefined,
  receivedTagId: string,
) => {
  if (!userInfo) {
    const errorMessage = 'Missing userInfo, cannot check for physical RFID tag';
    console.error(`${LOG_PREFIX}:${chargeSessionId} - ${errorMessage}`);
    throw new Error(errorMessage);
  }
  const appType = getAppType(chargeSessionId);
  const reasonForBlocking = 'No Credit';
  const appCountry = getAppCountry(chargeSessionId);

  if (appType === AppType.RFID) {
    const tagInfo = getTagInfo(userId, receivedTagId, userInfo);

    if (tagInfo) {
      const cardUid = tagInfo.tagId;
      const cardNumber = tagInfo.tagCardNumber;
      const country = userInfo?.country || appCountry;

      await blockRfid({
        userId,
        reasonForBlocking,
        cardUid,
        cardNumber,
        country,
      });
    }
  } else if (appType === AppType.Mobile) {
    console.log('Checking for physical RFID tag for Mobile charge');
    // 1. Find physical RFID tag
    const physicalRFIDTag = userInfo.tagIds?.find(
      (tag) => tag?.tagNotes?.toUpperCase() === PHYSICAL_RFID,
    );
    const cardStatus = physicalRFIDTag?.tagStatus?.toUpperCase();
    // 2. Block card if found and active
    if (physicalRFIDTag && cardStatus === 'ACTIVE') {
      const cardUid = physicalRFIDTag?.tagId;
      const cardNumber = physicalRFIDTag?.tagCardNumber;
      const country = userInfo?.country || appCountry;

      if (cardUid && cardNumber) {
        console.log(
          `Physical RFID tag found and active, blocking card ${cardNumber}`,
        );
        await blockRfid({
          userId,
          reasonForBlocking,
          cardUid,
          cardNumber,
          country,
        });
      } else {
        console.log(
          'Physical RFID tag found but card number or card uid is missing',
        );
      }
    } else {
      console.log('No physical RFID tag found for Mobile charge');
    }
  }
};

export type ProcessOutstandingPaymentArgs = {
  userId: string;
  chargeSessionId: string;
  receivedTagId: string;
  userInfo: UserInfo | null | undefined;
};

export const processOutstandingPayment = async ({
  chargeSessionId,
  userId,
  userInfo,
  receivedTagId,
}: ProcessOutstandingPaymentArgs) => {
  console.log(`Updating ${userId} rfid card if necessary...`);
  await processBlockRfidWhenValid(
    chargeSessionId,
    userId,
    userInfo,
    receivedTagId,
  );

  //trigger push notifications lambda
  console.info('Triggering Push Notification lambda...');
  const eventBody = {
    body: JSON.stringify({ userId, messageId: 1 }),
  };
  await pushNotifications(
    eventBody,
    env.PUSH_NOTIFICATION_LAMBDA_NAME,
    localNotificationsUrl,
  ).catch((err) => {
    console.error(
      `Trigger to Push Notifications Lambda failed for user id: ${userId}`,
      err,
    );
  });
};

export const applyDiscount = async (
  chargeSessionId: string,
  userId: string,
  country: string,
  totalGross: number,
): Promise<number> => {
  const ENABLE_OFFERS = env.ENABLE_OFFERS.trim().toLowerCase() === 'true';

  if (!ENABLE_OFFERS) {
    console.info('Skipping applyDiscount...');
    return totalGross;
  }

  const allowedCountries = ['UK'];

  try {
    if (country && !allowedCountries.includes(country)) {
      return totalGross;
    }

    const offersResult = await getOffersByUser(userId);
    if (offersResult.error) {
      console.error(
        `[ALARM - ApplyDiscount] Failed to fetch user offers due: ${offersResult.error}`,
      );
      return totalGross;
    }

    const offers = (offersResult.offers ?? []).filter(
      (offer) => offer.creditStatus === OfferStatus.REDEEMED,
    );

    console.debug(`User ${userId} has ${offers.length} offers.`);

    let amount = totalGross;
    const usedOfferCodes: Array<History_OfferCode> = [];

    while (offers.length && amount > 0) {
      const offer = offers.shift();

      if (
        typeof offer?.creditBalance !== 'number' ||
        offer.creditBalance <= 0
      ) {
        console.info(
          `No credit balance available for offer code ${offer?.offerCode}.`,
        );
        continue;
      }

      const usedCredit = Math.min(amount, offer.creditBalance);
      console.info(
        `Using ${usedCredit} ${offer.currency} from offer code ${offer.offerCode}.`,
      );

      const result = await retry(
        () => updateOfferCreditBalance(offer.offerCode, usedCredit),
        3,
      );

      if (result.error) {
        console.error(
          `[ALARM - ApplyDiscount] Failed to update credit balance due: ${result.error}`,
        );
        continue;
      }

      usedOfferCodes.push({
        offerCode: offer.offerCode,
        offerValue: usedCredit,
      });

      amount = floatingPoint(amount - usedCredit);
    }

    if (usedOfferCodes.length > 0) {
      const discount = floatingPoint(totalGross - amount);

      console.info(
        `Updating history record with discount: ${discount} | ${usedOfferCodes
          .map((o) => `${o.offerCode}: ${o.offerValue}`)
          .join(', ')}`,
      );

      const updateHistoryResult = await updateHistoryRecord({
        chargeSessionId,
        discount,
        offerCodes: usedOfferCodes,
      });

      if (updateHistoryResult?.status !== '200') {
        console.error(
          `[ALARM - ApplyDiscount] Failed to update history record with discount due: ${updateHistoryResult?.message}`,
        );
        return totalGross;
      }
    }

    return amount;
  } catch (e) {
    const err = e as Error;
    console.error(
      `[ALARM - ApplyDiscount] Failed to apply discount due: ${err.message}`,
      err,
    );

    return totalGross;
  }
};

type HandlePaymentResponse = {
  status: number;
  paymentType: PaymentType;
  reason?: string;
};
type HandleFullDiscountArgs = {
  appType: AppType;
  currency: string;
  chargeSessionId: string;
  eventPaymentId: string;
  hasPreAuth: boolean;
  userId: string;
  userHomeCountry: string;
};
const handleFullDiscount = async ({
  appType,
  chargeSessionId,
  currency,
  eventPaymentId,
  hasPreAuth = false,
  userId,
  userHomeCountry,
}: HandleFullDiscountArgs) => {
  // Check for RFID session
  let paymentRecordId: string = eventPaymentId;
  if (appType === AppType.RFID) {
    paymentRecordId = v4();
    console.info(
      `${LOG_PREFIX}:${chargeSessionId} - RFID session for fully discounted charge with new paymentRecordId: ${paymentRecordId}`,
    );
  }

  if (hasPreAuth) {
    console.info(`${LOG_PREFIX}:${chargeSessionId} - Voiding pre-auth`);
    await voidOrder([{ paymentId: eventPaymentId, country: userHomeCountry }]);
  }

  const [createPaymentRecordResponse, createPaymentRecordError] = await attempt(
    createPaymentRecord({
      chargeSessionId,
      currency,
      userId,
      paymentId: paymentRecordId,
      // If charge event hasPreAuth, it has been voided in previous step. Hence direct assignment to skipStoringPaymentRecord indicating voided preauth flow.
      // This ensures the payment record is not overwritten.
      skipStoringPaymentRecord: hasPreAuth,
      registered: true,
    }),
  );
  if (
    createPaymentRecordError ||
    !createPaymentRecordResponse.createPaymentRecordInternal
  ) {
    console.error(
      `${LOG_PREFIX}:${chargeSessionId} - Failed to create payment record for userId ${userId} : ${createPaymentRecordError?.message}`,
      createPaymentRecordError,
    );
    return {
      status: 500,
      paymentType: PaymentType.FULL_DISCOUNT,
      reason: createPaymentRecordError?.message,
    };
  }

  if (hasPreAuth) {
    console.info(
      `${LOG_PREFIX}:${chargeSessionId} - createPaymentRecord successful - skipped creating payment record for preAuth flow. ${{
        userId,
      }}.`,
    );
  } else {
    console.info(
      `${LOG_PREFIX}:${chargeSessionId} - Created an empty payment record for userId ${userId}.`,
    );
  }

  return {
    status: 200,
    paymentType: PaymentType.FULL_DISCOUNT,
  };
};

type HandlePreAuthPaymentArgs = {
  amount: number;
  eventPaymentId: string;
  currency: string;
  chargeSessionId: string;
  preAuthAmount: number;
  userId: string;
  userHomeCountry: string;
  userType: string | undefined;
};
const handlePreAuthPayment = async ({
  amount,
  eventPaymentId,
  chargeSessionId,
  currency,
  preAuthAmount,
  userId,
  userHomeCountry,
  userType,
}: HandlePreAuthPaymentArgs) => {
  const [capturePaymentResponse, capturePaymentError] = await attempt(
    capturePayment({
      chargeSessionId,
      paymentId: eventPaymentId,
      amount,
      appCountry: userHomeCountry,
      currency,
      userId,
      userType,
    }),
  );
  if (capturePaymentError || capturePaymentResponse.status !== 200) {
    console.info(`${LOG_PREFIX}:${chargeSessionId} - Voiding pre-auth`);
    await voidOrder([{ paymentId: eventPaymentId, country: userHomeCountry }]);
    console.error(
      `${LOG_PREFIX}:${chargeSessionId} - Pre-auth payment failed for userId ${userId}`,
    );
    return {
      status: 500,
      paymentType: PaymentType.PREAUTH,
      reason: capturePaymentError?.message,
    };
  }

  console.info(
    `${LOG_PREFIX}:${chargeSessionId} - Pre-auth payment captured for userId ${userId}. ${getPreAuthAmountMessage(
      preAuthAmount,
      amount,
    )}`,
  );
  return {
    status: capturePaymentResponse.status,
    paymentType: PaymentType.PREAUTH,
  };
};

type HandleMITPaymentArgs = {
  amount: number;
  eventPaymentId: string;
  chargeSessionId: string;
  currency: string;
  userHomeCountry: string;
  userId: string;
};
const handleMITPayment = async ({
  amount,
  chargeSessionId,
  currency,
  eventPaymentId,
  userHomeCountry,
  userId,
}: HandleMITPaymentArgs) => {
  const [defaultPaymentMethodResponse, defaultPaymentMethodResponseError] =
    await attempt(getDefaultPaymentMethodId(userId));
  if (defaultPaymentMethodResponseError) {
    return {
      status: 500,
      paymentType: PaymentType.MIT,
      reason: defaultPaymentMethodResponseError.message,
    };
  }
  const { paymentMethodId } = defaultPaymentMethodResponse;
  console.info(
    `${LOG_PREFIX}:${chargeSessionId} - Default payment method id for userId ${userId} : ${paymentMethodId}`,
  );

  const [createOrderResponse, createOrderError] = await attempt(
    createOrder({
      userId,
      appCountry: userHomeCountry,
      chargeSessionId,
      paymentId: eventPaymentId,
      registered: true,
    }),
  );
  if (
    createOrderError ||
    !createOrderResponse.correlationId ||
    !createOrderResponse.paymentId
  ) {
    return {
      status: 500,
      paymentType: PaymentType.MIT,
      reason: createOrderError?.message,
    };
  }

  const operationId = v4();
  const { correlationId, paymentId } = createOrderResponse;
  console.info(
    `${LOG_PREFIX}:${chargeSessionId} - Making simple payment for userId ${userId} with paymentId: ${paymentId}, correlationID: ${correlationId} and chargeSessionId: ${chargeSessionId}`,
  );
  const [makeSimplePaymentResponse, makeSimplePaymentError] = await attempt(
    makeSimplePayment({
      operationId,
      chargeSessionId,
      userId,
      paymentId,
      correlationId,
      paymentMethodId,
      amount,
      appCountry: userHomeCountry,
      currency,
    }),
  );
  if (makeSimplePaymentError) {
    console.error(
      `${LOG_PREFIX}:${chargeSessionId} - Make simple payment with correlationId: ${correlationId} failed`,
    );
    return {
      status: 500,
      paymentType: PaymentType.MIT,
      reason: makeSimplePaymentError.message,
    };
  }

  console.info(
    `${LOG_PREFIX}:${chargeSessionId} - Make simple payment for userId ${userId} and correlation id ${correlationId} completed with response: ${makeSimplePaymentResponse.status}`,
  );
  return {
    status: makeSimplePaymentResponse.status,
    paymentType: PaymentType.MIT,
  };
};

type HandlePaymentArgs = {
  appType: AppType;
  hasPreAuth: boolean | undefined;
  eventPaymentId: string;
  chargeSessionId: string;
  userId: string;
  userType: string | undefined;
  amount: number;
  userHomeCountry: string;
  currency: string;
  paymentAuthType: PaymentAuthType | null | undefined;
  preAuthAmount: number | undefined;
  preAuthStatus: boolean | undefined;
  voidTransaction: boolean | undefined;
};
export const handlePayment = async ({
  appType,
  hasPreAuth = false,
  eventPaymentId,
  chargeSessionId,
  userId,
  userType,
  amount,
  userHomeCountry,
  currency,
  paymentAuthType,
  preAuthAmount = 0,
  preAuthStatus = false,
  voidTransaction = false,
}: HandlePaymentArgs): Promise<HandlePaymentResponse> => {
  if (amount === 0) {
    return handleFullDiscount({
      appType,
      chargeSessionId,
      currency,
      eventPaymentId,
      hasPreAuth,
      userId,
      userHomeCountry,
    });
  }

  if (hasPreAuth) {
    return handlePreAuthPayment({
      amount,
      chargeSessionId,
      currency,
      eventPaymentId,
      preAuthAmount,
      userId,
      userHomeCountry,
      userType,
    });
  }

  if (!paymentAuthType || paymentAuthType === PaymentAuthType.PREAUTH) {
    console.error(
      `${LOG_PREFIX}:${chargeSessionId} - Expected ${paymentAuthType} payment but making simple payment instead. User: ${userId}, country: ${userHomeCountry}, appType: ${appType}, preAuthStatus: ${preAuthStatus}, voidTransaction: ${voidTransaction}`,
    );
  }
  return handleMITPayment({
    amount,
    chargeSessionId,
    currency,
    eventPaymentId,
    userHomeCountry,
    userId,
  });
};
