import { graphql } from './gql-client';

export const CAPTURE_PAYMENT = graphql(`
  mutation capturePayment(
    $paymentId: String!
    $amount: Float!
    $currency: String!
    $appCountry: String!
    $userId: String!
    $userType: String
    $chargeSessionId: String!
  ) {
    capturePayment(
      paymentId: $paymentId
      amount: $amount
      currency: $currency
      appCountry: $appCountry
      userId: $userId
      userType: $userType
      chargeSessionId: $chargeSessionId
    )
  }
`);

export const CREATE_ORDER = graphql(`
  mutation createOrder(
    $userId: String
    $appCountry: String
    $paymentId: String
    $chargeSessionId: String
    $registered: Boolean
  ) {
    createOrder(
      userId: $userId
      appCountry: $appCountry
      paymentId: $paymentId
      chargeSessionId: $chargeSessionId
      registered: $registered
    ) {
      paymentId
      correlationId
    }
  }
`);

export const WALLET_MIT_PAYMENT = graphql(`
  mutation makeSimplePayment(
    $userId: String!
    $paymentId: String!
    $chargeSessionId: String
    $correlationId: String!
    $paymentMethodId: String!
    $appCountry: String!
    $amount: Float!
    $currency: String!
    $outstanding: Boolean
    $operationId: String
  ) {
    makeSimplePayment(
      userId: $userId
      paymentId: $paymentId
      chargeSessionId: $chargeSessionId
      correlationId: $correlationId
      paymentMethodId: $paymentMethodId
      appCountry: $appCountry
      amount: $amount
      currency: $currency
      outstanding: $outstanding
      operationId: $operationId
    ) {
      status
    }
  }
`);

export const CREATE_PAYMENT_RECORD = graphql(`
  mutation createPaymentRecordInternal(
    $chargeSessionId: String!
    $amount: Float
    $currency: String!
    $userId: String!
    $paymentId: String!
    $finalPaymentStatus: String
    $skipStoringPaymentRecord: Boolean
    $registered: Boolean
  ) {
    createPaymentRecordInternal(
      chargeSessionId: $chargeSessionId
      amount: $amount
      currency: $currency
      userId: $userId
      paymentId: $paymentId
      finalPaymentStatus: $finalPaymentStatus
      skipStoringPaymentRecord: $skipStoringPaymentRecord
      registered: $registered
    )
  }
`);

export const GET_PAYMENT_RECORD = graphql(`
  query getPaymentRecord($paymentId: String!) {
    getPaymentRecord(paymentId: $paymentId) {
      status
      message
      paymentId
      chargeSessionId
      userId
      preAuthAmount
      preAuthStatus
      transactionId
      transactionNumber
      finalPaymentStatus
      paymentMethodType
      orderStarted
      voidTransaction
    }
  }
`);

export const VOID_ORDER = graphql(`
  mutation voidOrder($paymentData: [PaymentData!]!) {
    voidOrder(paymentData: $paymentData) {
      message
      status
      transactions {
        paymentId
        isVoided
      }
    }
  }
`);
