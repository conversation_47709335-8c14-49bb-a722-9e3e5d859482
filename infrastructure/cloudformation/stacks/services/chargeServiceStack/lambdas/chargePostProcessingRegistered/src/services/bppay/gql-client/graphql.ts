/* eslint-disable */
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
};

export type AuthLookup = {
  __typename?: 'AuthLookup';
  acsTransactionId?: Maybe<Scalars['String']['output']>;
  acsUrl?: Maybe<Scalars['String']['output']>;
  authenticationType?: Maybe<Scalars['String']['output']>;
  cardBrand?: Maybe<Scalars['String']['output']>;
  cavv?: Maybe<Scalars['String']['output']>;
  challengeRequired?: Maybe<Scalars['String']['output']>;
  dsTransactionId?: Maybe<Scalars['String']['output']>;
  eciFlag?: Maybe<Scalars['String']['output']>;
  enrolled?: Maybe<Scalars['String']['output']>;
  errorDescription?: Maybe<Scalars['String']['output']>;
  errorNumber?: Maybe<Scalars['String']['output']>;
  orderId?: Maybe<Scalars['String']['output']>;
  paresStatus?: Maybe<Scalars['String']['output']>;
  payload?: Maybe<Scalars['String']['output']>;
  sdkFlowType?: Maybe<Scalars['String']['output']>;
  signatureVerification?: Maybe<Scalars['String']['output']>;
  threeDSServerTransactionId?: Maybe<Scalars['String']['output']>;
  threeDSVersion?: Maybe<Scalars['String']['output']>;
  transactionId?: Maybe<Scalars['String']['output']>;
};

export type Authenticate = {
  __typename?: 'Authenticate';
  acsTransactionId?: Maybe<Scalars['String']['output']>;
  authenticationType?: Maybe<Scalars['String']['output']>;
  cardBin?: Maybe<Scalars['String']['output']>;
  cardBrand?: Maybe<Scalars['String']['output']>;
  cavv?: Maybe<Scalars['String']['output']>;
  dsTransactionId?: Maybe<Scalars['String']['output']>;
  eciFlag?: Maybe<Scalars['String']['output']>;
  errorDescription?: Maybe<Scalars['String']['output']>;
  errorNumber?: Maybe<Scalars['String']['output']>;
  interactionCounter?: Maybe<Scalars['String']['output']>;
  paresStatus?: Maybe<Scalars['String']['output']>;
  signatureVerification?: Maybe<Scalars['String']['output']>;
  statusReason?: Maybe<Scalars['String']['output']>;
  threeDSServerTransactionId?: Maybe<Scalars['String']['output']>;
  threeDSVersion?: Maybe<Scalars['String']['output']>;
};

export enum AuthenticationIndicatorType {
  ADD_CARD = 'ADD_CARD',
  PREAUTH = 'PREAUTH'
}

export enum BpPayPaymentCountry {
  DE = 'DE',
  ES = 'ES',
  NL = 'NL',
  UK = 'UK'
}

export type CardDetails = {
  __typename?: 'CardDetails';
  cardNumber?: Maybe<Scalars['String']['output']>;
  cardScheme?: Maybe<Scalars['String']['output']>;
  fundingMethod?: Maybe<Scalars['String']['output']>;
};

export enum ChargeStatus {
  started = 'started'
}

export enum DateType {
  ORDER_STARTED = 'ORDER_STARTED',
  SALES_POSTING = 'SALES_POSTING',
  SALES_POSTING_MISSING = 'SALES_POSTING_MISSING'
}

export type DropInSession = {
  __typename?: 'DropInSession';
  dropInType?: Maybe<Scalars['String']['output']>;
  merchant?: Maybe<Scalars['String']['output']>;
  redirectUrl?: Maybe<Scalars['String']['output']>;
  session?: Maybe<Session>;
  sessionType?: Maybe<Scalars['String']['output']>;
};

export type FinalPayment = {
  __typename?: 'FinalPayment';
  amount?: Maybe<Scalars['Float']['output']>;
  currency?: Maybe<Scalars['String']['output']>;
};

export type JourneyDetails = {
  __typename?: 'JourneyDetails';
  dropInSession?: Maybe<DropInSession>;
  journeyId?: Maybe<Scalars['String']['output']>;
};

export type MatchedTransaction = {
  __typename?: 'MatchedTransaction';
  chargeSessionId?: Maybe<Scalars['String']['output']>;
  paymentId?: Maybe<Scalars['String']['output']>;
};

export type Mutation = {
  __typename?: 'Mutation';
  authLookup3DS?: Maybe<AuthLookup>;
  authenticate3DS?: Maybe<Authenticate>;
  authoriseWalletPayment?: Maybe<Scalars['Boolean']['output']>;
  capturePayment?: Maybe<Scalars['Boolean']['output']>;
  createOrder?: Maybe<MutationCreateOrderResult>;
  createPaymentRecordInternal?: Maybe<Scalars['Boolean']['output']>;
  getToken3DS?: Maybe<Scalars['String']['output']>;
  makeSimplePayment?: Maybe<SimplePayment>;
  preAuth?: Maybe<Scalars['Boolean']['output']>;
  refundOrder?: Maybe<RefundOrderResponse>;
  registeredPreAuth?: Maybe<MutationRegisteredPreAuthResult>;
  saveTransactionIdInternal?: Maybe<SaveTransactionIdInternalResponse>;
  startJourney?: Maybe<JourneyDetails>;
  storeOperationId?: Maybe<StoreOperationIdResponse>;
  updateSalesPostingDate?: Maybe<UpdateSalesPostingDateResponse>;
  voidOrder: VoidOrderResult;
};


export type MutationAuthLookup3DsArgs = {
  amount: Scalars['Float']['input'];
  authenticationIndicator?: InputMaybe<AuthenticationIndicatorType>;
  country: Scalars['String']['input'];
  orderId: Scalars['String']['input'];
  paymentMethod: Scalars['String']['input'];
  paymentMethodType: Scalars['String']['input'];
  referenceId: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type MutationAuthenticate3DsArgs = {
  country: Scalars['String']['input'];
  paymentMethod: Scalars['String']['input'];
  paymentMethodType: Scalars['String']['input'];
  transactionId: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type MutationAuthoriseWalletPaymentArgs = {
  amount?: InputMaybe<Scalars['Float']['input']>;
  appCountry?: InputMaybe<Scalars['String']['input']>;
  appType?: InputMaybe<Scalars['String']['input']>;
  currency?: InputMaybe<Scalars['String']['input']>;
  paymentId?: InputMaybe<Scalars['String']['input']>;
  paymentMethodId?: InputMaybe<Scalars['String']['input']>;
  threeDS?: InputMaybe<ThreeDs>;
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCapturePaymentArgs = {
  amount: Scalars['Float']['input'];
  appCountry: Scalars['String']['input'];
  appType?: InputMaybe<Scalars['String']['input']>;
  chargeSessionId: Scalars['String']['input'];
  currency: Scalars['String']['input'];
  paymentId: Scalars['String']['input'];
  userId: Scalars['String']['input'];
  userType?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCreateOrderArgs = {
  appCountry?: InputMaybe<Scalars['String']['input']>;
  appType?: InputMaybe<Scalars['String']['input']>;
  chargeSessionId?: InputMaybe<Scalars['String']['input']>;
  paymentId?: InputMaybe<Scalars['String']['input']>;
  registered?: InputMaybe<Scalars['Boolean']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCreatePaymentRecordInternalArgs = {
  amount?: InputMaybe<Scalars['Float']['input']>;
  chargeSessionId: Scalars['String']['input'];
  currency: Scalars['String']['input'];
  finalPaymentStatus?: InputMaybe<Scalars['String']['input']>;
  paymentId: Scalars['String']['input'];
  registered?: InputMaybe<Scalars['Boolean']['input']>;
  skipStoringPaymentRecord?: InputMaybe<Scalars['Boolean']['input']>;
  userId: Scalars['String']['input'];
};


export type MutationGetToken3DsArgs = {
  amount: Scalars['Float']['input'];
  country: Scalars['String']['input'];
  orderId: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type MutationMakeSimplePaymentArgs = {
  amount: Scalars['Float']['input'];
  appCountry: Scalars['String']['input'];
  chargeSessionId?: InputMaybe<Scalars['String']['input']>;
  correlationId: Scalars['String']['input'];
  currency: Scalars['String']['input'];
  operationId?: InputMaybe<Scalars['String']['input']>;
  outstanding?: InputMaybe<Scalars['Boolean']['input']>;
  paymentId: Scalars['String']['input'];
  paymentMethodId: Scalars['String']['input'];
  threeDS?: InputMaybe<ThreeDs>;
  userId: Scalars['String']['input'];
};


export type MutationPreAuthArgs = {
  token?: InputMaybe<Scalars['String']['input']>;
};


export type MutationRefundOrderArgs = {
  amount?: InputMaybe<Scalars['Float']['input']>;
  currency?: InputMaybe<Scalars['String']['input']>;
  paymentId?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationRegisteredPreAuthArgs = {
  appCountry: BpPayPaymentCountry;
  paymentId: Scalars['String']['input'];
  paymentMethodId: Scalars['String']['input'];
  threeDS: ThreeDs;
  userId: Scalars['String']['input'];
};


export type MutationSaveTransactionIdInternalArgs = {
  authId?: InputMaybe<Scalars['String']['input']>;
  chargeSessionId?: InputMaybe<Scalars['String']['input']>;
  chargeSessionStart?: InputMaybe<Scalars['String']['input']>;
  chargeStatus?: InputMaybe<ChargeStatus>;
  connectorId?: InputMaybe<Scalars['String']['input']>;
  paymentId?: InputMaybe<Scalars['String']['input']>;
  registered?: InputMaybe<Scalars['Boolean']['input']>;
  transactionId?: InputMaybe<Scalars['String']['input']>;
  transactionNumber?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationStartJourneyArgs = {
  appCountry?: InputMaybe<Scalars['String']['input']>;
  appType?: InputMaybe<Scalars['String']['input']>;
  paymentMethodType?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationStoreOperationIdArgs = {
  operationId: Scalars['String']['input'];
  paymentId: Scalars['String']['input'];
};


export type MutationUpdateSalesPostingDateArgs = {
  paymentId: Scalars['String']['input'];
  salesPostingDate: Scalars['Float']['input'];
};


export type MutationVoidOrderArgs = {
  appType?: InputMaybe<Scalars['String']['input']>;
  paymentData: Array<PaymentData>;
};

export type MutationCreateOrderResult = {
  __typename?: 'MutationCreateOrderResult';
  correlationId?: Maybe<Scalars['String']['output']>;
  merchant?: Maybe<Scalars['String']['output']>;
  paymentId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type MutationRegisteredPreAuthResult = {
  __typename?: 'MutationRegisteredPreAuthResult';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type PaymentData = {
  country: Scalars['String']['input'];
  paymentId: Scalars['String']['input'];
};

export type PaymentDetails = {
  __typename?: 'PaymentDetails';
  cardDetails?: Maybe<CardDetails>;
  chargeSessionId?: Maybe<Scalars['String']['output']>;
  finalPayment?: Maybe<FinalPayment>;
  finalPaymentStatus?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  orderStarted?: Maybe<Scalars['Float']['output']>;
  paymentId?: Maybe<Scalars['String']['output']>;
  paymentMethodType?: Maybe<Scalars['String']['output']>;
  preAuthAmount?: Maybe<Scalars['Float']['output']>;
  preAuthRoundedAmount?: Maybe<Scalars['Float']['output']>;
  preAuthStatus?: Maybe<Scalars['Boolean']['output']>;
  refundedDate?: Maybe<Scalars['Float']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  transactionId?: Maybe<Scalars['String']['output']>;
  transactionNumber?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
  voidTransaction?: Maybe<Scalars['Boolean']['output']>;
};

export type Query = {
  __typename?: 'Query';
  getActivePreAuth?: Maybe<QueryGetActivePreAuth>;
  getPaymentRecord?: Maybe<PaymentDetails>;
  getTransactionByUserAndSession?: Maybe<MatchedTransaction>;
  getTransactions?: Maybe<Array<Maybe<Transaction>>>;
  getUserIdFromAuthIdInternal?: Maybe<UserInternal>;
  getUserPaymentDetails?: Maybe<UserPaymentDetails>;
  root?: Maybe<Scalars['String']['output']>;
};


export type QueryGetActivePreAuthArgs = {
  userId: Scalars['String']['input'];
};


export type QueryGetPaymentRecordArgs = {
  paymentId: Scalars['String']['input'];
};


export type QueryGetTransactionByUserAndSessionArgs = {
  chargeSessionStart: Scalars['String']['input'];
  connectorExternalId: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type QueryGetTransactionsArgs = {
  dateType?: InputMaybe<DateType>;
  endDate: Scalars['String']['input'];
  paymentStatus?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  startDate?: InputMaybe<Scalars['String']['input']>;
  transactionType?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetUserIdFromAuthIdInternalArgs = {
  authId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetUserPaymentDetailsArgs = {
  userId: Scalars['String']['input'];
};

export type QueryGetActivePreAuth = {
  __typename?: 'QueryGetActivePreAuth';
  message: Scalars['String']['output'];
  status: Scalars['Int']['output'];
};

export type RefundOrderResponse = {
  __typename?: 'RefundOrderResponse';
  paymentId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type Session = {
  __typename?: 'Session';
  id?: Maybe<Scalars['String']['output']>;
  updateStatus?: Maybe<Scalars['String']['output']>;
  version?: Maybe<Scalars['String']['output']>;
};

export type SimplePayment = {
  __typename?: 'SimplePayment';
  /**
   * An optional argument used to identify the chargeSession payment ID,
   * which can be different to the payment ID sent to dPaaS
   */
  chargeSessionId?: Maybe<Scalars['String']['output']>;
  correlationId?: Maybe<Scalars['String']['output']>;
  /** This is the payment ID sent to dPaaS to create payment orders on their platform */
  paymentId?: Maybe<Scalars['String']['output']>;
  rrn?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type StoreOperationIdResponse = {
  __typename?: 'StoreOperationIdResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type ThreeDs = {
  acsTransactionId?: InputMaybe<Scalars['String']['input']>;
  cavv?: InputMaybe<Scalars['String']['input']>;
  dsTransactionId?: InputMaybe<Scalars['String']['input']>;
  eciFlag?: InputMaybe<Scalars['String']['input']>;
  enrolled?: InputMaybe<Scalars['String']['input']>;
  paresStatus?: InputMaybe<Scalars['String']['input']>;
  statusReason?: InputMaybe<Scalars['String']['input']>;
  threeDSServerTransactionId?: InputMaybe<Scalars['String']['input']>;
  threeDSVersion?: InputMaybe<Scalars['String']['input']>;
};

export type Transaction = {
  __typename?: 'Transaction';
  card_number?: Maybe<Scalars['String']['output']>;
  card_scheme?: Maybe<Scalars['String']['output']>;
  charge_session_id?: Maybe<Scalars['String']['output']>;
  correlation_id?: Maybe<Scalars['String']['output']>;
  country?: Maybe<Scalars['String']['output']>;
  final_payment?: Maybe<FinalPayment>;
  final_payment_status?: Maybe<Scalars['String']['output']>;
  funding_method?: Maybe<Scalars['String']['output']>;
  journey_id?: Maybe<Scalars['String']['output']>;
  merchant_id?: Maybe<Scalars['String']['output']>;
  operationId?: Maybe<Scalars['String']['output']>;
  order_started?: Maybe<Scalars['String']['output']>;
  order_status?: Maybe<Scalars['String']['output']>;
  payment_id?: Maybe<Scalars['String']['output']>;
  payment_method_type?: Maybe<Scalars['String']['output']>;
  preauth?: Maybe<Scalars['Boolean']['output']>;
  reference_charge_session_id?: Maybe<Scalars['String']['output']>;
  refunded_date?: Maybe<Scalars['Float']['output']>;
  retrieval_reference_number?: Maybe<Scalars['String']['output']>;
  sales_posting_date?: Maybe<Scalars['String']['output']>;
  session_id?: Maybe<Scalars['String']['output']>;
  transaction_id?: Maybe<Scalars['String']['output']>;
  transaction_number?: Maybe<Scalars['String']['output']>;
  user_id?: Maybe<Scalars['String']['output']>;
  void_transaction?: Maybe<Scalars['String']['output']>;
};

export type TransactionResult = {
  __typename?: 'TransactionResult';
  isVoided: Scalars['Boolean']['output'];
  paymentId: Scalars['String']['output'];
};

export type UpdateSalesPostingDateResponse = {
  __typename?: 'UpdateSalesPostingDateResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type UserInternal = {
  __typename?: 'UserInternal';
  finalPaymentStatus?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  paymentId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
  voidTransaction?: Maybe<Scalars['Boolean']['output']>;
};

export type UserPaymentDetails = {
  __typename?: 'UserPaymentDetails';
  cardNumber?: Maybe<Scalars['String']['output']>;
  cardScheme?: Maybe<Scalars['String']['output']>;
  chargeSessionId?: Maybe<Scalars['String']['output']>;
  chargeStarted?: Maybe<Scalars['Boolean']['output']>;
  finalPayment?: Maybe<FinalPayment>;
  finalPaymentStatus?: Maybe<Scalars['String']['output']>;
  fundingMethod?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  /** @deprecated Use paymentId instead */
  orderId?: Maybe<Scalars['String']['output']>;
  paymentId?: Maybe<Scalars['String']['output']>;
  paymentMethodType?: Maybe<Scalars['String']['output']>;
  preAuthAmount?: Maybe<Scalars['Float']['output']>;
  preAuthStatus?: Maybe<Scalars['Boolean']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  transactionId?: Maybe<Scalars['String']['output']>;
  transactionNumber?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
  voidTransaction?: Maybe<Scalars['Boolean']['output']>;
};

export type VoidOrderResult = {
  __typename?: 'VoidOrderResult';
  message: Scalars['String']['output'];
  status: Scalars['Int']['output'];
  transactions: Array<TransactionResult>;
};

export type SaveTransactionIdInternalResponse = {
  __typename?: 'saveTransactionIdInternalResponse';
  message?: Maybe<Scalars['String']['output']>;
  paymentId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type CapturePaymentMutationVariables = Exact<{
  paymentId: Scalars['String']['input'];
  amount: Scalars['Float']['input'];
  currency: Scalars['String']['input'];
  appCountry: Scalars['String']['input'];
  userId: Scalars['String']['input'];
  userType?: InputMaybe<Scalars['String']['input']>;
  chargeSessionId: Scalars['String']['input'];
}>;


export type CapturePaymentMutation = { __typename?: 'Mutation', capturePayment?: boolean | null };

export type CreateOrderMutationVariables = Exact<{
  userId?: InputMaybe<Scalars['String']['input']>;
  appCountry?: InputMaybe<Scalars['String']['input']>;
  paymentId?: InputMaybe<Scalars['String']['input']>;
  chargeSessionId?: InputMaybe<Scalars['String']['input']>;
  registered?: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type CreateOrderMutation = { __typename?: 'Mutation', createOrder?: { __typename?: 'MutationCreateOrderResult', paymentId?: string | null, correlationId?: string | null } | null };

export type MakeSimplePaymentMutationVariables = Exact<{
  userId: Scalars['String']['input'];
  paymentId: Scalars['String']['input'];
  chargeSessionId?: InputMaybe<Scalars['String']['input']>;
  correlationId: Scalars['String']['input'];
  paymentMethodId: Scalars['String']['input'];
  appCountry: Scalars['String']['input'];
  amount: Scalars['Float']['input'];
  currency: Scalars['String']['input'];
  outstanding?: InputMaybe<Scalars['Boolean']['input']>;
  operationId?: InputMaybe<Scalars['String']['input']>;
}>;


export type MakeSimplePaymentMutation = { __typename?: 'Mutation', makeSimplePayment?: { __typename?: 'SimplePayment', status?: string | null } | null };

export type CreatePaymentRecordInternalMutationVariables = Exact<{
  chargeSessionId: Scalars['String']['input'];
  amount?: InputMaybe<Scalars['Float']['input']>;
  currency: Scalars['String']['input'];
  userId: Scalars['String']['input'];
  paymentId: Scalars['String']['input'];
  finalPaymentStatus?: InputMaybe<Scalars['String']['input']>;
  skipStoringPaymentRecord?: InputMaybe<Scalars['Boolean']['input']>;
  registered?: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type CreatePaymentRecordInternalMutation = { __typename?: 'Mutation', createPaymentRecordInternal?: boolean | null };

export type GetPaymentRecordQueryVariables = Exact<{
  paymentId: Scalars['String']['input'];
}>;


export type GetPaymentRecordQuery = { __typename?: 'Query', getPaymentRecord?: { __typename?: 'PaymentDetails', status?: string | null, message?: string | null, paymentId?: string | null, chargeSessionId?: string | null, userId?: string | null, preAuthAmount?: number | null, preAuthStatus?: boolean | null, transactionId?: string | null, transactionNumber?: string | null, finalPaymentStatus?: string | null, paymentMethodType?: string | null, orderStarted?: number | null, voidTransaction?: boolean | null } | null };

export type VoidOrderMutationVariables = Exact<{
  paymentData: Array<PaymentData> | PaymentData;
}>;


export type VoidOrderMutation = { __typename?: 'Mutation', voidOrder: { __typename?: 'VoidOrderResult', message: string, status: number, transactions: Array<{ __typename?: 'TransactionResult', paymentId: string, isVoided: boolean }> } };


export const CapturePaymentDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"capturePayment"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"paymentId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"amount"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"currency"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"appCountry"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"userId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"userType"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"chargeSessionId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"capturePayment"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"paymentId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"paymentId"}}},{"kind":"Argument","name":{"kind":"Name","value":"amount"},"value":{"kind":"Variable","name":{"kind":"Name","value":"amount"}}},{"kind":"Argument","name":{"kind":"Name","value":"currency"},"value":{"kind":"Variable","name":{"kind":"Name","value":"currency"}}},{"kind":"Argument","name":{"kind":"Name","value":"appCountry"},"value":{"kind":"Variable","name":{"kind":"Name","value":"appCountry"}}},{"kind":"Argument","name":{"kind":"Name","value":"userId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"userId"}}},{"kind":"Argument","name":{"kind":"Name","value":"userType"},"value":{"kind":"Variable","name":{"kind":"Name","value":"userType"}}},{"kind":"Argument","name":{"kind":"Name","value":"chargeSessionId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"chargeSessionId"}}}]}]}}]} as unknown as DocumentNode<CapturePaymentMutation, CapturePaymentMutationVariables>;
export const CreateOrderDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"createOrder"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"userId"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"appCountry"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"paymentId"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"chargeSessionId"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"registered"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"createOrder"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"userId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"userId"}}},{"kind":"Argument","name":{"kind":"Name","value":"appCountry"},"value":{"kind":"Variable","name":{"kind":"Name","value":"appCountry"}}},{"kind":"Argument","name":{"kind":"Name","value":"paymentId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"paymentId"}}},{"kind":"Argument","name":{"kind":"Name","value":"chargeSessionId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"chargeSessionId"}}},{"kind":"Argument","name":{"kind":"Name","value":"registered"},"value":{"kind":"Variable","name":{"kind":"Name","value":"registered"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"paymentId"}},{"kind":"Field","name":{"kind":"Name","value":"correlationId"}}]}}]}}]} as unknown as DocumentNode<CreateOrderMutation, CreateOrderMutationVariables>;
export const MakeSimplePaymentDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"makeSimplePayment"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"userId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"paymentId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"chargeSessionId"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"correlationId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"paymentMethodId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"appCountry"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"amount"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"currency"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"outstanding"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"operationId"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"makeSimplePayment"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"userId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"userId"}}},{"kind":"Argument","name":{"kind":"Name","value":"paymentId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"paymentId"}}},{"kind":"Argument","name":{"kind":"Name","value":"chargeSessionId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"chargeSessionId"}}},{"kind":"Argument","name":{"kind":"Name","value":"correlationId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"correlationId"}}},{"kind":"Argument","name":{"kind":"Name","value":"paymentMethodId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"paymentMethodId"}}},{"kind":"Argument","name":{"kind":"Name","value":"appCountry"},"value":{"kind":"Variable","name":{"kind":"Name","value":"appCountry"}}},{"kind":"Argument","name":{"kind":"Name","value":"amount"},"value":{"kind":"Variable","name":{"kind":"Name","value":"amount"}}},{"kind":"Argument","name":{"kind":"Name","value":"currency"},"value":{"kind":"Variable","name":{"kind":"Name","value":"currency"}}},{"kind":"Argument","name":{"kind":"Name","value":"outstanding"},"value":{"kind":"Variable","name":{"kind":"Name","value":"outstanding"}}},{"kind":"Argument","name":{"kind":"Name","value":"operationId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"operationId"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"status"}}]}}]}}]} as unknown as DocumentNode<MakeSimplePaymentMutation, MakeSimplePaymentMutationVariables>;
export const CreatePaymentRecordInternalDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"createPaymentRecordInternal"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"chargeSessionId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"amount"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"currency"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"userId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"paymentId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"finalPaymentStatus"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"skipStoringPaymentRecord"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"registered"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"createPaymentRecordInternal"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"chargeSessionId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"chargeSessionId"}}},{"kind":"Argument","name":{"kind":"Name","value":"amount"},"value":{"kind":"Variable","name":{"kind":"Name","value":"amount"}}},{"kind":"Argument","name":{"kind":"Name","value":"currency"},"value":{"kind":"Variable","name":{"kind":"Name","value":"currency"}}},{"kind":"Argument","name":{"kind":"Name","value":"userId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"userId"}}},{"kind":"Argument","name":{"kind":"Name","value":"paymentId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"paymentId"}}},{"kind":"Argument","name":{"kind":"Name","value":"finalPaymentStatus"},"value":{"kind":"Variable","name":{"kind":"Name","value":"finalPaymentStatus"}}},{"kind":"Argument","name":{"kind":"Name","value":"skipStoringPaymentRecord"},"value":{"kind":"Variable","name":{"kind":"Name","value":"skipStoringPaymentRecord"}}},{"kind":"Argument","name":{"kind":"Name","value":"registered"},"value":{"kind":"Variable","name":{"kind":"Name","value":"registered"}}}]}]}}]} as unknown as DocumentNode<CreatePaymentRecordInternalMutation, CreatePaymentRecordInternalMutationVariables>;
export const GetPaymentRecordDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"getPaymentRecord"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"paymentId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getPaymentRecord"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"paymentId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"paymentId"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"message"}},{"kind":"Field","name":{"kind":"Name","value":"paymentId"}},{"kind":"Field","name":{"kind":"Name","value":"chargeSessionId"}},{"kind":"Field","name":{"kind":"Name","value":"userId"}},{"kind":"Field","name":{"kind":"Name","value":"preAuthAmount"}},{"kind":"Field","name":{"kind":"Name","value":"preAuthStatus"}},{"kind":"Field","name":{"kind":"Name","value":"transactionId"}},{"kind":"Field","name":{"kind":"Name","value":"transactionNumber"}},{"kind":"Field","name":{"kind":"Name","value":"finalPaymentStatus"}},{"kind":"Field","name":{"kind":"Name","value":"paymentMethodType"}},{"kind":"Field","name":{"kind":"Name","value":"orderStarted"}},{"kind":"Field","name":{"kind":"Name","value":"voidTransaction"}}]}}]}}]} as unknown as DocumentNode<GetPaymentRecordQuery, GetPaymentRecordQueryVariables>;
export const VoidOrderDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"voidOrder"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"paymentData"}},"type":{"kind":"NonNullType","type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"PaymentData"}}}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"voidOrder"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"paymentData"},"value":{"kind":"Variable","name":{"kind":"Name","value":"paymentData"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"message"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"transactions"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"paymentId"}},{"kind":"Field","name":{"kind":"Name","value":"isVoided"}}]}}]}}]}}]} as unknown as DocumentNode<VoidOrderMutation, VoidOrderMutationVariables>;