import axios from 'axios';
import { request } from 'graphql-request';
import { env } from '../../env';

import {
  GET_PAYMENT_RECORD,
  CAPTURE_PAYMENT,
  CREATE_ORDER,
  CREATE_PAYMENT_RECORD,
  VOID_ORDER,
  WALLET_MIT_PAYMENT,
} from './queries';
import { attempt } from '../../utils/attempt';
import {
  MutationCapturePaymentArgs,
  MutationCreateOrderArgs,
  MutationCreatePaymentRecordInternalArgs,
  MutationMakeSimplePaymentArgs,
  PaymentData,
} from './gql-client/graphql';

axios.defaults.timeout = 10000;

export const capturePayment = ({
  chargeSessionId,
  paymentId,
  amount,
  appCountry,
  currency,
  userId,
  userType,
}: MutationCapturePaymentArgs) =>
  request(
    env.PRIVATE_GATEWAY_SERVER_HTTP,
    CAPTURE_PAYMENT,
    {
      paymentId,
      amount,
      currency,
      appCountry,
      userId,
      userType,
      chargeSessionId,
    },
    { 'x-apollo-internal-secret': env.APOLLO_INTERNAL_SECRET },
  )
    .then(({ capturePayment }) => ({ status: capturePayment ? 200 : 500 }))
    .catch((err) => {
      console.error(`Could not capture order: ${err.message}`);
      throw new Error(`
        {
          status: ${err.response.code},
          message: ${err.message}
        }
      `);
    });

export const createOrder = async (variables: MutationCreateOrderArgs) => {
  return request(env.PRIVATE_GATEWAY_SERVER_HTTP, CREATE_ORDER, variables, {
    'x-apollo-internal-secret': env.APOLLO_INTERNAL_SECRET,
  })
    .then(({ createOrder }) => {
      return {
        correlationId: createOrder?.correlationId,
        paymentId: createOrder?.paymentId,
      };
    })
    .catch((err) => {
      console.error(`Could not create order: ${err.message}`);
      throw new Error(`
      {
        status: ${err.response.code},
        message: ${err.message}
      }
    `);
    });
};

export const createPaymentRecord = async (
  variables: MutationCreatePaymentRecordInternalArgs,
) => {
  return request(
    env.PRIVATE_GATEWAY_SERVER_HTTP,
    CREATE_PAYMENT_RECORD,
    variables,
    {
      'x-apollo-internal-secret': env.APOLLO_INTERNAL_SECRET,
    },
  );
};

export type SimplePaymentResult = {
  status: number;
};

export const makeSimplePayment = ({
  userId,
  paymentId,
  chargeSessionId,
  correlationId,
  paymentMethodId,
  operationId,
  amount,
  currency,
  appCountry,
  outstanding,
}: MutationMakeSimplePaymentArgs) =>
  request(
    env.PRIVATE_GATEWAY_SERVER_HTTP,
    WALLET_MIT_PAYMENT,
    {
      userId,
      paymentId,
      chargeSessionId,
      correlationId,
      paymentMethodId,
      operationId,
      appCountry,
      amount,
      currency,
      outstanding,
    },
    { 'x-apollo-internal-secret': env.APOLLO_INTERNAL_SECRET },
  )
    .then(({ makeSimplePayment }) => ({
      status: makeSimplePayment ? 200 : 500,
    }))
    .catch((err) => {
      console.error(`Could not make simple payment: ${err.message}`);
      throw new Error(`
      {
        status: ${err.status},
        message: ${err.message}
      }
    `);
    });

export interface GraphQLError extends Error {
  response?: {
    code?: number;
  };
  status?: number;
  message: string;
}

export const getPaymentRecord = async (paymentId: string) => {
  try {
    const result = await request(
      env.PRIVATE_GATEWAY_SERVER_HTTP,
      GET_PAYMENT_RECORD,
      { paymentId },
      {
        'x-apollo-internal-secret': env.APOLLO_INTERNAL_SECRET,
      },
    );
    return result.getPaymentRecord;
  } catch (err: unknown) {
    const error = err as GraphQLError;
    console.error(
      `getPaymentRecord status ${
        error?.response?.code ?? 500
      }. paymentId: ${paymentId}. Error: ${error?.message}`,
    );
    throw new Error(`
      {
        status: ${error?.response?.code ?? 500},
        message: ${error?.message}
      }
    `);
  }
};

export const voidOrder = async (paymentData: Array<PaymentData>) => {
  const [result, error] = await attempt(
    request(
      env.PRIVATE_GATEWAY_SERVER_HTTP,
      VOID_ORDER,
      { paymentData },
      {
        'x-apollo-internal-secret': env.APOLLO_INTERNAL_SECRET,
      },
    ),
  );

  if (error) {
    console.error(
      `voidOrder - Failed to void pre-auth, Error: ${error.message}.`,
    );
    return null;
  }

  const voidOrder = result.voidOrder;
  if (voidOrder.status !== 200) {
    console.warn(
      `voidOrder - Failed to void pre-auth, status: ${voidOrder.status}, message: ${voidOrder.message}.`,
    );
  }

  return voidOrder;
};
