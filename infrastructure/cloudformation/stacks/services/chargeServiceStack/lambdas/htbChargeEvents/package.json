{"name": "htb_charge_events", "private": true, "scripts": {"prebuild": "npm run clean", "build": "npm i && tsc --project tsconfig.build.json && cp package.json dist/package.json && cp package-lock.json dist/package-lock.json && cd dist && npm ci --production", "clean": "rm -rf dist/", "start": "ts-node src/run-local.ts", "test": "jest", "test:dev": "jest --watch", "types:check": "tsc --noEmit", "types:generate": "graphql-codegen --config codegen.yml && npm run types:stage", "types:stage": "git add src/services/**/gql-client/*"}, "dependencies": {"axios": "^1.10.0", "dotenv": "^16.4.7", "graphql": "^15.5.3", "graphql-request": "^5.2.0", "ioredis": "^4.17.3", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.17.9", "@babel/preset-env": "^7.16.11", "@babel/preset-typescript": "^7.16.7", "@graphql-codegen/cli": "^5.0.2", "@graphql-codegen/client-preset": "^4.3.3", "@types/aws-lambda": "^8.10.148", "@types/ioredis": "^4.17.3", "@types/jest": "^27.5.2", "@types/node": "^18.19.83", "babel-jest": "^29.7.0", "jest": "^29.0.3", "lambda-local": "^2.2.0", "ts-node": "^10.9.2", "typescript": "^4.9.5"}}