import { KinesisStreamEvent } from 'aws-lambda';
import ioredis from 'ioredis';
import axios from 'axios';
import { handler } from './index';

jest.spyOn(global.console, 'debug').mockImplementation(jest.fn());
jest.spyOn(global.console, 'info').mockImplementation(jest.fn());
jest.spyOn(global.console, 'warn').mockImplementation(jest.fn());
jest.spyOn(global.console, 'error').mockImplementation(jest.fn());

jest.mock('axios');
jest.mock('ioredis');
jest.mock('./env');

const mockRequest = jest.fn();

jest.mock('graphql-request', () => ({
  ...jest.requireActual('graphql-request'),
  request: (...args) => mockRequest(args),
}));

const validStart = {
  Records: [
    {
      kinesis: {
        data: 'ewogICJldmVudERldGFpbHMiOiAiTk9USUZZX1NUQVJUX0NIQVJHRSIsCiAgImV2ZW50VGltZSI6ICIyMDIwLTA3LTE2VDEzOjA1OjI4KzAwOjAwIiwKICAicGF5bG9hZCI6IHsKICAgICJjaGFyZ2Vwb2ludElkIjogImExMTIzYTU0LWVmNDktNGU1Mi05NDY1LTJhYzJkYzEwMGYyZSIsCiAgICAiY29ubmVjdG9ySWQiOiAiYmExM2NkNzQtZjVjNi00OWViLWJjZjMtNzAzZWMzZGI1NjNlIiwKICAgICJzdGF0dXMiOiAiU3VjY2VzcyIsCiAgICAibWVzc2FnZSI6ICJTdGFydCBDaGFyZ2UgQ29uZmlybWVkIiwKICAgICJ0cmFuc2FjdGlvblV1aWQiOiAiMjczNTc4OSIsCiAgICAic2Vzc2lvbklkIjogIjIzNCIKICB9Cn0=',
      },
    },
  ],
} as KinesisStreamEvent;

const validStop = {
  Records: [
    {
      kinesis: {
        data: 'ewogICJldmVudERldGFpbHMiOiAiTk9USUZZX1NUT1BfQ0hBUkdFIiwKICAiZXZlbnRUaW1lIjogIjIwMjAtMDctMTZUMTM6MDU6MjgrMDA6MDAiLAogICJwYXlsb2FkIjogewogICAgImNoYXJnZXBvaW50SWQiOiAiYTExMjNhNTQtZWY0OS00ZTUyLTk0NjUtMmFjMmRjMTAwZjJlIiwKICAgICJjb25uZWN0b3JJZCI6ICJiYTEzY2Q3NC1mNWM2LTQ5ZWItYmNmMy03MDNlYzNkYjU2M2UiLAogICAgInRyYW5zYWN0aW9uVXVpZCI6ICIyNzM1Nzg5IiwKICAgICJzZXNzaW9uSWQiOiAiMjM0IiwKICAgICJzdGF0dXMiOiAiU3VjY2VzcyIsCiAgICAibWVzc2FnZSI6ICJTdG9wIENoYXJnZSBDb25maXJtZWQiCiAgIH0KfQ==',
      },
    },
  ],
} as KinesisStreamEvent;

const noIdValue = {
  Records: [
    {
      kinesis: {
        data: 'ewogIGV2ZW50RGV0YWlsczogJ1NUT1BfQ0hBUkdFX1JFU1BPTlNFJywKICBldmVudFRpbWU6ICcyMDIwLTA3LTE2VDEzOjA1OjI4KzAwOjAwJywKICBjaGFyZ2VQYXlsb2FkOiB7CiAgICBhcG9sbG9JbnRlcm5hbElkOiAnYTExMjNhNTQtZWY0OS00ZTUyLTk0NjUtMmFjMmRjMTAwZjJlJywKICAgIGNvbm5lY3RvckludGVybmFsSWQ6ICdiYTEzY2Q3NC1mNWM2LTQ5ZWItYmNmMy03MDNlYzNkYjU2M2UnLAogICAgcHJvdmlkZXJDaGFyZ2VJZHM6IFsKICAgICAgewogICAgICAgIHR5cGU6ICdzZXNzaW9uSWQnLAogICAgICAgIHZhbHVlOiAnMjM0JywKICAgICAgfSwKICAgIF0sCiAgICBzdGF0dXM6ICdTdWNjZXNzJywKICAgIG1lc3NhZ2U6ICdTdG9wIENoYXJnZSBDb25maXJtZWQnLAogIH0sCn0=',
      },
    },
  ],
} as KinesisStreamEvent;

const invalidData = {
  Records: [
    {
      kinesis: {
        data: 'ewogICAgImV2ZGFpbHMiOiAiU1RBUlRfQ0hBUkdFIFJFU1BPTlNFIiwgCiAgICAiZXZlbnRfdGltZSI6ICIxMToyNzo0MCwgMTIvNS8yMDIwIiwgICAgCiAgICAicG9zdF9zZXJpYWwiIDogIkFCQyIsICAgIAogICAgInBheWxvYWQiOiB7ICAgICAgICAgICAgICAgCiAgICAgICAgInNvYdF9udW1iZXIiOiAxLCAgIC0YWdfY2FyZF9udW1iZXIiOiAxMjMsICAgICAgICAgICAgCiAgICAgICAgInNhbGVzZm9yY2VfSUQiOiAyMzQsCiAgICAgICAgInN0YXR1cyI6ICJTdWNjZXNzIiwKICAgICAgICAic3RhdHVzX21lc3NhZ2UiOiAiQ2hhcmdlIHN0YXJ0ZWQgc3VjY2Vzc2ZCAgIH0Kf=',
      },
    },
  ],
} as KinesisStreamEvent;

const maliciousData = {
  Records: [
    {
      kinesis: {
        data: 'ewogIGV2ZW50RGV0YWlsczogJ1NUT1BfQ0hBUkdFX1JFU1BPTlNFJywKICBldmVudFRpbWU6ICcyMDIwLTA3LTE2VDEzOjA1OjI4KzAwOjAwJywKfQ==',
      },
    },
  ],
} as KinesisStreamEvent;

const expectedStart = {
  eventDetails: 'START_CHARGE_RESPONSE',
  eventTime: '2020-07-16T13:05:28+00:00',
  chargePayload: {
    apolloInternalId: 'hasToBe-a1123a54-ef49-4e52-9465-2ac2dc100f2e',
    connectorInternalId: 'ba13cd74-f5c6-49eb-bcf3-703ec3db563e',
    status: 'Success',
    message: 'Start Charge Confirmed',
    providerChargeIds: [
      {
        type: 'transactionUuid',
        value: '2735789',
      },
      {
        type: 'transactionNumber',
      },
      {
        type: 'sessionId',
        value: '234',
      },
    ],
  },
  userId: '234',
};

const expectedStop = {
  eventDetails: 'STOP_CHARGE_RESPONSE',
  eventTime: '2020-07-16T13:05:28+00:00',
  chargePayload: {
    apolloInternalId: 'hasToBe-a1123a54-ef49-4e52-9465-2ac2dc100f2e',
    connectorInternalId: 'ba13cd74-f5c6-49eb-bcf3-703ec3db563e',
    status: 'Success',
    message: 'Stop Charge Confirmed',
    providerChargeIds: [
      {
        type: 'transactionUuid',
        value: '2735789',
      },
      {
        type: 'transactionNumber',
      },
      {
        type: 'sessionId',
        value: '234',
      },
    ],
  },
  userId: '234',
};
jest.mock('axios', () => ({
  get: jest.fn(),
  post: jest.fn(),
}));
describe('Lambda - charge events', () => {
  beforeEach(() => jest.clearAllMocks());

  it('Should add stop charge to redis with 1 hour 30 exp', async () => {
    (axios.get as jest.Mock).mockResolvedValueOnce({
      data: { userId: '234', registered: true },
    });

    mockRequest.mockResolvedValue({
      connectors: [
        {
          chargepoint: {
            site: {
              siteDetails: {
                country: 'NL',
              },
            },
          },
        },
      ],
    });

    await handler(validStop, {} as any, () => {});

    expect(ioredis.prototype.set).toBeCalledWith(
      '234',
      JSON.stringify(expectedStop),
      'EX',
      360,
    );
  });

  it('Confirmed charge should have no expiry and set charge started state', async () => {
    (axios.get as jest.Mock).mockResolvedValueOnce({
      data: { userId: '234', registered: false },
    });

    mockRequest.mockResolvedValue({
      connectors: [
        {
          chargepoint: {
            site: {
              siteDetails: {
                country: 'NL',
              },
            },
          },
        },
      ],
    });

    await handler(validStart, {} as any, () => {});

    expect(ioredis.prototype.set).toBeCalledWith(
      '234',
      JSON.stringify(expectedStart),
      'EX',
      5400,
    );

    expect(axios.post).toHaveBeenCalledWith(
      'http://localhost:4011/private/storechargestatus',
      { userId: '234', connectorId: 'ba13cd74-f5c6-49eb-bcf3-703ec3db563e' },
    );
  });

  it('should swallow the error if saving the charge started state fails', async () => {
    (axios.get as jest.Mock).mockResolvedValue({
      data: { userId: '234', registered: true },
    });

    (axios.post as jest.Mock).mockRejectedValue(new Error());

    mockRequest.mockResolvedValue({
      connectors: [
        {
          chargepoint: {
            site: {
              siteDetails: {
                country: 'NL',
              },
            },
          },
        },
      ],
    });

    const res = await handler(validStart, {} as any, () => {});

    expect(res).toEqual({
      message: 'Finished processing 1 charge event record(s)',
    });

    expect(console.error).toHaveBeenCalledWith(
      '🚨 Error encountered: saveChargeStatusInDynamo failed, error swallowed, details: ',
      new Error(),
    );
  });

  it('should NOT add to redis', async () => {
    await handler(invalidData, {} as any, () => {});
    expect(ioredis.prototype.set).not.toHaveBeenCalled();
  });

  it('should NOT be called with invalid salesforce_ID', async () => {
    await handler(noIdValue, {} as any, () => {});
    expect(ioredis.prototype.set).not.toHaveBeenCalled();
  });

  it('should have correct result', async () => {
    const result = await handler(validStart, {} as any, () => {});

    expect(result).toStrictEqual({
      message: 'Finished processing 1 charge event record(s)',
    });
  });

  it('should not fail if malicious data sent in', async () => {
    await handler(maliciousData, {} as any, () => {});
    expect(ioredis.prototype.set).not.toHaveBeenCalled();
  });
});
