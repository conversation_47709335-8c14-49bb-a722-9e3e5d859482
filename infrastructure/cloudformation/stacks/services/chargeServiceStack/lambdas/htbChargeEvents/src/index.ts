import { <PERSON><PERSON>, KinesisStreamEvent } from 'aws-lambda';
import { getPaymentData, saveChargeStatusInDynamo } from './bppay';
import * as Redis from './redis';
import { EmspChargeEvent, EmspEventType, mapped } from './mappers';
import { getConnectors } from './services/map';
import { generateChargeSessionId } from './utils/helpers';

const chargeEventMalformedError = 'Charge event malformed';

const getPaymentDataFn = async (
  transactionId: string,
  chargeEvent: EmspChargeEvent,
) => {
  try {
    const connectorId = chargeEvent?.chargePayload?.connectorInternalId;
    const { country } = await getConnectors({
      connectorIds: [connectorId],
    });
    if (!country) {
      console.warn(
        `getPaymentDataFn: no country found, transactionId: ${transactionId}, connectorId: ${connectorId}`,
      );
      return;
    }

    const { userId, registered } = await getPaymentData(transactionId);
    if (!userId) {
      console.warn(
        `getPaymentDataFn: no userId found, transactionId ${transactionId}`,
      );
      return;
    }

    chargeEvent.userId = userId;

    await Redis.setCacheItem(chargeEvent);

    if (chargeEvent.eventDetails === EmspEventType.START_CHARGE_RESPONSE) {
      if (registered) {
        const chargeSessionId = generateChargeSessionId(country);
        await saveChargeStatusInDynamo(userId, connectorId, chargeSessionId);
      } else {
        await saveChargeStatusInDynamo(userId, connectorId);
      }
    }
  } catch (e) {
    const err = e as Error;

    console.error(
      `Failed request to dynamo for ${transactionId}, details: ${err.message}`,
      err,
    );
  }
};

const recordPromisesFn = (event: KinesisStreamEvent) => {
  return event.Records.map(async (record) => {
    let emspChargeEvent: EmspChargeEvent | null;
    let transactionId: string | undefined;

    try {
      const data = Buffer.from(record.kinesis.data, 'base64').toString('utf8');
      const htbChargeEvent = JSON.parse(data);
      console.debug('Processing HTB charge event:', htbChargeEvent);

      emspChargeEvent = mapped(htbChargeEvent);

      if (!emspChargeEvent?.chargePayload) {
        throw new Error(chargeEventMalformedError);
      }

      transactionId = emspChargeEvent.chargePayload.providerChargeIds.find(
        (id) => id.type === 'transactionUuid',
      )?.value;

      console.debug(
        `Successfully parsed request for transactionId: ${transactionId}`,
      );
    } catch (e) {
      // Handle parsing errors here
      console.error(`Lambda parse error: ${e}`, e);
      throw new Error(chargeEventMalformedError);
    }

    const isStartOrStopChargeEvent =
      emspChargeEvent.eventDetails === EmspEventType.START_CHARGE_RESPONSE ||
      emspChargeEvent.eventDetails === EmspEventType.STOP_CHARGE_RESPONSE;

    const isSuccess = emspChargeEvent.chargePayload.status === 'Success';

    if (!isStartOrStopChargeEvent || !isSuccess || !transactionId) {
      return;
    }

    await getPaymentDataFn(transactionId, emspChargeEvent);
  });
};

// Event handler for charging events to be processed
export const handler: Handler<KinesisStreamEvent> = async (event) => {
  // Process stream records
  const recordPromises = recordPromisesFn(event);

  // Process all records
  try {
    await Promise.all(recordPromises);

    const message = `Finished processing ${recordPromises.length} charge event record(s)`;
    console.info(message);

    return { message };
  } catch (e) {
    const err = e as Error;
    console.error(
      `error happend during Promise All with event record, details: ${err.message}`,
      err,
    );

    if (err.message !== chargeEventMalformedError) {
      throw err;
    }
  } finally {
    await Redis.quit();
  }
};
