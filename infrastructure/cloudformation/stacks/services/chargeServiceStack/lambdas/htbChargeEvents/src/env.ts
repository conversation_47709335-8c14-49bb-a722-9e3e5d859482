import dotenv from 'dotenv';

dotenv.config({ path: `${__dirname}/../.env` });

const requiredEnvVars = [
  'APOLLO_INTERNAL_SECRET',
  'ELASTICACHE_HOST',
  'ELASTICACHE_PORT',
  'PRIVATE_BPPAY_SERVER_HTTP',
  'PRIVATE_GATEWAY_SERVER_HTTP',
];

const missingVars = requiredEnvVars.filter(
  (variable) => process.env[variable] === undefined,
);

if (missingVars.length > 0) {
  console.error('Aborting environment variables missing: ', missingVars);
  if (process.env.NODE_ENV !== 'test') {
    process.exit(-1);
  }
}

const {
  APOLLO_INTERNAL_SECRET,
  NODE_ENV,
  ELASTICACHE_HOST,
  ELASTICACHE_PORT,
  PRIVATE_BPPAY_SERVER_HTTP,
  PRIVATE_GATEWAY_SERVER_HTTP,
} = process.env;

const OPTIONAL_ENV_VARS = {
  NODE_ENV,
  ELASTICACHE_HOST,
  ELASTICACHE_PORT,
  PRIVATE_BPPAY_SERVER_HTTP,
};

const REQUIRED_ENV_VARS = {
  APOLLO_INTERNAL_SECRET,
  ELASTICACHE_HOST,
  ELASTICACHE_PORT,
  PRIVATE_BPPAY_SERVER_HTTP,
  PRIVATE_GATEWAY_SERVER_HTTP,
};

export type EnvVars = Record<keyof typeof REQUIRED_ENV_VARS, string> &
  Record<keyof typeof OPTIONAL_ENV_VARS, string | undefined>;

export const env = { ...OPTIONAL_ENV_VARS, ...REQUIRED_ENV_VARS } as EnvVars;
