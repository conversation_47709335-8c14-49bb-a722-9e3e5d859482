import { getConnectors } from './index';

const mockRequest = jest.fn();

jest.mock('graphql-request', () => ({
  ...jest.requireActual('graphql-request'),
  request: (...args) => mockRequest(args),
}));

jest.mock('../../env');

jest.spyOn(global.console, 'error').mockImplementation(jest.fn());

describe('MAP service', () => {
  const connectorId = 'cd5a081b-c83e-4eff-92e0-4b9d3c6826a3';
  const country = 'NL';

  describe('getConnectors', () => {
    it('successfully gets connectors and returns data', async () => {
      mockRequest.mockResolvedValue({
        connectors: [
          {
            chargepoint: {
              site: {
                siteDetails: {
                  country,
                },
              },
            },
          },
        ],
      });
      const result = await getConnectors({ connectorIds: [connectorId] });
      expect(result).toStrictEqual({
        country,
      });
    });

    it('should catch and return status object', async () => {
      const error = { response: { code: 500 }, message: 'Failed' };
      mockRequest.mockRejectedValue(error);
      await expect(getConnectors({ connectorIds: [connectorId] })).rejects
        .toThrow(`
      {
        status: 500,
        message: Failed
      }`);
      expect(global.console.error).toHaveBeenLastCalledWith(
        `getConnectors status 500. connectorIds: ${connectorId}. Error: Failed`,
      );
    });
  });
});
