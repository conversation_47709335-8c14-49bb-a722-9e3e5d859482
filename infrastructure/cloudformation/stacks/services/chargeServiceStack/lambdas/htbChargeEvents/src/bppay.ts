import axios, { AxiosError } from 'axios';
import { env } from './env';
import { PaymentDataResponse } from './interfaces';

const PRIVATE_BPPAY_SERVER_HTTP = env.PRIVATE_BPPAY_SERVER_HTTP;

export const getPaymentData = async (
  transactionId: string,
): Promise<PaymentDataResponse> => {
  try {
    const res = await axios.get<PaymentDataResponse>(
      `${PRIVATE_BPPAY_SERVER_HTTP}/paymentdata/${transactionId}`,
    );

    const { userId, registered } = res.data;

    console.info(
      `getPaymentData, status: ${res.status}, userId: ${userId}, transactionId: ${transactionId}, registered: ${registered}`,
    );

    return {
      userId,
      registered,
    };
  } catch (e) {
    const err = e as AxiosError;

    console.error(
      `getPaymentData, status ${
        err.response?.status ?? 500
      }, transactionId: ${transactionId}, Error: ${err.message}`,
      err,
    );

    throw e;
  }
};

export const saveChargeStatusInDynamo = async (
  userId: string,
  connectorId: string,
  chargeSessionId?: string,
) => {
  try {
    const res = await axios.post(
      `${PRIVATE_BPPAY_SERVER_HTTP}/storechargestatus`,
      {
        userId,
        connectorId,
        chargeSessionId,
      },
    );

    return res.data;
  } catch (e) {
    console.error(
      `🚨 Error encountered: saveChargeStatusInDynamo failed, error swallowed, details: `,
      e,
    );
  }
};
