import { request } from 'graphql-request';
import { env } from '../../env';

import { GET_CONNECTORS } from './queries';

import { QueryConnectorsArgs } from './gql-client/graphql';

export interface GraphQLError extends Error {
  response?: {
    code?: number;
  };
  status?: number;
  message: string;
}

export const getConnectors = async ({ connectorIds }: QueryConnectorsArgs) =>
  request(
    env.PRIVATE_GATEWAY_SERVER_HTTP,
    GET_CONNECTORS,
    {
      connectorIds,
    },
    { 'x-apollo-internal-secret': env.APOLLO_INTERNAL_SECRET },
  )
    .then(({ connectors }) => {
      return {
        country: connectors[0]?.chargepoint?.site?.siteDetails?.country,
      };
    })
    .catch((error: GraphQLError) => {
      console.error(
        `getConnectors status ${
          error?.response?.code ?? 500
        }. connectorIds: ${connectorIds}. Error: ${error?.message}`,
      );
      throw new Error(`
      {
        status: ${error?.response?.code ?? 500},
        message: ${error?.message}
      }
    `);
    });
