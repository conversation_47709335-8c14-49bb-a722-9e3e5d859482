import { v4 as uuidv4 } from 'uuid';
import { generateChargeSessionId } from './helpers';

jest.mock('uuid');

describe('generateChargeSessionId', () => {
  it('should generate a charge session ID with the correct prefix and country code', async () => {
    (uuidv4 as jest.Mock).mockReturnValue(
      'fc5cffb5-a626-41b7-9d23-5393ff36515',
    );

    const country = 'NL';
    const result = generateChargeSessionId(country);

    expect(result).toBe('AMNL-fc5cffb5-a626-41b7-9d23-5393ff3651');
  });
});
