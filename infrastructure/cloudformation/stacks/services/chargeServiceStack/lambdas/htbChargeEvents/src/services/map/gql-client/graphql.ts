/* eslint-disable */
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
};

export enum AvailabilityState {
  Available = 'Available',
  Charging = 'Charging',
  Occupied = 'Occupied',
  Reserved = 'Reserved',
  Unavailable = 'Unavailable',
  Unknown = 'Unknown'
}

export type Chargepoint = {
  __typename?: 'Chargepoint';
  apolloExternalId: Scalars['String']['output'];
  apolloInternalId: Scalars['String']['output'];
  availability?: Maybe<ChargepointAvailability>;
  chargepointNumber?: Maybe<Scalars['Int']['output']>;
  connectorFormat: ConnectorFormat;
  connectors: Array<Connector>;
  costOfCharge: Array<CostOfCharge>;
  free: Scalars['Boolean']['output'];
  hasConnectorsAvailable?: Maybe<Scalars['Boolean']['output']>;
  /** @deprecated Field no longer supported */
  hasConnetorsAvailable?: Maybe<Scalars['Boolean']['output']>;
  hidden?: Maybe<Scalars['Boolean']['output']>;
  hours: Hours;
  lastCharge?: Maybe<Scalars['String']['output']>;
  lastLogin?: Maybe<Scalars['String']['output']>;
  lastUpdated: Scalars['String']['output'];
  lastUsed?: Maybe<Scalars['String']['output']>;
  location: GeoPoint;
  overstayFee?: Maybe<Scalars['Float']['output']>;
  owningProvider: Provider;
  private: Scalars['Boolean']['output'];
  provider: Provider;
  providerExternalId: Scalars['String']['output'];
  providerInternalId: Scalars['String']['output'];
  publicName?: Maybe<Scalars['String']['output']>;
  schemes: Array<ChargepointScheme>;
  site?: Maybe<Site>;
  siteId: Scalars['String']['output'];
};

export type ChargepointAvailability = {
  __typename?: 'ChargepointAvailability';
  apolloInternalId: Scalars['String']['output'];
  available: AvailabilityState;
  connectors: Array<ConnectorAvailability>;
  lastUsed?: Maybe<Scalars['String']['output']>;
  provider: Provider;
};

export type ChargepointScheme = {
  __typename?: 'ChargepointScheme';
  schemeId: Scalars['Int']['output'];
  schemeName: Scalars['String']['output'];
};

export enum ChargingSpeed {
  FAST = 'FAST',
  RAPID = 'RAPID',
  SLOW = 'SLOW',
  ULTRA_RAPID = 'ULTRA_RAPID',
  VERY_FAST = 'VERY_FAST'
}

export type Connector = {
  __typename?: 'Connector';
  availability?: Maybe<ConnectorAvailability>;
  chargepoint?: Maybe<Chargepoint>;
  connectorExternalId: Scalars['String']['output'];
  connectorInternalId: Scalars['String']['output'];
  connectorNumber?: Maybe<Scalars['Int']['output']>;
  phase?: Maybe<Scalars['String']['output']>;
  preAuth?: Maybe<PreAuth>;
  provider: Provider;
  rating: Scalars['Float']['output'];
  type: ConnectorType;
};

export type ConnectorAvailability = {
  __typename?: 'ConnectorAvailability';
  connectorExternalId: Scalars['String']['output'];
  connectorInternalId: Scalars['String']['output'];
  lastUpdate?: Maybe<Scalars['String']['output']>;
  state: AvailabilityState;
};

export enum ConnectorFormat {
  CABLE = 'CABLE',
  SOCKET = 'SOCKET',
  UNKNOWN = 'UNKNOWN'
}

export enum ConnectorType {
  CCS1 = 'CCS1',
  CCS2 = 'CCS2',
  CHADEMO = 'CHADEMO',
  COMMANDO = 'COMMANDO',
  DOMESTIC_E = 'DOMESTIC_E',
  DOMESTIC_G = 'DOMESTIC_G',
  DOMESTIC_J = 'DOMESTIC_J',
  SCHUKO = 'SCHUKO',
  TESLA = 'TESLA',
  TYPE_1 = 'TYPE_1',
  TYPE_2 = 'TYPE_2',
  TYPE_3A = 'TYPE_3A',
  TYPE_3C = 'TYPE_3C',
  UNKNOWN = 'UNKNOWN'
}

export type CostOfCharge = {
  __typename?: 'CostOfCharge';
  minimumCharge: CostOfChargeMinimum;
  tariff: CostOfChargeTariff;
};

export type CostOfChargeMinimum = {
  __typename?: 'CostOfChargeMinimum';
  cost: Scalars['String']['output'];
  type: TariffType;
  unit: Currency;
};

export type CostOfChargeTariff = {
  __typename?: 'CostOfChargeTariff';
  cost: Scalars['String']['output'];
  multiplier: Scalars['String']['output'];
  type: TariffType;
  unit: Currency;
};

export enum Country {
  AT = 'AT',
  BE = 'BE',
  BG = 'BG',
  CH = 'CH',
  CZ = 'CZ',
  DE = 'DE',
  DK = 'DK',
  EE = 'EE',
  ES = 'ES',
  FR = 'FR',
  HR = 'HR',
  HU = 'HU',
  IE = 'IE',
  IT = 'IT',
  LT = 'LT',
  LU = 'LU',
  LV = 'LV',
  NL = 'NL',
  NO = 'NO',
  PL = 'PL',
  RO = 'RO',
  SE = 'SE',
  SI = 'SI',
  SK = 'SK',
  UK = 'UK',
  US = 'US'
}

export enum CrossAcceptanceBrand {
  AN = 'AN',
  AP = 'AP',
  AR = 'AR',
  AS = 'AS',
  BP = 'BP',
  CK = 'CK',
  DH = 'DH',
  ES = 'ES',
  OM = 'OM',
  OT = 'OT',
  TO = 'TO',
  WE = 'WE',
  WP = 'WP'
}

export enum Currency {
  EUR = 'EUR',
  GBP = 'GBP',
  USD = 'USD'
}

export type DataResult = {
  __typename?: 'DataResult';
  error?: Maybe<Scalars['String']['output']>;
  items: Array<Site>;
  searchAfter?: Maybe<Scalars['String']['output']>;
  total: Scalars['Int']['output'];
};

export type EvAvailability = {
  __typename?: 'EvAvailability';
  availableChargepointCount?: Maybe<Scalars['Int']['output']>;
  availableConnectorCount?: Maybe<Scalars['Int']['output']>;
};

export type EvDetails = {
  __typename?: 'EvDetails';
  chargepointCount: Scalars['Int']['output'];
  connectorCount: Scalars['Int']['output'];
  hasRoyalMailScheme: Scalars['Boolean']['output'];
  hasUberScheme: Scalars['Boolean']['output'];
  maximumSpeed?: Maybe<Scalars['Float']['output']>;
  schemeIds: Array<Scalars['Int']['output']>;
  schemes: Array<ChargepointScheme>;
};

export enum FuelType {
  AD_BLUE = 'AD_BLUE',
  DIESEL = 'DIESEL',
  ERDGAS = 'ERDGAS',
  LKW_DIESEL = 'LKW_DIESEL',
  LPG = 'LPG',
  SUPER_E5 = 'SUPER_E5',
  SUPER_E10 = 'SUPER_E10',
  SUPER_PLUS = 'SUPER_PLUS',
  ULTIMATE_102 = 'ULTIMATE_102',
  ULTIMATE_DIESEL = 'ULTIMATE_DIESEL'
}

export type GeoPoint = {
  __typename?: 'GeoPoint';
  lat: Scalars['Float']['output'];
  lon: Scalars['Float']['output'];
};

export type GeoPointInput = {
  lat: Scalars['Float']['input'];
  lon: Scalars['Float']['input'];
};

export type Hours = {
  __typename?: 'Hours';
  fri1?: Maybe<Scalars['String']['output']>;
  fri2?: Maybe<Scalars['String']['output']>;
  mon1?: Maybe<Scalars['String']['output']>;
  mon2?: Maybe<Scalars['String']['output']>;
  sat1?: Maybe<Scalars['String']['output']>;
  sat2?: Maybe<Scalars['String']['output']>;
  sun1?: Maybe<Scalars['String']['output']>;
  sun2?: Maybe<Scalars['String']['output']>;
  thu1?: Maybe<Scalars['String']['output']>;
  thu2?: Maybe<Scalars['String']['output']>;
  tue1?: Maybe<Scalars['String']['output']>;
  tue2?: Maybe<Scalars['String']['output']>;
  wed1?: Maybe<Scalars['String']['output']>;
  wed2?: Maybe<Scalars['String']['output']>;
};

export enum IntersectionType {
  EXCLUDE = 'EXCLUDE',
  INCLUDE = 'INCLUDE',
  ONLY = 'ONLY'
}

export type Marker = MarkerCluster | MarkerPoint;

export type MarkerCluster = {
  __typename?: 'MarkerCluster';
  city?: Maybe<Scalars['String']['output']>;
  count: Scalars['Float']['output'];
  country?: Maybe<Scalars['String']['output']>;
  id: Scalars['Float']['output'];
  location: GeoPoint;
  siteIds: Array<Scalars['String']['output']>;
  siteLocations: Array<GeoPoint>;
};

export type MarkerPoint = {
  __typename?: 'MarkerPoint';
  location: GeoPoint;
  site: MarkerPointSiteInfo;
};

export type MarkerPointSiteInfo = {
  __typename?: 'MarkerPointSiteInfo';
  address?: Maybe<Scalars['String']['output']>;
  brandName?: Maybe<CrossAcceptanceBrand>;
  city?: Maybe<Scalars['String']['output']>;
  country: Scalars['String']['output'];
  cpo?: Maybe<Scalars['String']['output']>;
  evAvailability: EvAvailability;
  evDetails: EvDetails;
  hasEvCharging: Scalars['Boolean']['output'];
  hasFuel: Scalars['Boolean']['output'];
  isAds: Scalars['Boolean']['output'];
  location: GeoPoint;
  provider: Provider;
  siteId: Scalars['String']['output'];
};

export enum MarkerType {
  MarkerCluster = 'MarkerCluster',
  MarkerPoint = 'MarkerPoint'
}

export type OperatorCount = {
  __typename?: 'OperatorCount';
  count: Scalars['Int']['output'];
  name: Scalars['String']['output'];
};

export enum PowerType {
  EV = 'EV',
  fuel = 'fuel'
}

export type PreAuth = {
  __typename?: 'PreAuth';
  amount: Scalars['Float']['output'];
  currency: Currency;
};

export enum Provider {
  BPCM = 'BPCM',
  DCS = 'DCS',
  EVC = 'EVC',
  USOCPI = 'USOCPI',
  aral = 'aral',
  fleet = 'fleet',
  hasToBe = 'hasToBe',
  semarchy = 'semarchy'
}

export type Query = {
  __typename?: 'Query';
  chargepoints: Array<Chargepoint>;
  connectors: Array<Connector>;
  data: DataResult;
  markers: QueryMarkersResponse;
  operators: Array<OperatorCount>;
  search: Array<SearchResultItem>;
  sites?: Maybe<Array<Site>>;
};


export type QueryChargepointsArgs = {
  chargepointIds: Array<Scalars['String']['input']>;
};


export type QueryConnectorsArgs = {
  connectorIds: Array<Scalars['String']['input']>;
};


export type QueryDataArgs = {
  countries?: InputMaybe<Array<Country>>;
  powerTypes?: InputMaybe<Array<PowerType>>;
  providers?: InputMaybe<Array<Provider>>;
  searchAfter?: InputMaybe<Scalars['String']['input']>;
  size?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryMarkersArgs = {
  adsFilter?: InputMaybe<IntersectionType>;
  chargingSpeeds?: InputMaybe<Array<ChargingSpeed>>;
  connectorTypes?: InputMaybe<Array<ConnectorType>>;
  countries?: InputMaybe<Array<Country>>;
  cpo?: InputMaybe<Array<Scalars['String']['input']>>;
  entitledSchemes?: InputMaybe<Array<Scalars['Int']['input']>>;
  fuels?: InputMaybe<Array<FuelType>>;
  geoHashes: Array<Scalars['String']['input']>;
  isOpen24Hours?: InputMaybe<Scalars['Boolean']['input']>;
  maximumPrice?: InputMaybe<Scalars['Float']['input']>;
  minimumSpeed?: InputMaybe<Scalars['Int']['input']>;
  powerTypes?: InputMaybe<Array<PowerType>>;
  providers?: InputMaybe<Array<Provider>>;
  royalMailFilter?: InputMaybe<IntersectionType>;
  services?: InputMaybe<Array<Service>>;
  siteIds?: InputMaybe<Array<Scalars['String']['input']>>;
  uberFilter?: InputMaybe<IntersectionType>;
  zoom: Scalars['Float']['input'];
};


export type QueryOperatorsArgs = {
  countries?: InputMaybe<Array<Country>>;
};


export type QuerySearchArgs = {
  countries?: InputMaybe<Array<Country>>;
  input: Scalars['String']['input'];
  location?: InputMaybe<GeoPointInput>;
  providers?: InputMaybe<Array<Provider>>;
};


export type QuerySitesArgs = {
  adsFilter?: InputMaybe<IntersectionType>;
  chargingSpeeds?: InputMaybe<Array<ChargingSpeed>>;
  connectorTypes?: InputMaybe<Array<ConnectorType>>;
  countries?: InputMaybe<Array<Country>>;
  cpo?: InputMaybe<Array<Scalars['String']['input']>>;
  entitledSchemes?: InputMaybe<Array<Scalars['Int']['input']>>;
  from?: InputMaybe<Scalars['Int']['input']>;
  fuels?: InputMaybe<Array<FuelType>>;
  geoHashes?: InputMaybe<Array<Scalars['String']['input']>>;
  isOpen24Hours?: InputMaybe<Scalars['Boolean']['input']>;
  maximumPrice?: InputMaybe<Scalars['Float']['input']>;
  minimumSpeed?: InputMaybe<Scalars['Int']['input']>;
  powerTypes?: InputMaybe<Array<PowerType>>;
  providers?: InputMaybe<Array<Provider>>;
  relativeGeoPoint?: InputMaybe<GeoPointInput>;
  royalMailFilter?: InputMaybe<IntersectionType>;
  services?: InputMaybe<Array<Service>>;
  siteIds?: InputMaybe<Array<Scalars['String']['input']>>;
  size?: InputMaybe<Scalars['Int']['input']>;
  uberFilter?: InputMaybe<IntersectionType>;
};

export type QueryMarkersResponse = {
  __typename?: 'QueryMarkersResponse';
  args: QueryMarkersResponseArgs;
  markers: Array<Marker>;
};

export type QueryMarkersResponseArgs = {
  __typename?: 'QueryMarkersResponseArgs';
  adsFilter?: Maybe<IntersectionType>;
  chargingSpeeds?: Maybe<Array<ChargingSpeed>>;
  connectorTypes?: Maybe<Array<ConnectorType>>;
  countries?: Maybe<Array<Country>>;
  cpo?: Maybe<Array<Scalars['String']['output']>>;
  entitledSchemes?: Maybe<Array<Scalars['Int']['output']>>;
  fuels?: Maybe<Array<FuelType>>;
  geoHashes: Array<Scalars['String']['output']>;
  isOpen24Hours?: Maybe<Scalars['Boolean']['output']>;
  maximumPrice?: Maybe<Scalars['Float']['output']>;
  minimumSpeed?: Maybe<Scalars['Int']['output']>;
  powerTypes?: Maybe<Array<PowerType>>;
  providers?: Maybe<Array<Provider>>;
  /** @deprecated Field no longer supported */
  royalMailFilter?: Maybe<IntersectionType>;
  services?: Maybe<Array<Service>>;
  siteIds?: Maybe<Array<Scalars['String']['output']>>;
  uberFilter?: Maybe<IntersectionType>;
  zoom: Scalars['Float']['output'];
};

export type SearchResult = Chargepoint | Connector;

export type SearchResultItem = {
  __typename?: 'SearchResultItem';
  distance?: Maybe<Scalars['Float']['output']>;
  result: SearchResult;
};

export enum SearchResultType {
  Chargepoint = 'Chargepoint',
  Connector = 'Connector'
}

export enum Service {
  ADS = 'ADS',
  AD_BLUE = 'AD_BLUE',
  ARAL_FUEL_CARD = 'ARAL_FUEL_CARD',
  ARAL_STORE = 'ARAL_STORE',
  ARAL_ULTIMATE = 'ARAL_ULTIMATE',
  ATM_MACHINE = 'ATM_MACHINE',
  BP_ME = 'BP_ME',
  CABRIO_CARE = 'CABRIO_CARE',
  CAR_COURT = 'CAR_COURT',
  CAR_RENTAL = 'CAR_RENTAL',
  CAR_WASH_PLANT = 'CAR_WASH_PLANT',
  EV = 'EV',
  LPG = 'LPG',
  NATURAL_GAS = 'NATURAL_GAS',
  NEAR_MOTORWAY = 'NEAR_MOTORWAY',
  PAYBACK = 'PAYBACK',
  PAYBACK_FUEL_AND_GO = 'PAYBACK_FUEL_AND_GO',
  PETIT_BISTRO = 'PETIT_BISTRO',
  RECUP = 'RECUP',
  RESTAURANT = 'RESTAURANT',
  REWE_TO_GO = 'REWE_TO_GO',
  SUPER_BOX = 'SUPER_BOX',
  SUPER_WASH = 'SUPER_WASH',
  SUV_WASH = 'SUV_WASH',
  TOLL_STATION = 'TOLL_STATION',
  TOO_GOOD_TO_GO = 'TOO_GOOD_TO_GO',
  TRUCK_DIESEL = 'TRUCK_DIESEL',
  TRUCK_WASH = 'TRUCK_WASH',
  TWENTY_FOUR_HOURS = 'TWENTY_FOUR_HOURS',
  VDA = 'VDA',
  WASH = 'WASH'
}

export type Site = {
  __typename?: 'Site';
  brandName?: Maybe<CrossAcceptanceBrand>;
  chargepoints?: Maybe<Array<Chargepoint>>;
  cpo?: Maybe<Scalars['String']['output']>;
  deleted?: Maybe<Scalars['Boolean']['output']>;
  distanceToRelativeGeoPoint?: Maybe<Scalars['Float']['output']>;
  evAvailability?: Maybe<EvAvailability>;
  fuels: Array<FuelType>;
  hasEvCharging: Scalars['Boolean']['output'];
  hasFuel: Scalars['Boolean']['output'];
  hidden?: Maybe<Scalars['Boolean']['output']>;
  isAds: Scalars['Boolean']['output'];
  isOpen24Hours?: Maybe<Scalars['Boolean']['output']>;
  lastUpdated: Scalars['String']['output'];
  provider: Provider;
  sapId?: Maybe<Scalars['String']['output']>;
  services: Array<Service>;
  siteDetails: SiteDetails;
  siteId: Scalars['String']['output'];
};

export type SiteDetails = {
  __typename?: 'SiteDetails';
  address?: Maybe<Scalars['String']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  country: Scalars['String']['output'];
  email?: Maybe<Scalars['String']['output']>;
  fax?: Maybe<Scalars['String']['output']>;
  geohash: Scalars['String']['output'];
  hotline?: Maybe<Scalars['String']['output']>;
  hours: Hours;
  location: GeoPoint;
  phone?: Maybe<Scalars['String']['output']>;
  postcode?: Maybe<Scalars['String']['output']>;
  siteName?: Maybe<Scalars['String']['output']>;
  siteProviderIds: Array<SiteProviderId>;
};

export type SiteProviderId = {
  __typename?: 'SiteProviderId';
  id: Scalars['String']['output'];
  type: Scalars['String']['output'];
};

export enum TariffType {
  GUEST = 'GUEST',
  PAYG = 'PAYG',
  SUBSCRIBER = 'SUBSCRIBER'
}

export type ConnectorsQueryVariables = Exact<{
  connectorIds: Array<Scalars['String']['input']> | Scalars['String']['input'];
}>;


export type ConnectorsQuery = { __typename?: 'Query', connectors: Array<{ __typename?: 'Connector', chargepoint?: { __typename?: 'Chargepoint', site?: { __typename?: 'Site', siteDetails: { __typename?: 'SiteDetails', country: string } } | null } | null }> };


export const ConnectorsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"connectors"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"connectorIds"}},"type":{"kind":"NonNullType","type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"connectors"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"connectorIds"},"value":{"kind":"Variable","name":{"kind":"Name","value":"connectorIds"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"chargepoint"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"site"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"siteDetails"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"country"}}]}}]}}]}}]}}]}}]} as unknown as DocumentNode<ConnectorsQuery, ConnectorsQueryVariables>;