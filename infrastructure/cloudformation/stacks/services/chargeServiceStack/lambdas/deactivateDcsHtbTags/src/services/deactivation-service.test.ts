import { TagDeactivationService } from './deactivation-service';
import { DeactivationCommandFactory } from '../commands/deactivation-factory';
import { logger } from '../utils/logger';
import { getUserInfo } from './user';
import { sortTagsByPriority } from '../utils/helpers';

jest.mock('./user');
jest.mock('../commands/deactivation-factory');
jest.mock('../utils/logger', () => ({
    logger: { info: jest.fn(), error: jest.fn() },
}));

const mockGetUserInfo = getUserInfo as jest.Mock;
const mockCreateCommand = DeactivationCommandFactory.createCommand as jest.Mock;

const userTags = [
    {
        tagId: '1',
        tagCardNumber: 'A',
        tagNotes: 'virtual-HTB',
        tagStatus: 'ACTIVE',
    },
    {
        tagId: '2',
        tagCardNumber: 'B',
        tagNotes: 'virtual-DCS',
        tagStatus: 'ACTIVE',
    },
    {
        tagId: '3',
        tagCardNumber: 'C',
        tagNotes: 'physical-RFID',
        tagStatus: 'ACTIVE',
    },
    {
        tagId: '4',
        tagCardNumber: 'D',
        tagNotes: 'physical-RFID',
        tagStatus: 'ACTIVE',
    },
];

describe('TagDeactivationService', () => {
    let service: TagDeactivationService;
    beforeEach(() => {
        jest.clearAllMocks();
        service = new TagDeactivationService();
    });

    it('should throw error when country is missing', async () => {
        await expect(
            service.processDeactivationEvent({
                userId: 'user1',
                country: undefined,
                tagIds: [],
            } as any),
        ).rejects.toThrow('Country is required');
    });

    it('should sort tags by physical-RFID priority before deactivation', async () => {
        mockGetUserInfo.mockResolvedValue({
            userInfo: { tagIds: userTags, country: 'NL', balance: 10 },
        });

        mockCreateCommand.mockImplementation((tag) => ({
            execute: () => Promise.resolve({ status: 200 }),
        }));

        // Process the deactivation event
        const result = await service.processDeactivationEvent({
            userId: 'user1',
            country: 'NL',
            tagIds: [],
        });

        // Ensure that the tags are sorted correctly: physical-RFID tags first
        expect(result.successfullyTerminated[0].tag.tagNotes).toBe(
            'physical-RFID',
        );
        expect(result.successfullyTerminated[1].tag.tagNotes).toBe(
            'physical-RFID',
        );
        expect(result.successfullyTerminated[2].tag.tagNotes).toBe(
            'virtual-HTB',
        );
        expect(result.successfullyTerminated[3].tag.tagNotes).toBe(
            'virtual-DCS',
        );
    });

    it('deactivates all user tags when userId and country are provided', async () => {
        mockGetUserInfo.mockResolvedValue({
            userInfo: { tagIds: userTags, country: 'NL', balance: 10 },
        });
        mockCreateCommand.mockImplementation((tag) => ({
            execute: () => Promise.resolve({ status: 200 }),
        }));
        const result = await service.processDeactivationEvent({
            userId: 'user1',
            country: 'NL',
            tagIds: [],
        });
        expect(result.success).toBe(true);
        expect(result.successfullyTerminated.length).toBe(4);
        expect(result.failedToTerminate.length).toBe(0);
    });

    it('deactivates only provided tagIds when userId is not present', async () => {
        mockGetUserInfo.mockResolvedValue({
            userInfo: { tagIds: [], country: 'NL', balance: 5 },
        });
        mockCreateCommand.mockImplementation((tag) => ({
            execute: () => Promise.resolve({ status: 200 }),
        }));
        const tagIds = [
            {
                tagId: '3',
                tagCardNumber: 'C',
                tagNotes: 'virtual-HTB',
                tagStatus: 'ACTIVE',
            },
        ];
        const result = await service.processDeactivationEvent({
            userId: '',
            tagIds,
            country: 'NL',
        });
        expect(result.success).toBe(true);
        expect(result.successfullyTerminated.length).toBe(1);
        expect(result.failedToTerminate.length).toBe(0);
    });

    it('prefers userId over tagIds if both are present', async () => {
        mockGetUserInfo.mockResolvedValue({
            userInfo: { tagIds: userTags, country: 'NL', balance: 10 },
        });
        mockCreateCommand.mockImplementation((tag) => ({
            execute: () => Promise.resolve({ status: 200 }),
        }));
        const tagIds = [
            {
                tagId: '999',
                tagCardNumber: 'Z',
                tagNotes: 'virtual-HTB',
                tagStatus: 'ACTIVE',
            },
        ];
        const result = await service.processDeactivationEvent({
            userId: 'user1',
            tagIds,
            country: 'NL',
        });
        expect(result.successfullyTerminated.length).toBe(4); // userTags, not tagIds
        expect(result.success).toBe(true);
    });

    it('returns failure if no tags found', async () => {
        mockGetUserInfo.mockResolvedValue({
            userInfo: { tagIds: [], country: 'NL', balance: 0 },
        });
        const result = await service.processDeactivationEvent({
            userId: 'user1',
            country: 'NL',
            tagIds: [],
        });
        expect(result.success).toBe(false);
        expect(result.successfullyTerminated.length).toBe(0);
        expect(result.failedToTerminate.length).toBe(0);
    });

    it('handles errors in deactivateTags', async () => {
        mockGetUserInfo.mockResolvedValue({
            userInfo: { tagIds: userTags, country: 'NL', balance: 10 },
        });
        mockCreateCommand.mockImplementation((tag) => ({
            execute: () => {
                throw new Error('fail');
            },
        }));
        const result = await service.processDeactivationEvent({
            userId: 'user1',
            country: 'NL',
            tagIds: [],
        });
        expect(result.success).toBe(false);
        expect(result.failedToTerminate.length).toBe(4);
    });
});
