import { Tag } from '../types/types';
import { env } from './../env';
import { Countries } from './constants';

export const getFleetGroup = ({ country }: { country: string }) => {
    switch (country) {
        case Countries.NL:
            return env.NL_FLEET_GROUP;
        case Countries.DE:
            return env.DE_FLEET_GROUP;
        case Countries.ES:
            return env.ES_FLEET_GROUP;
        default:
            return '';
    }
};

export const getHTBCredentials = ({ country }: { country: string }) => {
    switch (country) {
        case Countries.NL:
            return {
                username: env.HTB_SERVICES_USERNAME_NL,
                password: env.HTB_SERVICES_PASSWORD_NL,
            };
        case Countries.DE:
            return {
                username: env.HTB_SERVICES_USERNAME_DE,
                password: env.HTB_SERVICES_PASSWORD_DE,
            };
        case Countries.ES:
            return {
                username: env.HTB_SERVICES_USERNAME_ES,
                password: env.HTB_SERVICES_PASSWORD_ES,
            };
        default:
            return {
                username: '',
                password: '',
            };
    }
};

export function sortTagsByPriority(
    tags: Array<Tag>,
    priorityTagNote: string,
): Array<Tag> {
    return tags.sort((a, b) => {
        if (a.tagNotes === priorityTagNote && b.tagNotes !== priorityTagNote) {
            return -1;
        }

        if (b.tagNotes === priorityTagNote && a.tagNotes !== priorityTagNote) {
            return 1;
        }
        return 0;
    });
}
