import {
    getFleetGroup,
    getHTBCredentials,
    sortTagsByPriority,
} from '../src/utils/helpers';
import { Countries } from '../src/utils/constants';

jest.mock('../src/env', () => ({
    env: {
        NL_FLEET_GROUP: 'nl-fleet',
        DE_FLEET_GROUP: 'de-fleet',
        ES_FLEET_GROUP: 'es-fleet',
        HTB_SERVICES_USERNAME_NL: 'nl-username',
        HTB_SERVICES_PASSWORD_NL: 'nl-password',
        HTB_SERVICES_USERNAME_DE: 'de-username',
        HTB_SERVICES_PASSWORD_DE: 'de-password',
        HTB_SERVICES_USERNAME_ES: 'es-username',
        HTB_SERVICES_PASSWORD_ES: 'es-password',
    },
}));

const userTags = [
    {
        tagId: '1',
        tagCardNumber: 'A',
        tagNotes: 'virtual-HTB',
        tagStatus: 'ACTIVE',
    },
    {
        tagId: '2',
        tagCardNumber: 'B',
        tagNotes: 'virtual-DCS',
        tagStatus: 'ACTIVE',
    },
    {
        tagId: '3',
        tagCardNumber: 'C',
        tagNotes: 'physical-RFID',
        tagStatus: 'ACTIVE',
    },
    {
        tagId: '4',
        tagCardNumber: 'D',
        tagNotes: 'physical-RFID',
        tagStatus: 'ACTIVE',
    },
];

describe('Helper Utilities', () => {
    describe('getFleetGroup', () => {
        it('should return correct fleet group for NL', () => {
            const result = getFleetGroup({ country: Countries.NL });
            expect(result).toBe('nl-fleet');
        });

        it('should return correct fleet group for DE', () => {
            const result = getFleetGroup({ country: Countries.DE });
            expect(result).toBe('de-fleet');
        });

        it('should return correct fleet group for ES', () => {
            const result = getFleetGroup({ country: Countries.ES });
            expect(result).toBe('es-fleet');
        });

        it('should return empty string for unsupported country', () => {
            const result = getFleetGroup({ country: 'FR' });
            expect(result).toBe('');
        });
    });

    describe('getHTBCredentials', () => {
        it('should return correct credentials for NL', () => {
            const result = getHTBCredentials({ country: Countries.NL });
            expect(result).toEqual({
                username: 'nl-username',
                password: 'nl-password',
            });
        });

        it('should return correct credentials for DE', () => {
            const result = getHTBCredentials({ country: Countries.DE });
            expect(result).toEqual({
                username: 'de-username',
                password: 'de-password',
            });
        });

        it('should return correct credentials for ES', () => {
            const result = getHTBCredentials({ country: Countries.ES });
            expect(result).toEqual({
                username: 'es-username',
                password: 'es-password',
            });
        });

        it('should return empty credentials for unsupported country', () => {
            const result = getHTBCredentials({ country: 'FR' });
            expect(result).toEqual({
                username: '',
                password: '',
            });
        });
    });

    describe('sortTagsByPriority', () => {
        it('should prioritize physical-RFID tags first', () => {
            const tags = userTags;

            const sortedTags = sortTagsByPriority(tags, 'physical-RFID');

            // Ensure the 'physical-RFID' tags come first
            expect(sortedTags[0].tagNotes).toBe('physical-RFID');
            expect(sortedTags[1].tagNotes).toBe('physical-RFID');
            expect(sortedTags[2].tagNotes).toBe('virtual-HTB');
            expect(sortedTags[3].tagNotes).toBe('virtual-DCS');
        });
    });
});
