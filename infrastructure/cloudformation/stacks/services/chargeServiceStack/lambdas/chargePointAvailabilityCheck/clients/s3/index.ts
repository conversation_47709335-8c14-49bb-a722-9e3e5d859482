import { AWSError, S3 } from 'aws-sdk';
import { ACLObject, S3ClientConfig } from '../../common/interfaces';
import { Readable } from 'node:stream';
const { S3_REGION, NODE_ENV } = process.env;

const clientConfig: S3ClientConfig = {
  region: S3_REGION || 'eu-west-2',
};

if (!NODE_ENV) {
  clientConfig.secretAccessKey = 'MINO123456';
  clientConfig.accessKeyId = 'MINO';
  clientConfig.endpoint = 'http://localhost:9000';
  clientConfig.s3ForcePathStyle = true;
}

const client = new S3(clientConfig);

export const put = (
  bucketName: string,
  fileKey: string,
  fileContent: Buffer | Readable,
  acl?: ACLObject,
  length?: number,
) => {
  const params = {
    Bucket: bucketName,
    Key: fileKey,
    Body: fileContent,
    ACL: acl,
    ContentLength: length,
  };

  return new Promise((resolve, reject) => {
    client.putObject(params, (err: AWSError, data: S3.PutObjectOutput) => {
      if (err) {
        console.error('Error putting object in S3', err);
        return reject(err);
      }
      return resolve(data);
    });
  });
};
