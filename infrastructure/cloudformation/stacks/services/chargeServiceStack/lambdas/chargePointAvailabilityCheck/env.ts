import dotenv from 'dotenv';
import path from 'path';

dotenv.config();

const requiredEnvVars = [
  'OTG_URL_V7',
  'OTG_API_KEY_V7',
  'CHARGEVISION_TOKEN_ENDPOINT',
  'CHARGEVISION_URL',
  'CHARGEVISION_AUTH_KEY',
  'CHARGE_STACK_DATA_HUB_BUCKET',
  'S3_REGION',
  'CHARGEPOINT_AVAILABILITY_CHECK_TRIGGER_ENABLED',
];

const missingVars = requiredEnvVars.filter(
  (key) => process.env[key] === undefined,
);

if (missingVars.length > 0) {
  console.error('Missing required environment variables:', missingVars);
  if (process.env.NODE_ENV !== 'test') {
    process.exit(-1);
  }
}

const {
  NODE_ENV,
  OTG_URL_V7,
  OTG_API_KEY_V7,
  CHARGEVISION_TOKEN_ENDPOINT,
  CHARGEVISION_URL,
  CHARGEVISION_AUTH_KEY,
  CHAR<PERSON>_STACK_DATA_HUB_BUCKET,
  S3_REGION,
  CHARGEPOINT_AVAILABILITY_CHECK_TRIGGER_ENABLED,
} = process.env;

const REQUIRED_ENV_VARS = {
  OTG_URL_V7,
  OTG_API_KEY_V7,
  CHARGEVISION_TOKEN_ENDPOINT,
  CHARGEVISION_URL,
  CHARGEVISION_AUTH_KEY,
  CHARGE_STACK_DATA_HUB_BUCKET,
  S3_REGION,
  CHARGEPOINT_AVAILABILITY_CHECK_TRIGGER_ENABLED,
};

const OPTIONAL_ENV_VARS = {
  NODE_ENV,
};

export type EnvVars = Record<keyof typeof REQUIRED_ENV_VARS, string> &
  Record<keyof typeof OPTIONAL_ENV_VARS, string | undefined>;

export const env = { ...OPTIONAL_ENV_VARS, ...REQUIRED_ENV_VARS } as EnvVars;
