import { AvailabilityState } from './services/apollo/response.types';

export enum SupportedCountries {
  UK = 'UK',
  DE = 'DE',
  NL = 'NL',
  ES = 'ES',
  US = 'US',
}

export enum Providers {
  BPCM = 'BPCM',
  HTB = 'hasToBe',
  ARAL = 'aral',
  SEMARCHY = 'semarchy',
  CHARGEVISION = 'chargeVision',
}

export enum CPO {
  CHARGEVISION = 'chargeVision',
  HTB = 'hasToBe',
}

export type FormattedConnector = {
  connectorExternalId: string;
  state: AvailabilityState;
};

export type FormattedChargepoint = {
  id: string;
  available: boolean;
  connectors: Array<FormattedConnector>;
  error?: boolean;
};

export type FormattedChargepointWithCount = FormattedChargepoint & {
  unavailable?: boolean;
  connectorCount: number;
};

export type FormattedChargepointWithTariff = {
  id: string;
  PAYG: string;
  SUBS: string;
  GUEST: string;
};

export type AvailabilityApiError = {
  error: boolean;
  message?: string;
  code?: number;
  status?: number;
};
