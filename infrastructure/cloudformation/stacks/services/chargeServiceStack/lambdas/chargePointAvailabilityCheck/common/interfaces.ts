export interface DefaultResponse {
  status: string;
  message: string;
}

export type ACLObject =
  | 'private'
  | 'public-read'
  | 'public-read-write'
  | 'authenticated-read'
  | 'aws-exec-read'
  | 'bucket-owner-read'
  | 'bucket-owner-full-control';

export interface S3ClientConfig {
  region: string;
  secretAccessKey?: string;
  accessKeyId?: string;
  endpoint?: string;
  s3ForcePathStyle?: boolean;
}
export type Chargepoint = {
  providerInternalId: string;
  providerExternalId: string;
  apolloInternalId: string;
  apolloExternalId: string;
};
