/**
 * @jest-environment node
 */

jest.mock('../clients/s3', () => ({
  put: jest.fn().mockResolvedValue({}),
}));

jest.mock('node:zlib', () => ({
  gzip: jest.fn((buffer, callback) => {
    // Mock gzip to return a compressed buffer
    const compressed = Buffer.from('compressed-' + buffer.toString());
    callback(null, compressed);
  }),
}));

import { put as s3Put } from '../clients/s3';
import { gzip } from 'node:zlib';
import { SupportedCountries, CPO } from '../index.types';
import { constructCsv } from './constructCsv';
import { CsvRow } from './formatResults';

jest.setTimeout(10000); // 10s timeout to avoid test timeouts

describe('constructCsv', () => {
  const ORIGINAL_BUCKET = process.env.CHARGEPOINT_AVAILABILITY_BUCKET;
  const ORIGINAL_DATA_HUB_BUCKET = process.env.CHARGE_STACK_DATA_HUB_BUCKET;

  const mockCsvData: CsvRow[] = [
    {
      timestamp: '2024-01-15T10:30:00.000Z',
      country: 'UK',
      provider: 'CHARGEVISION',
      chargepoint_id: 'CP001',
      chargepoint_available_emsp: true,
      chargepoint_available_provider: true,
      connector_id: 'CON001',
      connector_available_emsp: true,
      connector_available_provider: false,
    },
    {
      timestamp: '2024-01-15T10:30:00.000Z',
      country: 'UK',
      provider: 'CHARGEVISION',
      chargepoint_id: 'CP002',
      chargepoint_available_emsp: false,
      chargepoint_available_provider: true,
      connector_id: 'CON002',
      connector_available_emsp: false,
      connector_available_provider: false,
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    // Set default environment variables
    process.env.CHARGEPOINT_AVAILABILITY_BUCKET = 'test-emsp-bucket';
  });

  afterEach(() => {
    process.env.CHARGEPOINT_AVAILABILITY_BUCKET = ORIGINAL_BUCKET;
    process.env.CHARGE_STACK_DATA_HUB_BUCKET = ORIGINAL_DATA_HUB_BUCKET;
  });

  describe('successful uploads', () => {
    it('should work if bucket env var is present', async () => {
      process.env.CHARGEPOINT_AVAILABILITY_BUCKET = 'test-bucket';

      await expect(
        constructCsv([], SupportedCountries.UK, CPO.CHARGEVISION),
      ).resolves.not.toThrow();

      expect(s3Put).toHaveBeenCalled();
    });

    it('should upload to both buckets when both are configured', async () => {
      const result = await constructCsv(
        mockCsvData,
        SupportedCountries.UK,
        CPO.CHARGEVISION,
      );

      // Should call s3Put twice - once for each bucket
      expect(s3Put).toHaveBeenCalledTimes(2);

      // Check eMSP bucket upload (first call)
      expect(s3Put).toHaveBeenNthCalledWith(
        1,
        'test-emsp-bucket',
        expect.stringMatching(/^\d{4}-\d{2}-\d{2}\/.+\.csv$/),
        expect.any(Buffer),
      );

      // Check Data Hub bucket upload (second call - gzipped)
      // The implementation uses the default bucket if env var is not set
      expect(s3Put).toHaveBeenNthCalledWith(
        2,
        'ws-01t0-shared-ingestion-bucket', // This is the actual default used by the implementation
        expect.stringMatching(
          /^apollo\/emsp\/cp_availability_check\/\d{4}\/\d{2}\/\d{2}\/.+\.csv\.gz$/,
        ),
        expect.any(Object), // Readable stream
        'bucket-owner-full-control',
        expect.any(Number),
      );

      // Should return the eMSP bucket key
      expect(result).toMatch(/^\d{4}-\d{2}-\d{2}\/.+\.csv$/);
    });

    it('should upload to both buckets with explicit Data Hub bucket configured', async () => {
      const result = await constructCsv(
        mockCsvData,
        SupportedCountries.UK,
        CPO.CHARGEVISION,
      );

      // Should call s3Put twice - once for each bucket
      expect(s3Put).toHaveBeenCalledTimes(2);

      // Check eMSP bucket upload (first call)
      expect(s3Put).toHaveBeenNthCalledWith(
        1,
        'test-emsp-bucket',
        expect.stringMatching(/^\d{4}-\d{2}-\d{2}\/.+\.csv$/),
        expect.any(Buffer),
      );

      // Check Data Hub bucket upload (second call - gzipped)
      expect(s3Put).toHaveBeenNthCalledWith(
        2,
        'ws-01t0-shared-ingestion-bucket',
        expect.stringMatching(
          /^apollo\/emsp\/cp_availability_check\/\d{4}\/\d{2}\/\d{2}\/.+\.csv\.gz$/,
        ),
        expect.any(Object), // Readable stream
        'bucket-owner-full-control',
        expect.any(Number),
      );

      // Should return the eMSP bucket key
      expect(result).toMatch(/^\d{4}-\d{2}-\d{2}\/.+\.csv$/);
    });

    it('should upload to Data Hub bucket even when eMSP bucket is not configured', async () => {
      delete process.env.CHARGEPOINT_AVAILABILITY_BUCKET;
      // The implementation always uploads to Data Hub bucket (default or env var)

      await constructCsv(mockCsvData, SupportedCountries.UK, CPO.CHARGEVISION);

      // Should only call s3Put once for Data Hub bucket (implementation always uploads there)
      expect(s3Put).toHaveBeenCalledTimes(1);
      expect(s3Put).toHaveBeenCalledWith(
        'ws-01t0-shared-ingestion-bucket', // Default bucket
        expect.stringMatching(
          /^apollo\/emsp\/cp_availability_check\/\d{4}\/\d{2}\/\d{2}\/.+\.csv\.gz$/,
        ),
        expect.any(Object),
        'bucket-owner-full-control',
        expect.any(Number),
      );
    });

    it('should use default Data Hub bucket when env var is not set', async () => {
      delete process.env.CHARGE_STACK_DATA_HUB_BUCKET;
      delete process.env.CHARGEPOINT_AVAILABILITY_BUCKET;

      await constructCsv(mockCsvData, SupportedCountries.UK, CPO.CHARGEVISION);

      // Should upload to the default Data Hub bucket
      expect(s3Put).toHaveBeenCalledWith(
        'ws-01t0-shared-ingestion-bucket', // Default bucket from implementation
        expect.stringMatching(
          /^apollo\/emsp\/cp_availability_check\/\d{4}\/\d{2}\/\d{2}\/.+\.csv\.gz$/,
        ),
        expect.any(Object),
        'bucket-owner-full-control',
        expect.any(Number),
      );
    });
  });

  describe('CSV content generation', () => {
    it('should generate correct CSV content with proper escaping', async () => {
      const dataWithSpecialChars: CsvRow[] = [
        {
          timestamp: '2024-01-15T10:30:00.000Z',
          country: 'UK',
          provider: 'TEST,PROVIDER',
          chargepoint_id: 'CP"001',
          chargepoint_available_emsp: true,
          chargepoint_available_provider: false,
          connector_id: 'CON\n001',
          connector_available_emsp: false,
          connector_available_provider: false,
        },
      ];

      await constructCsv(
        dataWithSpecialChars,
        SupportedCountries.UK,
        CPO.CHARGEVISION,
      );

      const csvBuffer = (s3Put as jest.Mock).mock.calls[0][2] as Buffer;
      const csvContent = csvBuffer.toString();

      // Check header
      expect(csvContent).toContain(
        'timestamp,country,provider,chargepoint_id,chargepoint_available_emsp,chargepoint_available_provider,connector_id,connector_available_emsp,connector_available_provider',
      );

      // Check escaped values
      expect(csvContent).toContain('"TEST,PROVIDER"'); // Comma escaped
      expect(csvContent).toContain('"CP""001"'); // Quote escaped
      expect(csvContent).toContain('"CON\n001"'); // Newline escaped
    });

    it('should handle null and undefined values correctly', async () => {
      const dataWithNulls: CsvRow[] = [
        {
          timestamp: '2024-01-15T10:30:00.000Z',
          country: 'UK',
          provider: 'CHARGEVISION',
          chargepoint_id: 'CP001',
          chargepoint_available_emsp: true,
          chargepoint_available_provider: false,
          connector_id: 'CON001',
          connector_available_emsp: false,
          connector_available_provider: false,
        },
      ];

      await constructCsv(
        dataWithNulls,
        SupportedCountries.UK,
        CPO.CHARGEVISION,
      );

      const csvBuffer = (s3Put as jest.Mock).mock.calls[0][2] as Buffer;
      const csvContent = csvBuffer.toString();

      // Check header exists
      expect(csvContent).toContain(
        'timestamp,country,provider,chargepoint_id,chargepoint_available_emsp,chargepoint_available_provider,connector_id,connector_available_emsp,connector_available_provider',
      );

      // Check data row exists
      expect(csvContent).toContain(
        '2024-01-15T10:30:00.000Z,UK,CHARGEVISION,CP001,true,false,CON001,false,false',
      );

      // Verify the complete structure with proper newlines
      const expectedCsv = `timestamp,country,provider,chargepoint_id,chargepoint_available_emsp,chargepoint_available_provider,connector_id,connector_available_emsp,connector_available_provider
2024-01-15T10:30:00.000Z,UK,CHARGEVISION,CP001,true,false,CON001,false,false
`;
      expect(csvContent).toBe(expectedCsv);
    });
  });

  describe('error handling', () => {
    it('should throw error when eMSP bucket upload fails', async () => {
      const uploadError = new Error('S3 upload failed');
      (s3Put as jest.Mock).mockRejectedValueOnce(uploadError);

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      await expect(
        constructCsv(mockCsvData, SupportedCountries.UK, CPO.CHARGEVISION),
      ).rejects.toThrow('Error uploading CSV to eMSP bucket: S3 upload failed');

      expect(consoleSpy).toHaveBeenCalledWith(
        'Error uploading file to eMSP bucket:',
        uploadError,
      );

      consoleSpy.mockRestore();
    });

    it('should throw error when Data Hub bucket upload fails', async () => {
      const uploadError = new Error('Gzip upload failed');
      (s3Put as jest.Mock)
        .mockResolvedValueOnce({}) // First call (eMSP) succeeds
        .mockRejectedValueOnce(uploadError); // Second call (Data Hub) fails

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      await expect(
        constructCsv(mockCsvData, SupportedCountries.UK, CPO.CHARGEVISION),
      ).rejects.toThrow(
        'Error uploading gzipped CSV to DataHub bucket: Gzip upload failed',
      );

      expect(consoleSpy).toHaveBeenCalledWith(
        'Error uploading gzipped CSV to DataHub bucket:',
        uploadError,
      );

      consoleSpy.mockRestore();
    });

    it('should handle non-Error objects in catch blocks', async () => {
      (s3Put as jest.Mock).mockRejectedValueOnce('String error');

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      await expect(
        constructCsv(mockCsvData, SupportedCountries.UK, CPO.CHARGEVISION),
      ).rejects.toThrow('Error uploading CSV to eMSP bucket: Unknown error');

      consoleSpy.mockRestore();
    });

    it('should handle gzip compression errors', async () => {
      const gzipError = new Error('Gzip compression failed');
      (gzip as unknown as jest.Mock).mockImplementation((buffer, callback) => {
        callback(gzipError, null);
      });

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      await expect(
        constructCsv(mockCsvData, SupportedCountries.UK, CPO.CHARGEVISION),
      ).rejects.toThrow(
        'Error uploading gzipped CSV to DataHub bucket: Gzip compression failed',
      );

      consoleSpy.mockRestore();
    });
  });

  describe('different market and provider combinations', () => {
    const testCases = [
      { market: SupportedCountries.UK, provider: CPO.CHARGEVISION },
    ];

    testCases.forEach(({ market, provider }) => {
      it(`should handle ${market}-${provider} combination`, async () => {
        // Reset gzip mock to work properly for this test
        (gzip as unknown as jest.Mock).mockImplementation(
          (buffer, callback) => {
            const compressed = Buffer.from('compressed-' + buffer.toString());
            callback(null, compressed);
          },
        );

        const result = await constructCsv(mockCsvData, market, provider);

        expect(result).toContain(`_${market}_${provider}_`);
        expect(s3Put).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('file naming and paths', () => {
    beforeEach(() => {
      // Mock Date to ensure consistent timestamps
      jest.useFakeTimers();
      jest.setSystemTime(new Date('2024-01-15T10:30:45.123Z'));

      // Reset gzip mock to work properly for this test
      (gzip as unknown as jest.Mock).mockImplementation((buffer, callback) => {
        const compressed = Buffer.from('compressed-' + buffer.toString());
        callback(null, compressed);
      });
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should generate correct file names and paths', async () => {
      // Set explicit env vars for this test
      process.env.CHARGEPOINT_AVAILABILITY_BUCKET = 'test-emsp-bucket';
      process.env.CHARGE_STACK_DATA_HUB_BUCKET =
        'ws-01t0-shared-ingestion-bucket';

      await constructCsv(mockCsvData, SupportedCountries.UK, CPO.CHARGEVISION);

      // Check eMSP bucket call
      expect(s3Put).toHaveBeenCalledWith(
        'test-emsp-bucket',
        '2024-01-15/2024-01-15T10-30-45-123Z_UK_chargeVision_chargepoint_and_connector_availability.csv',
        expect.any(Buffer),
      );

      // Check Data Hub bucket call
      expect(s3Put).toHaveBeenCalledWith(
        'ws-01t0-shared-ingestion-bucket',
        'apollo/emsp/cp_availability_check/2024/01/15/2024-01-15T10-30-45-123Z_UK_chargeVision_chargepoint_and_connector_availability.csv.gz',
        expect.any(Object),
        'bucket-owner-full-control',
        expect.any(Number),
      );
    });
  });

  describe('console logging', () => {
    it('should log success message for Data Hub upload', async () => {
      const consoleSpy = jest.spyOn(console, 'info').mockImplementation();

      await constructCsv(mockCsvData, SupportedCountries.UK, CPO.CHARGEVISION);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringMatching(
          /^Gzipped CSV uploaded successfully: apollo\/emsp\/cp_availability_check\/\d{4}\/\d{2}\/\d{2}\/.+\.csv\.gz$/,
        ),
      );

      consoleSpy.mockRestore();
    });
  });
});
