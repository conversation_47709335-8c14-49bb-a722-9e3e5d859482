import { Readable } from 'node:stream';
import { gzip } from 'node:zlib';
import { put as s3Put } from '../clients/s3';
import { CsvRow } from './formatResults';
import { SupportedCountries, CPO } from '../index.types';
import { promisify } from 'node:util';

const CHARGE_STACK_DATA_HUB_BUCKET =
  process.env.CHARGE_STACK_DATA_HUB_BUCKET || 'ws-01t0-shared-ingestion-bucket';
const gzipPromise = promisify(gzip);

const escapeCsv = (
  field: string | number | boolean | null | undefined,
): string => {
  if (field === null || field === undefined) return '';
  const str = String(field);
  if (/[",\n]/.test(str)) {
    return `"${str.replace(/"/g, '""')}"`;
  }
  return str;
};

const createGzipStreamWithLength = async (
  buffer: Buffer,
): Promise<{ stream: Readable; length: number }> => {
  const gzippedBuffer = await gzipPromise(buffer);
  const length = gzippedBuffer.length;
  const stream = Readable.from(gzippedBuffer);
  return { stream, length };
};

export const constructCsv = async (
  data: CsvRow[],
  market: SupportedCountries,
  provider: CPO,
): Promise<string> => {
  const BUCKET_NAME = process.env.CHARGEPOINT_AVAILABILITY_BUCKET;

  const { csvContent, fullKey, fileName, now } = buildCsv(
    data,
    market,
    provider,
  );

  // Log alarm if neither bucket is configured
  if (!BUCKET_NAME && !CHARGE_STACK_DATA_HUB_BUCKET) {
    console.log('[ALARM chargepointAvailabilityCheck - S3 BUCKET NOT FOUND]');
  }

  // Upload CSV to eMSP bucket
  if (BUCKET_NAME) {
    try {
      await s3Put(BUCKET_NAME, fullKey, Buffer.from(csvContent, 'utf-8'));
    } catch (error) {
      console.error(
        '[ALARM chargepointAvailabilityCheck - CSV UPLOAD FAILURE]',
      );
      console.error('Error uploading file to eMSP bucket:', error);
      throw new Error(
        `Error uploading CSV to eMSP bucket: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
      );
    }
  }

  // Upload gzipped CSV to Data Hub bucket
  if (CHARGE_STACK_DATA_HUB_BUCKET) {
    try {
      const { stream: gzStream, length } = await createGzipStreamWithLength(
        Buffer.from(csvContent, 'utf-8'),
      );
      const dataHubKey = buildDataHubKey(fileName, now);
      await s3Put(
        CHARGE_STACK_DATA_HUB_BUCKET,
        dataHubKey,
        gzStream,
        'bucket-owner-full-control',
        length,
      );
      console.info(`Gzipped CSV uploaded successfully: ${dataHubKey}`);
    } catch (error) {
      console.error(
        '[ALARM chargepointAvailabilityCheck - CSV UPLOAD FAILURE]',
      );
      console.error('Error uploading gzipped CSV to DataHub bucket:', error);
      throw new Error(
        `Error uploading gzipped CSV to DataHub bucket: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
      );
    }
  }

  return fullKey;
};

const buildCsv = (
  data: CsvRow[],
  market: SupportedCountries,
  provider: CPO,
): { csvContent: string; fullKey: string; fileName: string; now: Date } => {
  const now = new Date();
  const timestamp = now.toISOString().replace(/[:.]/g, '-');
  const fileName = `${timestamp}_${market}_${provider}_chargepoint_and_connector_availability.csv`;

  const fullKey = buildEmspKey(fileName, now);

  const csvHeader =
    [
      'timestamp',
      'country',
      'provider',
      'chargepoint_id',
      'chargepoint_available_emsp',
      'chargepoint_available_provider',
      'connector_id',
      'connector_available_emsp',
      'connector_available_provider',
    ].join(',') + '\n';

  const csvRows = data
    .map(
      (row) =>
        [
          escapeCsv(row.timestamp),
          escapeCsv(row.country),
          escapeCsv(row.provider),
          escapeCsv(row.chargepoint_id),
          escapeCsv(row.chargepoint_available_emsp),
          escapeCsv(row.chargepoint_available_provider),
          escapeCsv(row.connector_id),
          escapeCsv(row.connector_available_emsp),
          escapeCsv(row.connector_available_provider),
        ].join(',') + '\n',
    )
    .join('');

  return { csvContent: csvHeader + csvRows, fullKey, fileName, now };
};

const buildEmspKey = (fileName: string, now: Date): string => {
  const datePrefix = now.toISOString().split('T')[0];
  return `${datePrefix}/${fileName}`;
};

const buildDataHubKey = (fileName: string, date: Date): string => {
  const yyyy = date.getUTCFullYear();
  const mm = String(date.getUTCMonth() + 1).padStart(2, '0');
  const dd = String(date.getUTCDate()).padStart(2, '0');
  return `apollo/emsp/cp_availability_check/${yyyy}/${mm}/${dd}/${fileName}.gz`;
};
