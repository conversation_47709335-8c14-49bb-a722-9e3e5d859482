{"name": "es_monthly_snapshot_cleaner_lambda", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "es_monthly_snapshot_cleaner_lambda", "dependencies": {"@aws-crypto/sha256-js": "^5.0.0", "@aws-sdk/client-elasticsearch-service": "^3.410.0", "@aws-sdk/credential-provider-node": "^3.410.0", "@aws-sdk/node-http-handler": "^3.374.0", "@aws-sdk/protocol-http": "^3.374.0", "@aws-sdk/signature-v4": "^3.374.0", "aws-sdk": "^2.723.0", "aws4": "^1.12.0", "axios": "^1.10.0"}, "devDependencies": {"@types/aws4": "^1.11.3"}}, "node_modules/@aws-crypto/crc32": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/crc32/-/crc32-3.0.0.tgz", "integrity": "sha512-IzSgsrxUcsrejQbPVilIKy16kAT52EwB6zSaI+M3xxIhKh5+aldEyvI+z6erM7TCLB2BJsFrtHjp6/4/sr+3dA==", "dependencies": {"@aws-crypto/util": "^3.0.0", "@aws-sdk/types": "^3.222.0", "tslib": "^1.11.1"}}, "node_modules/@aws-crypto/crc32/node_modules/@aws-crypto/util": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/util/-/util-3.0.0.tgz", "integrity": "sha512-2OJlpeJpCR48CC8r+uKVChzs9Iungj9wkZrl8Z041DWEWvyIHILYKCPNzJghKsivj+S3mLo6BVc7mBNzdxA46w==", "dependencies": {"@aws-sdk/types": "^3.222.0", "@aws-sdk/util-utf8-browser": "^3.0.0", "tslib": "^1.11.1"}}, "node_modules/@aws-crypto/ie11-detection": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/ie11-detection/-/ie11-detection-3.0.0.tgz", "integrity": "sha512-341lBBkiY1DfDNKai/wXM3aujNBkXR7tq1URPQDL9wi3AUbI80NR74uF1TXHMm7po1AcnFk8iu2S2IeU/+/A+Q==", "dependencies": {"tslib": "^1.11.1"}}, "node_modules/@aws-crypto/sha256-browser": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/sha256-browser/-/sha256-browser-3.0.0.tgz", "integrity": "sha512-8VLmW2B+gjFbU5uMeqtQM6Nj0/F1bro80xQXCW6CQBWgosFWXTx77aeOF5CAIAmbOK64SdMBJdNr6J41yP5mvQ==", "dependencies": {"@aws-crypto/ie11-detection": "^3.0.0", "@aws-crypto/sha256-js": "^3.0.0", "@aws-crypto/supports-web-crypto": "^3.0.0", "@aws-crypto/util": "^3.0.0", "@aws-sdk/types": "^3.222.0", "@aws-sdk/util-locate-window": "^3.0.0", "@aws-sdk/util-utf8-browser": "^3.0.0", "tslib": "^1.11.1"}}, "node_modules/@aws-crypto/sha256-browser/node_modules/@aws-crypto/sha256-js": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/sha256-js/-/sha256-js-3.0.0.tgz", "integrity": "sha512-PnNN7os0+yd1XvXAy23CFOmTbMaDxgxXtTKHybrJ39Y8kGzBATgBFibWJKH6BhytLI/Zyszs87xCOBNyBig6vQ==", "dependencies": {"@aws-crypto/util": "^3.0.0", "@aws-sdk/types": "^3.222.0", "tslib": "^1.11.1"}}, "node_modules/@aws-crypto/sha256-browser/node_modules/@aws-crypto/util": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/util/-/util-3.0.0.tgz", "integrity": "sha512-2OJlpeJpCR48CC8r+uKVChzs9Iungj9wkZrl8Z041DWEWvyIHILYKCPNzJghKsivj+S3mLo6BVc7mBNzdxA46w==", "dependencies": {"@aws-sdk/types": "^3.222.0", "@aws-sdk/util-utf8-browser": "^3.0.0", "tslib": "^1.11.1"}}, "node_modules/@aws-crypto/sha256-js": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/sha256-js/-/sha256-js-5.0.0.tgz", "integrity": "sha512-g+u9iKkaQVp9Mjoxq1IJSHj9NHGZF441+R/GIH0dn7u4mix5QQ4VqgpppHrNm1LzjUzb0BpcFGsBXP6cOVf+ZQ==", "dependencies": {"@aws-crypto/util": "^5.0.0", "@aws-sdk/types": "^3.222.0", "tslib": "^1.11.1"}, "engines": {"node": ">=16.0.0"}}, "node_modules/@aws-crypto/supports-web-crypto": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/supports-web-crypto/-/supports-web-crypto-3.0.0.tgz", "integrity": "sha512-06hBdMwUAb2WFTuGG73LSC0wfPu93xWwo5vL2et9eymgmu3Id5vFAHBbajVWiGhPO37qcsdCap/FqXvJGJWPIg==", "dependencies": {"tslib": "^1.11.1"}}, "node_modules/@aws-crypto/util": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/util/-/util-5.0.0.tgz", "integrity": "sha512-1GYqLdYRe96idcCltlqxdJ68OWE6ADT8qGLmVi7PVHKl8AxD2EWSbJSSevPq2eTx6vaPZpkr1RoZ3lcw/uGoEA==", "dependencies": {"@aws-sdk/types": "^3.222.0", "@aws-sdk/util-utf8-browser": "^3.0.0", "tslib": "^1.11.1"}}, "node_modules/@aws-sdk/client-elasticsearch-service": {"version": "3.414.0", "resolved": "https://registry.npmjs.org/@aws-sdk/client-elasticsearch-service/-/client-elasticsearch-service-3.414.0.tgz", "integrity": "sha512-Yx8vMIWXYCHb1j+yTBCNzF4s6QbBhUoe73WzN94UYzGSP2Itnn3GnWF9VJTN7yCSrztibdMvhG+dF2zmwJhIJw==", "dependencies": {"@aws-crypto/sha256-browser": "3.0.0", "@aws-crypto/sha256-js": "3.0.0", "@aws-sdk/client-sts": "3.414.0", "@aws-sdk/credential-provider-node": "3.414.0", "@aws-sdk/middleware-host-header": "3.413.0", "@aws-sdk/middleware-logger": "3.413.0", "@aws-sdk/middleware-recursion-detection": "3.413.0", "@aws-sdk/middleware-signing": "3.413.0", "@aws-sdk/middleware-user-agent": "3.413.0", "@aws-sdk/region-config-resolver": "3.413.0", "@aws-sdk/types": "3.413.0", "@aws-sdk/util-endpoints": "3.413.0", "@aws-sdk/util-user-agent-browser": "3.413.0", "@aws-sdk/util-user-agent-node": "3.413.0", "@smithy/config-resolver": "^2.0.8", "@smithy/fetch-http-handler": "^2.1.3", "@smithy/hash-node": "^2.0.7", "@smithy/invalid-dependency": "^2.0.7", "@smithy/middleware-content-length": "^2.0.9", "@smithy/middleware-endpoint": "^2.0.7", "@smithy/middleware-retry": "^2.0.10", "@smithy/middleware-serde": "^2.0.7", "@smithy/middleware-stack": "^2.0.0", "@smithy/node-config-provider": "^2.0.10", "@smithy/node-http-handler": "^2.1.3", "@smithy/protocol-http": "^3.0.3", "@smithy/smithy-client": "^2.1.4", "@smithy/types": "^2.3.1", "@smithy/url-parser": "^2.0.7", "@smithy/util-base64": "^2.0.0", "@smithy/util-body-length-browser": "^2.0.0", "@smithy/util-body-length-node": "^2.1.0", "@smithy/util-defaults-mode-browser": "^2.0.8", "@smithy/util-defaults-mode-node": "^2.0.10", "@smithy/util-retry": "^2.0.0", "@smithy/util-utf8": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/client-elasticsearch-service/node_modules/@aws-crypto/sha256-js": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/sha256-js/-/sha256-js-3.0.0.tgz", "integrity": "sha512-PnNN7os0+yd1XvXAy23CFOmTbMaDxgxXtTKHybrJ39Y8kGzBATgBFibWJKH6BhytLI/Zyszs87xCOBNyBig6vQ==", "dependencies": {"@aws-crypto/util": "^3.0.0", "@aws-sdk/types": "^3.222.0", "tslib": "^1.11.1"}}, "node_modules/@aws-sdk/client-elasticsearch-service/node_modules/@aws-crypto/sha256-js/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "node_modules/@aws-sdk/client-elasticsearch-service/node_modules/@aws-crypto/util": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/util/-/util-3.0.0.tgz", "integrity": "sha512-2OJlpeJpCR48CC8r+uKVChzs9Iungj9wkZrl8Z041DWEWvyIHILYKCPNzJghKsivj+S3mLo6BVc7mBNzdxA46w==", "dependencies": {"@aws-sdk/types": "^3.222.0", "@aws-sdk/util-utf8-browser": "^3.0.0", "tslib": "^1.11.1"}}, "node_modules/@aws-sdk/client-elasticsearch-service/node_modules/@aws-crypto/util/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "node_modules/@aws-sdk/client-elasticsearch-service/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@aws-sdk/client-sso": {"version": "3.414.0", "resolved": "https://registry.npmjs.org/@aws-sdk/client-sso/-/client-sso-3.414.0.tgz", "integrity": "sha512-GvRwQ7wA3edzsQEKS70ZPhkOUZ62PAiXasjp6GxrsADEb8sV1z4FxXNl9Un/7fQxKkh9QYaK1Wu1PmhLi9MLMg==", "dependencies": {"@aws-crypto/sha256-browser": "3.0.0", "@aws-crypto/sha256-js": "3.0.0", "@aws-sdk/middleware-host-header": "3.413.0", "@aws-sdk/middleware-logger": "3.413.0", "@aws-sdk/middleware-recursion-detection": "3.413.0", "@aws-sdk/middleware-user-agent": "3.413.0", "@aws-sdk/region-config-resolver": "3.413.0", "@aws-sdk/types": "3.413.0", "@aws-sdk/util-endpoints": "3.413.0", "@aws-sdk/util-user-agent-browser": "3.413.0", "@aws-sdk/util-user-agent-node": "3.413.0", "@smithy/config-resolver": "^2.0.8", "@smithy/fetch-http-handler": "^2.1.3", "@smithy/hash-node": "^2.0.7", "@smithy/invalid-dependency": "^2.0.7", "@smithy/middleware-content-length": "^2.0.9", "@smithy/middleware-endpoint": "^2.0.7", "@smithy/middleware-retry": "^2.0.10", "@smithy/middleware-serde": "^2.0.7", "@smithy/middleware-stack": "^2.0.0", "@smithy/node-config-provider": "^2.0.10", "@smithy/node-http-handler": "^2.1.3", "@smithy/protocol-http": "^3.0.3", "@smithy/smithy-client": "^2.1.4", "@smithy/types": "^2.3.1", "@smithy/url-parser": "^2.0.7", "@smithy/util-base64": "^2.0.0", "@smithy/util-body-length-browser": "^2.0.0", "@smithy/util-body-length-node": "^2.1.0", "@smithy/util-defaults-mode-browser": "^2.0.8", "@smithy/util-defaults-mode-node": "^2.0.10", "@smithy/util-retry": "^2.0.0", "@smithy/util-utf8": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/client-sso/node_modules/@aws-crypto/sha256-js": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/sha256-js/-/sha256-js-3.0.0.tgz", "integrity": "sha512-PnNN7os0+yd1XvXAy23CFOmTbMaDxgxXtTKHybrJ39Y8kGzBATgBFibWJKH6BhytLI/Zyszs87xCOBNyBig6vQ==", "dependencies": {"@aws-crypto/util": "^3.0.0", "@aws-sdk/types": "^3.222.0", "tslib": "^1.11.1"}}, "node_modules/@aws-sdk/client-sso/node_modules/@aws-crypto/sha256-js/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "node_modules/@aws-sdk/client-sso/node_modules/@aws-crypto/util": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/util/-/util-3.0.0.tgz", "integrity": "sha512-2OJlpeJpCR48CC8r+uKVChzs9Iungj9wkZrl8Z041DWEWvyIHILYKCPNzJghKsivj+S3mLo6BVc7mBNzdxA46w==", "dependencies": {"@aws-sdk/types": "^3.222.0", "@aws-sdk/util-utf8-browser": "^3.0.0", "tslib": "^1.11.1"}}, "node_modules/@aws-sdk/client-sso/node_modules/@aws-crypto/util/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "node_modules/@aws-sdk/client-sso/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@aws-sdk/client-sts": {"version": "3.414.0", "resolved": "https://registry.npmjs.org/@aws-sdk/client-sts/-/client-sts-3.414.0.tgz", "integrity": "sha512-xeYH3si6Imp1EWolWn1zuxJJu2AXKwXl1HDftQULwC5AWkm1mNFbXYSJN4hQul1IM+kn+JTRB0XRHByQkKhe+Q==", "dependencies": {"@aws-crypto/sha256-browser": "3.0.0", "@aws-crypto/sha256-js": "3.0.0", "@aws-sdk/credential-provider-node": "3.414.0", "@aws-sdk/middleware-host-header": "3.413.0", "@aws-sdk/middleware-logger": "3.413.0", "@aws-sdk/middleware-recursion-detection": "3.413.0", "@aws-sdk/middleware-sdk-sts": "3.413.0", "@aws-sdk/middleware-signing": "3.413.0", "@aws-sdk/middleware-user-agent": "3.413.0", "@aws-sdk/region-config-resolver": "3.413.0", "@aws-sdk/types": "3.413.0", "@aws-sdk/util-endpoints": "3.413.0", "@aws-sdk/util-user-agent-browser": "3.413.0", "@aws-sdk/util-user-agent-node": "3.413.0", "@smithy/config-resolver": "^2.0.8", "@smithy/fetch-http-handler": "^2.1.3", "@smithy/hash-node": "^2.0.7", "@smithy/invalid-dependency": "^2.0.7", "@smithy/middleware-content-length": "^2.0.9", "@smithy/middleware-endpoint": "^2.0.7", "@smithy/middleware-retry": "^2.0.10", "@smithy/middleware-serde": "^2.0.7", "@smithy/middleware-stack": "^2.0.0", "@smithy/node-config-provider": "^2.0.10", "@smithy/node-http-handler": "^2.1.3", "@smithy/protocol-http": "^3.0.3", "@smithy/smithy-client": "^2.1.4", "@smithy/types": "^2.3.1", "@smithy/url-parser": "^2.0.7", "@smithy/util-base64": "^2.0.0", "@smithy/util-body-length-browser": "^2.0.0", "@smithy/util-body-length-node": "^2.1.0", "@smithy/util-defaults-mode-browser": "^2.0.8", "@smithy/util-defaults-mode-node": "^2.0.10", "@smithy/util-retry": "^2.0.0", "@smithy/util-utf8": "^2.0.0", "fast-xml-parser": "4.2.5", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/client-sts/node_modules/@aws-crypto/sha256-js": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/sha256-js/-/sha256-js-3.0.0.tgz", "integrity": "sha512-PnNN7os0+yd1XvXAy23CFOmTbMaDxgxXtTKHybrJ39Y8kGzBATgBFibWJKH6BhytLI/Zyszs87xCOBNyBig6vQ==", "dependencies": {"@aws-crypto/util": "^3.0.0", "@aws-sdk/types": "^3.222.0", "tslib": "^1.11.1"}}, "node_modules/@aws-sdk/client-sts/node_modules/@aws-crypto/sha256-js/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "node_modules/@aws-sdk/client-sts/node_modules/@aws-crypto/util": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/util/-/util-3.0.0.tgz", "integrity": "sha512-2OJlpeJpCR48CC8r+uKVChzs9Iungj9wkZrl8Z041DWEWvyIHILYKCPNzJghKsivj+S3mLo6BVc7mBNzdxA46w==", "dependencies": {"@aws-sdk/types": "^3.222.0", "@aws-sdk/util-utf8-browser": "^3.0.0", "tslib": "^1.11.1"}}, "node_modules/@aws-sdk/client-sts/node_modules/@aws-crypto/util/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "node_modules/@aws-sdk/client-sts/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@aws-sdk/credential-provider-env": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-env/-/credential-provider-env-3.413.0.tgz", "integrity": "sha512-yeMOkfG20/RlzfPMtQuDB647AcPEvFEVYOWZzAWVJfldYQ5ybKr0d7sBkgG9sdAzGkK3Aw9dE4rigYI8EIqc1Q==", "dependencies": {"@aws-sdk/types": "3.413.0", "@smithy/property-provider": "^2.0.0", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/credential-provider-env/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@aws-sdk/credential-provider-ini": {"version": "3.414.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-ini/-/credential-provider-ini-3.414.0.tgz", "integrity": "sha512-rlpLLx70roJL/t40opWC96LbIASejdMbRlgSCRpK8b/hKngYDe5A7SRVacaw08vYrAywxRiybxpQOwOt9b++rA==", "dependencies": {"@aws-sdk/credential-provider-env": "3.413.0", "@aws-sdk/credential-provider-process": "3.413.0", "@aws-sdk/credential-provider-sso": "3.414.0", "@aws-sdk/credential-provider-web-identity": "3.413.0", "@aws-sdk/types": "3.413.0", "@smithy/credential-provider-imds": "^2.0.0", "@smithy/property-provider": "^2.0.0", "@smithy/shared-ini-file-loader": "^2.0.6", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/credential-provider-ini/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@aws-sdk/credential-provider-node": {"version": "3.414.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-node/-/credential-provider-node-3.414.0.tgz", "integrity": "sha512-xlkcOUKeGHInxWKKrZKIPSBCUL/ozyCldJBjmMKEj7ZmBAEiDcjpMe3pZ//LibMkCSy0b/7jtyQBE/eaIT2o0A==", "dependencies": {"@aws-sdk/credential-provider-env": "3.413.0", "@aws-sdk/credential-provider-ini": "3.414.0", "@aws-sdk/credential-provider-process": "3.413.0", "@aws-sdk/credential-provider-sso": "3.414.0", "@aws-sdk/credential-provider-web-identity": "3.413.0", "@aws-sdk/types": "3.413.0", "@smithy/credential-provider-imds": "^2.0.0", "@smithy/property-provider": "^2.0.0", "@smithy/shared-ini-file-loader": "^2.0.6", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/credential-provider-node/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@aws-sdk/credential-provider-process": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-process/-/credential-provider-process-3.413.0.tgz", "integrity": "sha512-GFJdgS14GzJ1wc2DEnS44Z/34iBZ05CAkvDsLN2CMwcDgH4eZuif9/x0lwzIJBK3xVFHzYUeVvEzsqRPbCHRsw==", "dependencies": {"@aws-sdk/types": "3.413.0", "@smithy/property-provider": "^2.0.0", "@smithy/shared-ini-file-loader": "^2.0.6", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/credential-provider-process/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@aws-sdk/credential-provider-sso": {"version": "3.414.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-sso/-/credential-provider-sso-3.414.0.tgz", "integrity": "sha512-w9g2hlkZn7WekWICRqk+L33py7KrjYMFryVpkKXOx2pjDchCfZDr6pL1ml782GZ0L3qsob4SbNpbtp13JprnWQ==", "dependencies": {"@aws-sdk/client-sso": "3.414.0", "@aws-sdk/token-providers": "3.413.0", "@aws-sdk/types": "3.413.0", "@smithy/property-provider": "^2.0.0", "@smithy/shared-ini-file-loader": "^2.0.6", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/credential-provider-sso/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@aws-sdk/credential-provider-web-identity": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-web-identity/-/credential-provider-web-identity-3.413.0.tgz", "integrity": "sha512-5cdA1Iq9JeEHtg59ERV9fdMQ7cS0JF6gH/BWA7HYEUGdSVPXCuwyEggPtG64QgpNU7SmxH+QdDG+Ldxz09ycIA==", "dependencies": {"@aws-sdk/types": "3.413.0", "@smithy/property-provider": "^2.0.0", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/credential-provider-web-identity/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@aws-sdk/middleware-host-header": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-host-header/-/middleware-host-header-3.413.0.tgz", "integrity": "sha512-r9PQx468EzPHo9wRzZLfgROpKtVdbkteMrdhsuM12bifVHjU1OHr7yfhc1OdWv39X8Xiv6F8n5r+RBQEM0S6+g==", "dependencies": {"@aws-sdk/types": "3.413.0", "@smithy/protocol-http": "^3.0.3", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-host-header/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@aws-sdk/middleware-logger": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-logger/-/middleware-logger-3.413.0.tgz", "integrity": "sha512-jqcXDubcKvoqBy+kkEa0WoNjG6SveDeyNy+gdGnTV+DEtYjkcHrHJei4q0W5zFl0mzc+dP+z8tJF44rv95ZY3Q==", "dependencies": {"@aws-sdk/types": "3.413.0", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-logger/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@aws-sdk/middleware-recursion-detection": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-recursion-detection/-/middleware-recursion-detection-3.413.0.tgz", "integrity": "sha512-C6k0IKJk/A4/VBGwUjxEPG+WOjjnmWAZVRBUzaeM7PqRh+g5rLcuIV356ntV3pREVxyiSTePTYVYIHU9YXkLKQ==", "dependencies": {"@aws-sdk/types": "3.413.0", "@smithy/protocol-http": "^3.0.3", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-recursion-detection/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@aws-sdk/middleware-sdk-sts": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-sdk-sts/-/middleware-sdk-sts-3.413.0.tgz", "integrity": "sha512-t0u//JUyaEZRVnH5q+Ur3tWnuyIsTdwA0XOdDCZXcSlLYzGp2MI/tScLjn9IydRrceIFpFfmbjk4Nf/Q6TeBTQ==", "dependencies": {"@aws-sdk/middleware-signing": "3.413.0", "@aws-sdk/types": "3.413.0", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-sdk-sts/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@aws-sdk/middleware-signing": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-signing/-/middleware-signing-3.413.0.tgz", "integrity": "sha512-QFEnVvIKYPCermM+ESxEztgUgXzGSKpnPnohMYNvSZySqmOLu/4VvxiZbRO/BX9J3ZHcUgaw4vKm5VBZRrycxw==", "dependencies": {"@aws-sdk/types": "3.413.0", "@smithy/property-provider": "^2.0.0", "@smithy/protocol-http": "^3.0.3", "@smithy/signature-v4": "^2.0.0", "@smithy/types": "^2.3.1", "@smithy/util-middleware": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-signing/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@aws-sdk/middleware-user-agent": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-user-agent/-/middleware-user-agent-3.413.0.tgz", "integrity": "sha512-eVMJyeWxNBqerhfD+sE9sTjDtwQiECrfU6wpUQP5fGPhJD2cVVZPxuTuJGDZCu/4k/V61dF85IYlsPUNLdVQ6w==", "dependencies": {"@aws-sdk/types": "3.413.0", "@aws-sdk/util-endpoints": "3.413.0", "@smithy/protocol-http": "^3.0.3", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/middleware-user-agent/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@aws-sdk/node-http-handler": {"version": "3.374.0", "resolved": "https://registry.npmjs.org/@aws-sdk/node-http-handler/-/node-http-handler-3.374.0.tgz", "integrity": "sha512-v1Z6m0wwkf65/tKuhwrtPRqVoOtNkDTRn2MBMtxCwEw+8V8Q+YRFqVgGN+J1n53ktE0G5OYVBux/NHiAjJHReQ==", "deprecated": "This package has moved to @smithy/node-http-handler", "dependencies": {"@smithy/node-http-handler": "^1.0.2", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/node-http-handler/node_modules/@smithy/abort-controller": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@smithy/abort-controller/-/abort-controller-1.1.0.tgz", "integrity": "sha512-5imgGUlZL4dW4YWdMYAKLmal9ny/tlenM81QZY7xYyb76z9Z/QOg7oM5Ak9HQl8QfFTlGVWwcMXl+54jroRgEQ==", "dependencies": {"@smithy/types": "^1.2.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/node-http-handler/node_modules/@smithy/node-http-handler": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@smithy/node-http-handler/-/node-http-handler-1.1.0.tgz", "integrity": "sha512-d3kRriEgaIiGXLziAM8bjnaLn1fthCJeTLZIwEIpzQqe6yPX0a+yQoLCTyjb2fvdLwkMoG4p7THIIB5cj5lkbg==", "dependencies": {"@smithy/abort-controller": "^1.1.0", "@smithy/protocol-http": "^1.2.0", "@smithy/querystring-builder": "^1.1.0", "@smithy/types": "^1.2.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/node-http-handler/node_modules/@smithy/protocol-http": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/@smithy/protocol-http/-/protocol-http-1.2.0.tgz", "integrity": "sha512-GfGfruksi3nXdFok5RhgtOnWe5f6BndzYfmEXISD+5gAGdayFGpjWu5pIqIweTudMtse20bGbc+7MFZXT1Tb8Q==", "dependencies": {"@smithy/types": "^1.2.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/node-http-handler/node_modules/@smithy/querystring-builder": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@smithy/querystring-builder/-/querystring-builder-1.1.0.tgz", "integrity": "sha512-gDEi4LxIGLbdfjrjiY45QNbuDmpkwh9DX4xzrR2AzjjXpxwGyfSpbJaYhXARw9p17VH0h9UewnNQXNwaQyYMDA==", "dependencies": {"@smithy/types": "^1.2.0", "@smithy/util-uri-escape": "^1.1.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/node-http-handler/node_modules/@smithy/types": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/@smithy/types/-/types-1.2.0.tgz", "integrity": "sha512-z1r00TvBqF3dh4aHhya7nz1HhvCg4TRmw51fjMrh5do3h+ngSstt/yKlNbHeb9QxJmFbmN8KEVSWgb1bRvfEoA==", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/node-http-handler/node_modules/@smithy/util-uri-escape": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@smithy/util-uri-escape/-/util-uri-escape-1.1.0.tgz", "integrity": "sha512-/jL/V1xdVRt5XppwiaEU8Etp5WHZj609n0xMTuehmCqdoOFbId1M+aEeDWZsQ+8JbEB/BJ6ynY2SlYmOaKtt8w==", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/node-http-handler/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@aws-sdk/protocol-http": {"version": "3.374.0", "resolved": "https://registry.npmjs.org/@aws-sdk/protocol-http/-/protocol-http-3.374.0.tgz", "integrity": "sha512-9WpRUbINdGroV3HiZZIBoJvL2ndoWk39OfwxWs2otxByppJZNN14bg/lvCx5e8ggHUti7IBk5rb0nqQZ4m05pg==", "deprecated": "This package has moved to @smithy/protocol-http", "dependencies": {"@smithy/protocol-http": "^1.1.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/protocol-http/node_modules/@smithy/protocol-http": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/@smithy/protocol-http/-/protocol-http-1.2.0.tgz", "integrity": "sha512-GfGfruksi3nXdFok5RhgtOnWe5f6BndzYfmEXISD+5gAGdayFGpjWu5pIqIweTudMtse20bGbc+7MFZXT1Tb8Q==", "dependencies": {"@smithy/types": "^1.2.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/protocol-http/node_modules/@smithy/types": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/@smithy/types/-/types-1.2.0.tgz", "integrity": "sha512-z1r00TvBqF3dh4aHhya7nz1HhvCg4TRmw51fjMrh5do3h+ngSstt/yKlNbHeb9QxJmFbmN8KEVSWgb1bRvfEoA==", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/protocol-http/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@aws-sdk/region-config-resolver": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/region-config-resolver/-/region-config-resolver-3.413.0.tgz", "integrity": "sha512-h90e6yyOhvoc+1F5vFk3C5mxwB8RSDEMKTO/fxexyur94seczZ1yxyYkTMZv30oc9RUiToABlHNrh/wxL7TZPQ==", "dependencies": {"@smithy/node-config-provider": "^2.0.10", "@smithy/types": "^2.3.1", "@smithy/util-config-provider": "^2.0.0", "@smithy/util-middleware": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/region-config-resolver/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@aws-sdk/signature-v4": {"version": "3.374.0", "resolved": "https://registry.npmjs.org/@aws-sdk/signature-v4/-/signature-v4-3.374.0.tgz", "integrity": "sha512-2xLJvSdzcZZAg0lsDLUAuSQuihzK0dcxIK7WmfuJeF7DGKJFmp9czQmz5f3qiDz6IDQzvgK1M9vtJSVCslJbyQ==", "deprecated": "This package has moved to @smithy/signature-v4", "dependencies": {"@smithy/signature-v4": "^1.0.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/signature-v4/node_modules/@smithy/eventstream-codec": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@smithy/eventstream-codec/-/eventstream-codec-1.1.0.tgz", "integrity": "sha512-3tEbUb8t8an226jKB6V/Q2XU/J53lCwCzULuBPEaF4JjSh+FlCMp7TmogE/Aij5J9DwlsZ4VAD/IRDuQ/0ZtMw==", "dependencies": {"@aws-crypto/crc32": "3.0.0", "@smithy/types": "^1.2.0", "@smithy/util-hex-encoding": "^1.1.0", "tslib": "^2.5.0"}}, "node_modules/@aws-sdk/signature-v4/node_modules/@smithy/is-array-buffer": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@smithy/is-array-buffer/-/is-array-buffer-1.1.0.tgz", "integrity": "sha512-twpQ/n+3OWZJ7Z+xu43MJErmhB/WO/mMTnqR6PwWQShvSJ/emx5d1N59LQZk6ZpTAeuRWrc+eHhkzTp9NFjNRQ==", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/signature-v4/node_modules/@smithy/signature-v4": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@smithy/signature-v4/-/signature-v4-1.1.0.tgz", "integrity": "sha512-fDo3m7YqXBs7neciOePPd/X9LPm5QLlDMdIC4m1H6dgNLnXfLMFNIxEfPyohGA8VW9Wn4X8lygnPSGxDZSmp0Q==", "dependencies": {"@smithy/eventstream-codec": "^1.1.0", "@smithy/is-array-buffer": "^1.1.0", "@smithy/types": "^1.2.0", "@smithy/util-hex-encoding": "^1.1.0", "@smithy/util-middleware": "^1.1.0", "@smithy/util-uri-escape": "^1.1.0", "@smithy/util-utf8": "^1.1.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/signature-v4/node_modules/@smithy/types": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/@smithy/types/-/types-1.2.0.tgz", "integrity": "sha512-z1r00TvBqF3dh4aHhya7nz1HhvCg4TRmw51fjMrh5do3h+ngSstt/yKlNbHeb9QxJmFbmN8KEVSWgb1bRvfEoA==", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/signature-v4/node_modules/@smithy/util-buffer-from": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@smithy/util-buffer-from/-/util-buffer-from-1.1.0.tgz", "integrity": "sha512-9m6NXE0ww+ra5HKHCHig20T+FAwxBAm7DIdwc/767uGWbRcY720ybgPacQNB96JMOI7xVr/CDa3oMzKmW4a+kw==", "dependencies": {"@smithy/is-array-buffer": "^1.1.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/signature-v4/node_modules/@smithy/util-hex-encoding": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@smithy/util-hex-encoding/-/util-hex-encoding-1.1.0.tgz", "integrity": "sha512-7UtIE9eH0u41zpB60Jzr0oNCQ3hMJUabMcKRUVjmyHTXiWDE4vjSqN6qlih7rCNeKGbioS7f/y2Jgym4QZcKFg==", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/signature-v4/node_modules/@smithy/util-middleware": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@smithy/util-middleware/-/util-middleware-1.1.0.tgz", "integrity": "sha512-6hhckcBqVgjWAqLy2vqlPZ3rfxLDhFWEmM7oLh2POGvsi7j0tHkbN7w4DFhuBExVJAbJ/qqxqZdRY6Fu7/OezQ==", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/signature-v4/node_modules/@smithy/util-uri-escape": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@smithy/util-uri-escape/-/util-uri-escape-1.1.0.tgz", "integrity": "sha512-/jL/V1xdVRt5XppwiaEU8Etp5WHZj609n0xMTuehmCqdoOFbId1M+aEeDWZsQ+8JbEB/BJ6ynY2SlYmOaKtt8w==", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/signature-v4/node_modules/@smithy/util-utf8": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@smithy/util-utf8/-/util-utf8-1.1.0.tgz", "integrity": "sha512-p/MYV+JmqmPyjdgyN2UxAeYDj9cBqCjp0C/NsTWnnjoZUVqoeZ6IrW915L9CAKWVECgv9lVQGc4u/yz26/bI1A==", "dependencies": {"@smithy/util-buffer-from": "^1.1.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/signature-v4/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@aws-sdk/token-providers": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/token-providers/-/token-providers-3.413.0.tgz", "integrity": "sha512-NfP1Ib9LAWVLMTOa/1aJwt4TRrlRrNyukCpVZGfNaMnNNEoP5Rakdbcs8KFVHe/MJzU+GdKVzxQ4TgRkLOGTrA==", "dependencies": {"@aws-crypto/sha256-browser": "3.0.0", "@aws-crypto/sha256-js": "3.0.0", "@aws-sdk/middleware-host-header": "3.413.0", "@aws-sdk/middleware-logger": "3.413.0", "@aws-sdk/middleware-recursion-detection": "3.413.0", "@aws-sdk/middleware-user-agent": "3.413.0", "@aws-sdk/types": "3.413.0", "@aws-sdk/util-endpoints": "3.413.0", "@aws-sdk/util-user-agent-browser": "3.413.0", "@aws-sdk/util-user-agent-node": "3.413.0", "@smithy/config-resolver": "^2.0.8", "@smithy/fetch-http-handler": "^2.1.3", "@smithy/hash-node": "^2.0.7", "@smithy/invalid-dependency": "^2.0.7", "@smithy/middleware-content-length": "^2.0.9", "@smithy/middleware-endpoint": "^2.0.7", "@smithy/middleware-retry": "^2.0.10", "@smithy/middleware-serde": "^2.0.7", "@smithy/middleware-stack": "^2.0.0", "@smithy/node-config-provider": "^2.0.10", "@smithy/node-http-handler": "^2.1.3", "@smithy/property-provider": "^2.0.0", "@smithy/protocol-http": "^3.0.3", "@smithy/shared-ini-file-loader": "^2.0.6", "@smithy/smithy-client": "^2.1.4", "@smithy/types": "^2.3.1", "@smithy/url-parser": "^2.0.7", "@smithy/util-base64": "^2.0.0", "@smithy/util-body-length-browser": "^2.0.0", "@smithy/util-body-length-node": "^2.1.0", "@smithy/util-defaults-mode-browser": "^2.0.8", "@smithy/util-defaults-mode-node": "^2.0.10", "@smithy/util-retry": "^2.0.0", "@smithy/util-utf8": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/token-providers/node_modules/@aws-crypto/sha256-js": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/sha256-js/-/sha256-js-3.0.0.tgz", "integrity": "sha512-PnNN7os0+yd1XvXAy23CFOmTbMaDxgxXtTKHybrJ39Y8kGzBATgBFibWJKH6BhytLI/Zyszs87xCOBNyBig6vQ==", "dependencies": {"@aws-crypto/util": "^3.0.0", "@aws-sdk/types": "^3.222.0", "tslib": "^1.11.1"}}, "node_modules/@aws-sdk/token-providers/node_modules/@aws-crypto/sha256-js/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "node_modules/@aws-sdk/token-providers/node_modules/@aws-crypto/util": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/util/-/util-3.0.0.tgz", "integrity": "sha512-2OJlpeJpCR48CC8r+uKVChzs9Iungj9wkZrl8Z041DWEWvyIHILYKCPNzJghKsivj+S3mLo6BVc7mBNzdxA46w==", "dependencies": {"@aws-sdk/types": "^3.222.0", "@aws-sdk/util-utf8-browser": "^3.0.0", "tslib": "^1.11.1"}}, "node_modules/@aws-sdk/token-providers/node_modules/@aws-crypto/util/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "node_modules/@aws-sdk/token-providers/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@aws-sdk/types": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/types/-/types-3.413.0.tgz", "integrity": "sha512-j1xib0f/TazIFc5ySIKOlT1ujntRbaoG4LJFeEezz4ji03/wSJMI8Vi4KjzpBp8J1tTu0oRDnsxRIGixsUBeYQ==", "dependencies": {"@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/types/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@aws-sdk/util-endpoints": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-endpoints/-/util-endpoints-3.413.0.tgz", "integrity": "sha512-VAwr7cITNb1L6/2XUPIbCOuhKGm0VtKCRblurrfUF2bxqG/wtuw/2Fm4ahYJPyxklOSXAMSq+RHdFWcir0YB/g==", "dependencies": {"@aws-sdk/types": "3.413.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/util-endpoints/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@aws-sdk/util-locate-window": {"version": "3.310.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-locate-window/-/util-locate-window-3.310.0.tgz", "integrity": "sha512-qo2t/vBTnoXpjKxlsC2e1gBrRm80M3bId27r0BRB2VniSSe7bL1mmzM+/HFtujm0iAxtPM+aLEflLJlJeDPg0w==", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@aws-sdk/util-locate-window/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@aws-sdk/util-user-agent-browser": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-user-agent-browser/-/util-user-agent-browser-3.413.0.tgz", "integrity": "sha512-7j/qWcRO2OBZBre2fC6V6M0PAS9n7k6i+VtofPkkhxC2DZszLJElqnooF9hGmVGYK3zR47Np4WjURXKIEZclWg==", "dependencies": {"@aws-sdk/types": "3.413.0", "@smithy/types": "^2.3.1", "bowser": "^2.11.0", "tslib": "^2.5.0"}}, "node_modules/@aws-sdk/util-user-agent-browser/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@aws-sdk/util-user-agent-node": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-user-agent-node/-/util-user-agent-node-3.413.0.tgz", "integrity": "sha512-vHm9TVZIzfWMeDvdmoOky6VarqOt8Pr68CESHN0jyuO6XbhCDnr9rpaXiBhbSR+N1Qm7R/AfJgAhQyTMu2G1OA==", "dependencies": {"@aws-sdk/types": "3.413.0", "@smithy/node-config-provider": "^2.0.10", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"aws-crt": ">=1.0.0"}, "peerDependenciesMeta": {"aws-crt": {"optional": true}}}, "node_modules/@aws-sdk/util-user-agent-node/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@aws-sdk/util-utf8-browser": {"version": "3.259.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-utf8-browser/-/util-utf8-browser-3.259.0.tgz", "integrity": "sha512-UvFa/vR+e19XookZF8RzFZBrw2EUkQWxiBW0yYQAhvk3C+QVGl0H3ouca8LDBlBfQKXwmW3huo/59H8rwb1wJw==", "dependencies": {"tslib": "^2.3.1"}}, "node_modules/@aws-sdk/util-utf8-browser/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/abort-controller": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/@smithy/abort-controller/-/abort-controller-2.0.8.tgz", "integrity": "sha512-2SOdVj5y0zE37Y9scSXoizoxgi6mgnDabi7a/SOfhl0p+50I0rIkuJTfyAuTPDtQ7e5dD6tSZPCLB3c/YM6Zig==", "dependencies": {"@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/abort-controller/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/config-resolver": {"version": "2.0.9", "resolved": "https://registry.npmjs.org/@smithy/config-resolver/-/config-resolver-2.0.9.tgz", "integrity": "sha512-QBkGPLUqyPmis9Erz8v4q5lo/ErnF7+GD5WZHa6JZiXopUPfaaM+B21n8gzS5xCkIXZmnwzNQhObP9xQPu8oqQ==", "dependencies": {"@smithy/node-config-provider": "^2.0.11", "@smithy/types": "^2.3.2", "@smithy/util-config-provider": "^2.0.0", "@smithy/util-middleware": "^2.0.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/config-resolver/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/credential-provider-imds": {"version": "2.0.11", "resolved": "https://registry.npmjs.org/@smithy/credential-provider-imds/-/credential-provider-imds-2.0.11.tgz", "integrity": "sha512-uJJs8dnM5iXkn8a2GaKvlKMhcOJ+oJPYqY9gY3CM/EieCVObIDjxUtR/g8lU/k/A+OauA78GzScAfulmFjPOYA==", "dependencies": {"@smithy/node-config-provider": "^2.0.11", "@smithy/property-provider": "^2.0.9", "@smithy/types": "^2.3.2", "@smithy/url-parser": "^2.0.8", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/credential-provider-imds/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/eventstream-codec": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/@smithy/eventstream-codec/-/eventstream-codec-2.0.8.tgz", "integrity": "sha512-onO4to8ujCKn4m5XagReT9Nc6FlNG5vveuvjp1H7AtaG7njdet1LOl6/jmUOkskF2C/w+9jNw3r9Ak+ghOvN0A==", "dependencies": {"@aws-crypto/crc32": "3.0.0", "@smithy/types": "^2.3.2", "@smithy/util-hex-encoding": "^2.0.0", "tslib": "^2.5.0"}}, "node_modules/@smithy/eventstream-codec/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/fetch-http-handler": {"version": "2.1.4", "resolved": "https://registry.npmjs.org/@smithy/fetch-http-handler/-/fetch-http-handler-2.1.4.tgz", "integrity": "sha512-SL24M9W5ERByoXaVicRx+bj9GJVujDnPn+QO7GY7adhY0mPGa6DSF58pVKsgIh4r5Tx/k3SWCPlH4BxxSxA/fQ==", "dependencies": {"@smithy/protocol-http": "^3.0.4", "@smithy/querystring-builder": "^2.0.8", "@smithy/types": "^2.3.2", "@smithy/util-base64": "^2.0.0", "tslib": "^2.5.0"}}, "node_modules/@smithy/fetch-http-handler/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/hash-node": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/@smithy/hash-node/-/hash-node-2.0.8.tgz", "integrity": "sha512-yZL/nmxZzjZV5/QX5JWSgXlt0HxuMTwFO89CS++jOMMPiCMZngf6VYmtNdccs8IIIAMmfQeTzwu07XgUE/Zd3Q==", "dependencies": {"@smithy/types": "^2.3.2", "@smithy/util-buffer-from": "^2.0.0", "@smithy/util-utf8": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/hash-node/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/invalid-dependency": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/@smithy/invalid-dependency/-/invalid-dependency-2.0.8.tgz", "integrity": "sha512-88VOS7W3KzUz/bNRc+Sl/F/CDIasFspEE4G39YZRHIh9YmsXF7GUyVaAKURfMNulTie62ayk6BHC9O0nOBAVgQ==", "dependencies": {"@smithy/types": "^2.3.2", "tslib": "^2.5.0"}}, "node_modules/@smithy/invalid-dependency/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/is-array-buffer": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@smithy/is-array-buffer/-/is-array-buffer-2.0.0.tgz", "integrity": "sha512-z3PjFjMyZNI98JFRJi/U0nGoLWMSJlDjAW4QUX2WNZLas5C0CmVV6LJ01JI0k90l7FvpmixjWxPFmENSClQ7ug==", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/is-array-buffer/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/middleware-content-length": {"version": "2.0.10", "resolved": "https://registry.npmjs.org/@smithy/middleware-content-length/-/middleware-content-length-2.0.10.tgz", "integrity": "sha512-EGSbysyA4jH0p3xI6G0jdXoj9Iz9GUnAta6aEaHtXm3wVWtenRf80y2TeVvNkVSr5jwKOdSCjKIRI2l1A/oZLA==", "dependencies": {"@smithy/protocol-http": "^3.0.4", "@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/middleware-content-length/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/middleware-endpoint": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/@smithy/middleware-endpoint/-/middleware-endpoint-2.0.8.tgz", "integrity": "sha512-yOpogfG2d2V0cbJdAJ6GLAWkNOc9pVsL5hZUfXcxJu408N3CUCsXzIAFF6+70ZKSE+lCfG3GFErcSXv/UfUbjw==", "dependencies": {"@smithy/middleware-serde": "^2.0.8", "@smithy/types": "^2.3.2", "@smithy/url-parser": "^2.0.8", "@smithy/util-middleware": "^2.0.1", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/middleware-endpoint/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/middleware-retry": {"version": "2.0.11", "resolved": "https://registry.npmjs.org/@smithy/middleware-retry/-/middleware-retry-2.0.11.tgz", "integrity": "sha512-pknfokumZ+wvBERSuKAI2vVr+aK3ZgPiWRg6+0ZG4kKJogBRpPmDGWw+Jht0izS9ZaEbIobNzueIb4wD33JJVg==", "dependencies": {"@smithy/node-config-provider": "^2.0.11", "@smithy/protocol-http": "^3.0.4", "@smithy/service-error-classification": "^2.0.1", "@smithy/types": "^2.3.2", "@smithy/util-middleware": "^2.0.1", "@smithy/util-retry": "^2.0.1", "tslib": "^2.5.0", "uuid": "^8.3.2"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/middleware-retry/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/middleware-serde": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/@smithy/middleware-serde/-/middleware-serde-2.0.8.tgz", "integrity": "sha512-Is0sm+LiNlgsc0QpstDzifugzL9ehno1wXp109GgBgpnKTK3j+KphiparBDI4hWTtH9/7OUsxuspNqai2yyhcg==", "dependencies": {"@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/middleware-serde/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/middleware-stack": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/@smithy/middleware-stack/-/middleware-stack-2.0.1.tgz", "integrity": "sha512-UexsfY6/oQZRjTQL56s9AKtMcR60tBNibSgNYX1I2WXaUaXg97W9JCkFyth85TzBWKDBTyhLfenrukS/kyu54A==", "dependencies": {"@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/middleware-stack/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/node-config-provider": {"version": "2.0.11", "resolved": "https://registry.npmjs.org/@smithy/node-config-provider/-/node-config-provider-2.0.11.tgz", "integrity": "sha512-CaR1dciSSGKttjhcefpytYjsfI/Yd5mqL8am4wfmyFCDxSiPsvnEWHl8UjM/RbcAjX0klt+CeIKPSHEc0wGvJA==", "dependencies": {"@smithy/property-provider": "^2.0.9", "@smithy/shared-ini-file-loader": "^2.0.10", "@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/node-config-provider/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/node-http-handler": {"version": "2.1.4", "resolved": "https://registry.npmjs.org/@smithy/node-http-handler/-/node-http-handler-2.1.4.tgz", "integrity": "sha512-8Rw/AusvWDyC6SK8esAcVBeTlQHf94NMFv805suFUJCQ2gwlh0oLDNh+6s2MDOrxcjvLxjjzv1mytM0Mt+0cPQ==", "dependencies": {"@smithy/abort-controller": "^2.0.8", "@smithy/protocol-http": "^3.0.4", "@smithy/querystring-builder": "^2.0.8", "@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/node-http-handler/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/property-provider": {"version": "2.0.9", "resolved": "https://registry.npmjs.org/@smithy/property-provider/-/property-provider-2.0.9.tgz", "integrity": "sha512-25pPZ8f8DeRwYI5wbPRZaoMoR+3vrw8DwbA0TjP+GsdiB2KxScndr4HQehiJ5+WJ0giOTWhLz0bd+7Djv1qpUQ==", "dependencies": {"@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/property-provider/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/protocol-http": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/@smithy/protocol-http/-/protocol-http-3.0.4.tgz", "integrity": "sha512-CGfSWk6TRlbwa8YgrSXdn80Yu7pov3EV/h7TSfiCHhq6/LO3WymmqnzgH1f0qV2bdTDipIkTNp5dGCGN3Af/5g==", "dependencies": {"@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/protocol-http/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/querystring-builder": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/@smithy/querystring-builder/-/querystring-builder-2.0.8.tgz", "integrity": "sha512-+vzIMwjC8Saz97/ptPn+IJRCRRZ+pP95ZIWDRqEqZV/a6hiKbaFoMSa2iCKsnKzR696U2JZXrDqMu3e/FD1+2g==", "dependencies": {"@smithy/types": "^2.3.2", "@smithy/util-uri-escape": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/querystring-builder/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/querystring-parser": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/@smithy/querystring-parser/-/querystring-parser-2.0.8.tgz", "integrity": "sha512-ArbanNuR7O/MmTd90ZqhDqGOPPDYmxx3huHxD+R3cuCnazcK/1tGQA+SnnR5307T7ZRb5WTpB6qBggERuibVSA==", "dependencies": {"@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/querystring-parser/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/service-error-classification": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/@smithy/service-error-classification/-/service-error-classification-2.0.1.tgz", "integrity": "sha512-QHa9+t+v4s0cMuDCcbjIJN67mNZ42/+fc3jKe8P6ZMPXZl5ksKk6a8vhZ/m494GZng5eFTc3OePv+NF9cG83yg==", "dependencies": {"@smithy/types": "^2.3.2"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/shared-ini-file-loader": {"version": "2.0.10", "resolved": "https://registry.npmjs.org/@smithy/shared-ini-file-loader/-/shared-ini-file-loader-2.0.10.tgz", "integrity": "sha512-jWASteSezRKohJ7GdA7pHDvmr7Q7tw3b5mu3xLHIkZy/ICftJ+O7aqNaF8wklhI7UNFoQ7flFRM3Rd0KA+1BbQ==", "dependencies": {"@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/shared-ini-file-loader/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/signature-v4": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/@smithy/signature-v4/-/signature-v4-2.0.8.tgz", "integrity": "sha512-qrtiYMzaLlQ5HSJOaFwnyTQ3JLjmPY+3+pr9IBDpCVM6YtVj22cBLVB9bPOiZMIpkdI7ZRdxLBFlIjh5CO1Bhw==", "dependencies": {"@smithy/eventstream-codec": "^2.0.8", "@smithy/is-array-buffer": "^2.0.0", "@smithy/types": "^2.3.2", "@smithy/util-hex-encoding": "^2.0.0", "@smithy/util-middleware": "^2.0.1", "@smithy/util-uri-escape": "^2.0.0", "@smithy/util-utf8": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/signature-v4/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/smithy-client": {"version": "2.1.5", "resolved": "https://registry.npmjs.org/@smithy/smithy-client/-/smithy-client-2.1.5.tgz", "integrity": "sha512-7S865uKzsxApM8W8Q6zkij7tcUFgaG8PuADMFdMt1yL/ku3d0+s6Zwrg3N7iXCPM08Gu/mf0BIfTXIu/9i450Q==", "dependencies": {"@smithy/middleware-stack": "^2.0.1", "@smithy/types": "^2.3.2", "@smithy/util-stream": "^2.0.11", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/smithy-client/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/types": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/@smithy/types/-/types-2.3.2.tgz", "integrity": "sha512-iH0cdKi7HQlzfAM3w2shFk/qZYKAqJWswtpmQpPtlruF+uFZeGEpMJjgDRyhWiddfVM4e2oP4nMaOBsMy6lXgg==", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/types/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/url-parser": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/@smithy/url-parser/-/url-parser-2.0.8.tgz", "integrity": "sha512-wQw7j004ScCrBRJ+oNPXlLE9mtofxyadSZ9D8ov/rHkyurS7z1HTNuyaGRj6OvKsEk0SVQsuY0C9+EfM75XTkw==", "dependencies": {"@smithy/querystring-parser": "^2.0.8", "@smithy/types": "^2.3.2", "tslib": "^2.5.0"}}, "node_modules/@smithy/url-parser/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/util-base64": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@smithy/util-base64/-/util-base64-2.0.0.tgz", "integrity": "sha512-Zb1E4xx+m5Lud8bbeYi5FkcMJMnn+1WUnJF3qD7rAdXpaL7UjkFQLdmW5fHadoKbdHpwH9vSR8EyTJFHJs++tA==", "dependencies": {"@smithy/util-buffer-from": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/util-base64/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/util-body-length-browser": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@smithy/util-body-length-browser/-/util-body-length-browser-2.0.0.tgz", "integrity": "sha512-JdDuS4ircJt+FDnaQj88TzZY3+njZ6O+D3uakS32f2VNnDo3vyEuNdBOh/oFd8Df1zSZOuH1HEChk2AOYDezZg==", "dependencies": {"tslib": "^2.5.0"}}, "node_modules/@smithy/util-body-length-browser/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/util-body-length-node": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/@smithy/util-body-length-node/-/util-body-length-node-2.1.0.tgz", "integrity": "sha512-/li0/kj/y3fQ3vyzn36NTLGmUwAICb7Jbe/CsWCktW363gh1MOcpEcSO3mJ344Gv2dqz8YJCLQpb6hju/0qOWw==", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/util-body-length-node/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/util-buffer-from": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@smithy/util-buffer-from/-/util-buffer-from-2.0.0.tgz", "integrity": "sha512-/YNnLoHsR+4W4Vf2wL5lGv0ksg8Bmk3GEGxn2vEQt52AQaPSCuaO5PM5VM7lP1K9qHRKHwrPGktqVoAHKWHxzw==", "dependencies": {"@smithy/is-array-buffer": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/util-buffer-from/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/util-config-provider": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@smithy/util-config-provider/-/util-config-provider-2.0.0.tgz", "integrity": "sha512-xCQ6UapcIWKxXHEU4Mcs2s7LcFQRiU3XEluM2WcCjjBtQkUN71Tb+ydGmJFPxMUrW/GWMgQEEGipLym4XG0jZg==", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/util-config-provider/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/util-defaults-mode-browser": {"version": "2.0.9", "resolved": "https://registry.npmjs.org/@smithy/util-defaults-mode-browser/-/util-defaults-mode-browser-2.0.9.tgz", "integrity": "sha512-JONLJVQWT8165XoSV36ERn3SVlZLJJ4D6IeGsCSePv65Uxa93pzSLE0UMSR9Jwm4zix7rst9AS8W5QIypZWP8Q==", "dependencies": {"@smithy/property-provider": "^2.0.9", "@smithy/smithy-client": "^2.1.5", "@smithy/types": "^2.3.2", "bowser": "^2.11.0", "tslib": "^2.5.0"}, "engines": {"node": ">= 10.0.0"}}, "node_modules/@smithy/util-defaults-mode-browser/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/util-defaults-mode-node": {"version": "2.0.11", "resolved": "https://registry.npmjs.org/@smithy/util-defaults-mode-node/-/util-defaults-mode-node-2.0.11.tgz", "integrity": "sha512-tmqjNsfj+bgZN6jXBe6efZnukzILA7BUytHkzqikuRLNtR+0VVchQHvawD0w6vManh76rO81ydhioe7i4oBzuA==", "dependencies": {"@smithy/config-resolver": "^2.0.9", "@smithy/credential-provider-imds": "^2.0.11", "@smithy/node-config-provider": "^2.0.11", "@smithy/property-provider": "^2.0.9", "@smithy/smithy-client": "^2.1.5", "@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "engines": {"node": ">= 10.0.0"}}, "node_modules/@smithy/util-defaults-mode-node/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/util-hex-encoding": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@smithy/util-hex-encoding/-/util-hex-encoding-2.0.0.tgz", "integrity": "sha512-c5xY+NUnFqG6d7HFh1IFfrm3mGl29lC+vF+geHv4ToiuJCBmIfzx6IeHLg+OgRdPFKDXIw6pvi+p3CsscaMcMA==", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/util-hex-encoding/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/util-middleware": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/@smithy/util-middleware/-/util-middleware-2.0.1.tgz", "integrity": "sha512-LnsBMi0Mg3gfz/TpNGLv2Jjcz2ra1OX5HR/4IaCepIYmtPQzqMWDdhX/XTW1LS8OZ0xbQuyQPcHkQ+2XkhWOVQ==", "dependencies": {"@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/util-middleware/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/util-retry": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/@smithy/util-retry/-/util-retry-2.0.1.tgz", "integrity": "sha512-naj4X0IafJ9yJnVJ58QgSMkCNLjyQOnyrnKh/T0f+0UOUxJiT8vuFn/hS7B/pNqbo2STY7PyJ4J4f+5YqxwNtA==", "dependencies": {"@smithy/service-error-classification": "^2.0.1", "@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "engines": {"node": ">= 14.0.0"}}, "node_modules/@smithy/util-retry/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/util-stream": {"version": "2.0.11", "resolved": "https://registry.npmjs.org/@smithy/util-stream/-/util-stream-2.0.11.tgz", "integrity": "sha512-2MeWfqSpZKdmEJ+tH8CJQSgzLWhH5cmdE24X7JB0hiamXrOmswWGGuPvyj/9sQCTclo57pNxLR2p7KrP8Ahiyg==", "dependencies": {"@smithy/fetch-http-handler": "^2.1.4", "@smithy/node-http-handler": "^2.1.4", "@smithy/types": "^2.3.2", "@smithy/util-base64": "^2.0.0", "@smithy/util-buffer-from": "^2.0.0", "@smithy/util-hex-encoding": "^2.0.0", "@smithy/util-utf8": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/util-stream/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/util-uri-escape": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@smithy/util-uri-escape/-/util-uri-escape-2.0.0.tgz", "integrity": "sha512-ebkxsqinSdEooQduuk9CbKcI+wheijxEb3utGXkCoYQkJnwTnLbH1JXGimJtUkQwNQbsbuYwG2+aFVyZf5TLaw==", "dependencies": {"tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/util-uri-escape/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@smithy/util-utf8": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@smithy/util-utf8/-/util-utf8-2.0.0.tgz", "integrity": "sha512-rctU1VkziY84n5OXe3bPNpKR001ZCME2JCaBBFgtiM2hfKbHFudc/BkMuPab8hRbLd0j3vbnBTTZ1igBf0wgiQ==", "dependencies": {"@smithy/util-buffer-from": "^2.0.0", "tslib": "^2.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/@smithy/util-utf8/node_modules/tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}, "node_modules/@types/aws4": {"version": "1.11.3", "resolved": "https://registry.npmjs.org/@types/aws4/-/aws4-1.11.3.tgz", "integrity": "sha512-Ka2xKf04xZUH0N7wIYpqcNdavgfPQnaJ1T6GieZs1ydo21vao93aCbHyrA6uKXnaTXzvBcMJkgMsBfT9XvypFQ==", "dev": true, "dependencies": {"@types/node": "*"}}, "node_modules/@types/node": {"version": "20.6.2", "resolved": "https://registry.npmjs.org/@types/node/-/node-20.6.2.tgz", "integrity": "sha512-Y+/1vGBHV/cYk6OI1Na/LHzwnlNCAfU3ZNGrc1LdRe/LAIbdDPTTv/HU3M7yXN448aTVDq3eKRm2cg7iKLb8gw==", "dev": true}, "node_modules/asynckit": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==", "license": "MIT"}, "node_modules/available-typed-arrays": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.5.tgz", "integrity": "sha512-DMD0KiN46eipeziST1LPP/STfDU0sufISXmjSgvVsoU2tqxctQeASejWcfNtxYKqETM1UxQ8sp2OrSBWpHY6sw==", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/aws-sdk": {"version": "2.1459.0", "resolved": "https://registry.npmjs.org/aws-sdk/-/aws-sdk-2.1459.0.tgz", "integrity": "sha512-My45PgQYhRTh6fOeZ94ELUoXzza/6gTy0J22aK4iy0DEA+uE5gjr1VthnIwbLYNMeEqn8xwJZuNJqvi/WaUUcQ==", "dependencies": {"buffer": "4.9.2", "events": "1.1.1", "ieee754": "1.1.13", "jmespath": "0.16.0", "querystring": "0.2.0", "sax": "1.2.1", "url": "0.10.3", "util": "^0.12.4", "uuid": "8.0.0", "xml2js": "0.5.0"}, "engines": {"node": ">= 10.0.0"}}, "node_modules/aws-sdk/node_modules/uuid": {"version": "8.0.0", "resolved": "https://registry.npmjs.org/uuid/-/uuid-8.0.0.tgz", "integrity": "sha512-jOXGuXZAWdsTH7eZLtyXMqUb9EcWMGZNbL9YcGBJl4MH4nrxHmZJhEHvyLFrkxo+28uLb/NYRcStH48fnD0Vzw==", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/aws4": {"version": "1.12.0", "resolved": "https://registry.npmjs.org/aws4/-/aws4-1.12.0.tgz", "integrity": "sha512-NmWvPnx0F1SfrQbYwOi7OeaNGokp9XhzNioJ/CSBs8Qa4vxug81mhJEAVZwxXuBmYB5KDRfMq/F3RR0BIU7sWg=="}, "node_modules/axios": {"version": "1.11.0", "resolved": "https://registry.npmjs.org/axios/-/axios-1.11.0.tgz", "integrity": "sha512-1Lx3WLFQWm3ooKDYZD1eXmoGO9fxYQjrycfHFC8P0sCfQVXyROp0p9PFWBehewBOdCwHc+f/b8I0fMto5eSfwA==", "license": "MIT", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.4", "proxy-from-env": "^1.1.0"}}, "node_modules/base64-js": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/bowser": {"version": "2.11.0", "resolved": "https://registry.npmjs.org/bowser/-/bowser-2.11.0.tgz", "integrity": "sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA=="}, "node_modules/buffer": {"version": "4.9.2", "resolved": "https://registry.npmjs.org/buffer/-/buffer-4.9.2.tgz", "integrity": "sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg==", "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}}, "node_modules/call-bind": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==", "dependencies": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/combined-stream": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/dunder-proto": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz", "integrity": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-define-property": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz", "integrity": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz", "integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "integrity": "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz", "integrity": "sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/events": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/events/-/events-1.1.1.tgz", "integrity": "sha512-kEcvvCBByWXGnZy6JUlgAp2gBIUjfCAV6P6TgT1/aaQKcmuAEC4OZTV1I4EWQLz2gxZw76atuVyvHhTxvi0Flw==", "engines": {"node": ">=0.4.x"}}, "node_modules/fast-xml-parser": {"version": "4.2.5", "resolved": "https://registry.npmjs.org/fast-xml-parser/-/fast-xml-parser-4.2.5.tgz", "integrity": "sha512-B9/wizE4WngqQftFPmdaMYlXoJlJOYxGQOanC77fq9k8+Z0v5dDSVh+3glErdIROP//s/jgb7ZuxKfB8nVyo0g==", "funding": [{"type": "paypal", "url": "https://paypal.me/naturalintelligence"}, {"type": "github", "url": "https://github.com/sponsors/NaturalIntelligence"}], "dependencies": {"strnum": "^1.0.5"}, "bin": {"fxparser": "src/cli/cli.js"}}, "node_modules/follow-redirects": {"version": "1.15.11", "resolved": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.11.tgz", "integrity": "sha512-deG2P0JfjrTxl50XGCDyfI97ZGVCxIpfKYmfyrQ54n5FO/0gfIES8C/Psl6kWVDolizcaaxZJnTS0QSMxvnsBQ==", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/for-each": {"version": "0.3.3", "resolved": "https://registry.npmjs.org/for-each/-/for-each-0.3.3.tgz", "integrity": "sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==", "dependencies": {"is-callable": "^1.1.3"}}, "node_modules/form-data": {"version": "4.0.4", "resolved": "https://registry.npmjs.org/form-data/-/form-data-4.0.4.tgz", "integrity": "sha512-KrGhL9Q4zjj0kiUt5OO4Mr/A/jlI2jDYs5eHBpYHPcBEVSiipAvn2Ko2HnPe20rmcuuvMHNdZFp+4IlGTMF0Ow==", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "hasown": "^2.0.2", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "integrity": "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz", "integrity": "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/gopd": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz", "integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz", "integrity": "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz", "integrity": "sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==", "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hasown": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz", "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/ieee754": {"version": "1.1.13", "resolved": "https://registry.npmjs.org/ieee754/-/ieee754-1.1.13.tgz", "integrity": "sha512-4vf7I2LYV/HaWerSo3XmlMkp5eZ83i+/CDluXi/IGTs/O1sejBNhTtnxzmRZfvOUqj7lZjqHkeTvpgSFDlWZTg=="}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="}, "node_modules/is-arguments": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/is-arguments/-/is-arguments-1.1.1.tgz", "integrity": "sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==", "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-callable": {"version": "1.2.7", "resolved": "https://registry.npmjs.org/is-callable/-/is-callable-1.2.7.tgz", "integrity": "sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-generator-function": {"version": "1.0.10", "resolved": "https://registry.npmjs.org/is-generator-function/-/is-generator-function-1.0.10.tgz", "integrity": "sha512-jsEjy9l3yiXEQ+PsXdmBwEPcOxaXWLspKdplFUVI9vq1iZgIekeC0L167qeu86czQaxed3q/Uzuw0swL0irL8A==", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-typed-array": {"version": "1.1.12", "resolved": "https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.12.tgz", "integrity": "sha512-Z14TF2JNG8Lss5/HMqt0//T9JeHXttXy5pH/DBU4vi98ozO2btxzq9MwYDZYnKwU8nRsz/+GVFVRDq3DkVuSPg==", "dependencies": {"which-typed-array": "^1.1.11"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/isarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "integrity": "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ=="}, "node_modules/jmespath": {"version": "0.16.0", "resolved": "https://registry.npmjs.org/jmespath/-/jmespath-0.16.0.tgz", "integrity": "sha512-9FzQjJ7MATs1tSpnco1K6ayiYE3figslrXA72G2HQ/n76RzvYlofyi5QM+iX4YRs/pu3yzxlVQSST23+dMDknw==", "engines": {"node": ">= 0.6.0"}}, "node_modules/math-intrinsics": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "integrity": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/mime-db": {"version": "1.52.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/proxy-from-env": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "integrity": "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==", "license": "MIT"}, "node_modules/punycode": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/punycode/-/punycode-1.3.2.tgz", "integrity": "sha512-RofWgt/7fL5wP1Y7fxE7/EmTLzQVnB0ycyibJ0OOHIlJqTNzglYFxVwETOcIoJqJmpDXJ9xImDv+Fq34F/d4Dw=="}, "node_modules/querystring": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/querystring/-/querystring-0.2.0.tgz", "integrity": "sha512-X/xY82scca2tau62i9mDyU9K+I+djTMUsvwf7xnUX5GLvVzgJybOJf4Y6o9Zx3oJK/LSXg5tTZBjwzqVPaPO2g==", "deprecated": "The querystring API is considered Legacy. new code should use the URLSearchParams API instead.", "engines": {"node": ">=0.4.x"}}, "node_modules/sax": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/sax/-/sax-1.2.1.tgz", "integrity": "sha512-8I2a3LovHTOpm7NV5yOyO8IHqgVsfK4+UuySrXU8YXkSRX7k6hCV9b3HrkKCr3nMpgj+0bmocaJJWpvp1oc7ZA=="}, "node_modules/strnum": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/strnum/-/strnum-1.0.5.tgz", "integrity": "sha512-J8bbNyKKXl5qYcR36TIO8W3mVGVHrmmxsd5PAItGkmyzwJvybiw2IVq5nqd0i4LSNSkB/sx9VHllbfFdr9k1JA=="}, "node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "node_modules/url": {"version": "0.10.3", "resolved": "https://registry.npmjs.org/url/-/url-0.10.3.tgz", "integrity": "sha512-hzSUW2q06EqL1gKM/a+obYHLIO6ct2hwPuviqTTOcfFVc61UbfJ2Q32+uGL/HCPxKqrdGB5QUwIe7UqlDgwsOQ==", "dependencies": {"punycode": "1.3.2", "querystring": "0.2.0"}}, "node_modules/util": {"version": "0.12.5", "resolved": "https://registry.npmjs.org/util/-/util-0.12.5.tgz", "integrity": "sha512-kZf/K6hEIrWHI6XqOFUiiMa+79wE/D8Q+NCNAWclkyg3b4d2k7s0QGepNjiABc+aR3N1PAyHL7p6UcLY6LmrnA==", "dependencies": {"inherits": "^2.0.3", "is-arguments": "^1.0.4", "is-generator-function": "^1.0.7", "is-typed-array": "^1.1.3", "which-typed-array": "^1.1.2"}}, "node_modules/uuid": {"version": "8.3.2", "resolved": "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz", "integrity": "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/which-typed-array": {"version": "1.1.11", "resolved": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.11.tgz", "integrity": "sha512-qe9UWWpkeG5yzZ0tNYxDmd7vo58HDBc39mZ0xWWpolAGADdFOzkfamWLDxkOWcvHQKVmdTyQdLD4NOfjLWTKew==", "dependencies": {"available-typed-arrays": "^1.0.5", "call-bind": "^1.0.2", "for-each": "^0.3.3", "gopd": "^1.0.1", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/xml2js": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/xml2js/-/xml2js-0.5.0.tgz", "integrity": "sha512-drPFnkQJik/O+uPKpqSgr22mpuFHqKdbS835iAQrUC73L2F5WkboIRd63ai/2Yg6I1jzifPFKH2NTK+cfglkIA==", "dependencies": {"sax": ">=0.6.0", "xmlbuilder": "~11.0.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/xmlbuilder": {"version": "11.0.1", "resolved": "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-11.0.1.tgz", "integrity": "sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA==", "engines": {"node": ">=4.0"}}}, "dependencies": {"@aws-crypto/crc32": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/crc32/-/crc32-3.0.0.tgz", "integrity": "sha512-IzSgsrxUcsrejQbPVilIKy16kAT52EwB6zSaI+M3xxIhKh5+aldEyvI+z6erM7TCLB2BJsFrtHjp6/4/sr+3dA==", "requires": {"@aws-crypto/util": "^3.0.0", "@aws-sdk/types": "^3.222.0", "tslib": "^1.11.1"}, "dependencies": {"@aws-crypto/util": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/util/-/util-3.0.0.tgz", "integrity": "sha512-2OJlpeJpCR48CC8r+uKVChzs9Iungj9wkZrl8Z041DWEWvyIHILYKCPNzJghKsivj+S3mLo6BVc7mBNzdxA46w==", "requires": {"@aws-sdk/types": "^3.222.0", "@aws-sdk/util-utf8-browser": "^3.0.0", "tslib": "^1.11.1"}}}}, "@aws-crypto/ie11-detection": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/ie11-detection/-/ie11-detection-3.0.0.tgz", "integrity": "sha512-341lBBkiY1DfDNKai/wXM3aujNBkXR7tq1URPQDL9wi3AUbI80NR74uF1TXHMm7po1AcnFk8iu2S2IeU/+/A+Q==", "requires": {"tslib": "^1.11.1"}}, "@aws-crypto/sha256-browser": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/sha256-browser/-/sha256-browser-3.0.0.tgz", "integrity": "sha512-8VLmW2B+gjFbU5uMeqtQM6Nj0/F1bro80xQXCW6CQBWgosFWXTx77aeOF5CAIAmbOK64SdMBJdNr6J41yP5mvQ==", "requires": {"@aws-crypto/ie11-detection": "^3.0.0", "@aws-crypto/sha256-js": "^3.0.0", "@aws-crypto/supports-web-crypto": "^3.0.0", "@aws-crypto/util": "^3.0.0", "@aws-sdk/types": "^3.222.0", "@aws-sdk/util-locate-window": "^3.0.0", "@aws-sdk/util-utf8-browser": "^3.0.0", "tslib": "^1.11.1"}, "dependencies": {"@aws-crypto/sha256-js": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/sha256-js/-/sha256-js-3.0.0.tgz", "integrity": "sha512-PnNN7os0+yd1XvXAy23CFOmTbMaDxgxXtTKHybrJ39Y8kGzBATgBFibWJKH6BhytLI/Zyszs87xCOBNyBig6vQ==", "requires": {"@aws-crypto/util": "^3.0.0", "@aws-sdk/types": "^3.222.0", "tslib": "^1.11.1"}}, "@aws-crypto/util": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/util/-/util-3.0.0.tgz", "integrity": "sha512-2OJlpeJpCR48CC8r+uKVChzs9Iungj9wkZrl8Z041DWEWvyIHILYKCPNzJghKsivj+S3mLo6BVc7mBNzdxA46w==", "requires": {"@aws-sdk/types": "^3.222.0", "@aws-sdk/util-utf8-browser": "^3.0.0", "tslib": "^1.11.1"}}}}, "@aws-crypto/sha256-js": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/sha256-js/-/sha256-js-5.0.0.tgz", "integrity": "sha512-g+u9iKkaQVp9Mjoxq1IJSHj9NHGZF441+R/GIH0dn7u4mix5QQ4VqgpppHrNm1LzjUzb0BpcFGsBXP6cOVf+ZQ==", "requires": {"@aws-crypto/util": "^5.0.0", "@aws-sdk/types": "^3.222.0", "tslib": "^1.11.1"}}, "@aws-crypto/supports-web-crypto": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/supports-web-crypto/-/supports-web-crypto-3.0.0.tgz", "integrity": "sha512-06hBdMwUAb2WFTuGG73LSC0wfPu93xWwo5vL2et9eymgmu3Id5vFAHBbajVWiGhPO37qcsdCap/FqXvJGJWPIg==", "requires": {"tslib": "^1.11.1"}}, "@aws-crypto/util": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/util/-/util-5.0.0.tgz", "integrity": "sha512-1GYqLdYRe96idcCltlqxdJ68OWE6ADT8qGLmVi7PVHKl8AxD2EWSbJSSevPq2eTx6vaPZpkr1RoZ3lcw/uGoEA==", "requires": {"@aws-sdk/types": "^3.222.0", "@aws-sdk/util-utf8-browser": "^3.0.0", "tslib": "^1.11.1"}}, "@aws-sdk/client-elasticsearch-service": {"version": "3.414.0", "resolved": "https://registry.npmjs.org/@aws-sdk/client-elasticsearch-service/-/client-elasticsearch-service-3.414.0.tgz", "integrity": "sha512-Yx8vMIWXYCHb1j+yTBCNzF4s6QbBhUoe73WzN94UYzGSP2Itnn3GnWF9VJTN7yCSrztibdMvhG+dF2zmwJhIJw==", "requires": {"@aws-crypto/sha256-browser": "3.0.0", "@aws-crypto/sha256-js": "3.0.0", "@aws-sdk/client-sts": "3.414.0", "@aws-sdk/credential-provider-node": "3.414.0", "@aws-sdk/middleware-host-header": "3.413.0", "@aws-sdk/middleware-logger": "3.413.0", "@aws-sdk/middleware-recursion-detection": "3.413.0", "@aws-sdk/middleware-signing": "3.413.0", "@aws-sdk/middleware-user-agent": "3.413.0", "@aws-sdk/region-config-resolver": "3.413.0", "@aws-sdk/types": "3.413.0", "@aws-sdk/util-endpoints": "3.413.0", "@aws-sdk/util-user-agent-browser": "3.413.0", "@aws-sdk/util-user-agent-node": "3.413.0", "@smithy/config-resolver": "^2.0.8", "@smithy/fetch-http-handler": "^2.1.3", "@smithy/hash-node": "^2.0.7", "@smithy/invalid-dependency": "^2.0.7", "@smithy/middleware-content-length": "^2.0.9", "@smithy/middleware-endpoint": "^2.0.7", "@smithy/middleware-retry": "^2.0.10", "@smithy/middleware-serde": "^2.0.7", "@smithy/middleware-stack": "^2.0.0", "@smithy/node-config-provider": "^2.0.10", "@smithy/node-http-handler": "^2.1.3", "@smithy/protocol-http": "^3.0.3", "@smithy/smithy-client": "^2.1.4", "@smithy/types": "^2.3.1", "@smithy/url-parser": "^2.0.7", "@smithy/util-base64": "^2.0.0", "@smithy/util-body-length-browser": "^2.0.0", "@smithy/util-body-length-node": "^2.1.0", "@smithy/util-defaults-mode-browser": "^2.0.8", "@smithy/util-defaults-mode-node": "^2.0.10", "@smithy/util-retry": "^2.0.0", "@smithy/util-utf8": "^2.0.0", "tslib": "^2.5.0"}, "dependencies": {"@aws-crypto/sha256-js": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/sha256-js/-/sha256-js-3.0.0.tgz", "integrity": "sha512-PnNN7os0+yd1XvXAy23CFOmTbMaDxgxXtTKHybrJ39Y8kGzBATgBFibWJKH6BhytLI/Zyszs87xCOBNyBig6vQ==", "requires": {"@aws-crypto/util": "^3.0.0", "@aws-sdk/types": "^3.222.0", "tslib": "^1.11.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}}}, "@aws-crypto/util": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/util/-/util-3.0.0.tgz", "integrity": "sha512-2OJlpeJpCR48CC8r+uKVChzs9Iungj9wkZrl8Z041DWEWvyIHILYKCPNzJghKsivj+S3mLo6BVc7mBNzdxA46w==", "requires": {"@aws-sdk/types": "^3.222.0", "@aws-sdk/util-utf8-browser": "^3.0.0", "tslib": "^1.11.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}}}, "tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@aws-sdk/client-sso": {"version": "3.414.0", "resolved": "https://registry.npmjs.org/@aws-sdk/client-sso/-/client-sso-3.414.0.tgz", "integrity": "sha512-GvRwQ7wA3edzsQEKS70ZPhkOUZ62PAiXasjp6GxrsADEb8sV1z4FxXNl9Un/7fQxKkh9QYaK1Wu1PmhLi9MLMg==", "requires": {"@aws-crypto/sha256-browser": "3.0.0", "@aws-crypto/sha256-js": "3.0.0", "@aws-sdk/middleware-host-header": "3.413.0", "@aws-sdk/middleware-logger": "3.413.0", "@aws-sdk/middleware-recursion-detection": "3.413.0", "@aws-sdk/middleware-user-agent": "3.413.0", "@aws-sdk/region-config-resolver": "3.413.0", "@aws-sdk/types": "3.413.0", "@aws-sdk/util-endpoints": "3.413.0", "@aws-sdk/util-user-agent-browser": "3.413.0", "@aws-sdk/util-user-agent-node": "3.413.0", "@smithy/config-resolver": "^2.0.8", "@smithy/fetch-http-handler": "^2.1.3", "@smithy/hash-node": "^2.0.7", "@smithy/invalid-dependency": "^2.0.7", "@smithy/middleware-content-length": "^2.0.9", "@smithy/middleware-endpoint": "^2.0.7", "@smithy/middleware-retry": "^2.0.10", "@smithy/middleware-serde": "^2.0.7", "@smithy/middleware-stack": "^2.0.0", "@smithy/node-config-provider": "^2.0.10", "@smithy/node-http-handler": "^2.1.3", "@smithy/protocol-http": "^3.0.3", "@smithy/smithy-client": "^2.1.4", "@smithy/types": "^2.3.1", "@smithy/url-parser": "^2.0.7", "@smithy/util-base64": "^2.0.0", "@smithy/util-body-length-browser": "^2.0.0", "@smithy/util-body-length-node": "^2.1.0", "@smithy/util-defaults-mode-browser": "^2.0.8", "@smithy/util-defaults-mode-node": "^2.0.10", "@smithy/util-retry": "^2.0.0", "@smithy/util-utf8": "^2.0.0", "tslib": "^2.5.0"}, "dependencies": {"@aws-crypto/sha256-js": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/sha256-js/-/sha256-js-3.0.0.tgz", "integrity": "sha512-PnNN7os0+yd1XvXAy23CFOmTbMaDxgxXtTKHybrJ39Y8kGzBATgBFibWJKH6BhytLI/Zyszs87xCOBNyBig6vQ==", "requires": {"@aws-crypto/util": "^3.0.0", "@aws-sdk/types": "^3.222.0", "tslib": "^1.11.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}}}, "@aws-crypto/util": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/util/-/util-3.0.0.tgz", "integrity": "sha512-2OJlpeJpCR48CC8r+uKVChzs9Iungj9wkZrl8Z041DWEWvyIHILYKCPNzJghKsivj+S3mLo6BVc7mBNzdxA46w==", "requires": {"@aws-sdk/types": "^3.222.0", "@aws-sdk/util-utf8-browser": "^3.0.0", "tslib": "^1.11.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}}}, "tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@aws-sdk/client-sts": {"version": "3.414.0", "resolved": "https://registry.npmjs.org/@aws-sdk/client-sts/-/client-sts-3.414.0.tgz", "integrity": "sha512-xeYH3si6Imp1EWolWn1zuxJJu2AXKwXl1HDftQULwC5AWkm1mNFbXYSJN4hQul1IM+kn+JTRB0XRHByQkKhe+Q==", "requires": {"@aws-crypto/sha256-browser": "3.0.0", "@aws-crypto/sha256-js": "3.0.0", "@aws-sdk/credential-provider-node": "3.414.0", "@aws-sdk/middleware-host-header": "3.413.0", "@aws-sdk/middleware-logger": "3.413.0", "@aws-sdk/middleware-recursion-detection": "3.413.0", "@aws-sdk/middleware-sdk-sts": "3.413.0", "@aws-sdk/middleware-signing": "3.413.0", "@aws-sdk/middleware-user-agent": "3.413.0", "@aws-sdk/region-config-resolver": "3.413.0", "@aws-sdk/types": "3.413.0", "@aws-sdk/util-endpoints": "3.413.0", "@aws-sdk/util-user-agent-browser": "3.413.0", "@aws-sdk/util-user-agent-node": "3.413.0", "@smithy/config-resolver": "^2.0.8", "@smithy/fetch-http-handler": "^2.1.3", "@smithy/hash-node": "^2.0.7", "@smithy/invalid-dependency": "^2.0.7", "@smithy/middleware-content-length": "^2.0.9", "@smithy/middleware-endpoint": "^2.0.7", "@smithy/middleware-retry": "^2.0.10", "@smithy/middleware-serde": "^2.0.7", "@smithy/middleware-stack": "^2.0.0", "@smithy/node-config-provider": "^2.0.10", "@smithy/node-http-handler": "^2.1.3", "@smithy/protocol-http": "^3.0.3", "@smithy/smithy-client": "^2.1.4", "@smithy/types": "^2.3.1", "@smithy/url-parser": "^2.0.7", "@smithy/util-base64": "^2.0.0", "@smithy/util-body-length-browser": "^2.0.0", "@smithy/util-body-length-node": "^2.1.0", "@smithy/util-defaults-mode-browser": "^2.0.8", "@smithy/util-defaults-mode-node": "^2.0.10", "@smithy/util-retry": "^2.0.0", "@smithy/util-utf8": "^2.0.0", "fast-xml-parser": "4.2.5", "tslib": "^2.5.0"}, "dependencies": {"@aws-crypto/sha256-js": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/sha256-js/-/sha256-js-3.0.0.tgz", "integrity": "sha512-PnNN7os0+yd1XvXAy23CFOmTbMaDxgxXtTKHybrJ39Y8kGzBATgBFibWJKH6BhytLI/Zyszs87xCOBNyBig6vQ==", "requires": {"@aws-crypto/util": "^3.0.0", "@aws-sdk/types": "^3.222.0", "tslib": "^1.11.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}}}, "@aws-crypto/util": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/util/-/util-3.0.0.tgz", "integrity": "sha512-2OJlpeJpCR48CC8r+uKVChzs9Iungj9wkZrl8Z041DWEWvyIHILYKCPNzJghKsivj+S3mLo6BVc7mBNzdxA46w==", "requires": {"@aws-sdk/types": "^3.222.0", "@aws-sdk/util-utf8-browser": "^3.0.0", "tslib": "^1.11.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}}}, "tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@aws-sdk/credential-provider-env": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-env/-/credential-provider-env-3.413.0.tgz", "integrity": "sha512-yeMOkfG20/RlzfPMtQuDB647AcPEvFEVYOWZzAWVJfldYQ5ybKr0d7sBkgG9sdAzGkK3Aw9dE4rigYI8EIqc1Q==", "requires": {"@aws-sdk/types": "3.413.0", "@smithy/property-provider": "^2.0.0", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@aws-sdk/credential-provider-ini": {"version": "3.414.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-ini/-/credential-provider-ini-3.414.0.tgz", "integrity": "sha512-rlpLLx70roJL/t40opWC96LbIASejdMbRlgSCRpK8b/hKngYDe5A7SRVacaw08vYrAywxRiybxpQOwOt9b++rA==", "requires": {"@aws-sdk/credential-provider-env": "3.413.0", "@aws-sdk/credential-provider-process": "3.413.0", "@aws-sdk/credential-provider-sso": "3.414.0", "@aws-sdk/credential-provider-web-identity": "3.413.0", "@aws-sdk/types": "3.413.0", "@smithy/credential-provider-imds": "^2.0.0", "@smithy/property-provider": "^2.0.0", "@smithy/shared-ini-file-loader": "^2.0.6", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@aws-sdk/credential-provider-node": {"version": "3.414.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-node/-/credential-provider-node-3.414.0.tgz", "integrity": "sha512-xlkcOUKeGHInxWKKrZKIPSBCUL/ozyCldJBjmMKEj7ZmBAEiDcjpMe3pZ//LibMkCSy0b/7jtyQBE/eaIT2o0A==", "requires": {"@aws-sdk/credential-provider-env": "3.413.0", "@aws-sdk/credential-provider-ini": "3.414.0", "@aws-sdk/credential-provider-process": "3.413.0", "@aws-sdk/credential-provider-sso": "3.414.0", "@aws-sdk/credential-provider-web-identity": "3.413.0", "@aws-sdk/types": "3.413.0", "@smithy/credential-provider-imds": "^2.0.0", "@smithy/property-provider": "^2.0.0", "@smithy/shared-ini-file-loader": "^2.0.6", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@aws-sdk/credential-provider-process": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-process/-/credential-provider-process-3.413.0.tgz", "integrity": "sha512-GFJdgS14GzJ1wc2DEnS44Z/34iBZ05CAkvDsLN2CMwcDgH4eZuif9/x0lwzIJBK3xVFHzYUeVvEzsqRPbCHRsw==", "requires": {"@aws-sdk/types": "3.413.0", "@smithy/property-provider": "^2.0.0", "@smithy/shared-ini-file-loader": "^2.0.6", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@aws-sdk/credential-provider-sso": {"version": "3.414.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-sso/-/credential-provider-sso-3.414.0.tgz", "integrity": "sha512-w9g2hlkZn7WekWICRqk+L33py7KrjYMFryVpkKXOx2pjDchCfZDr6pL1ml782GZ0L3qsob4SbNpbtp13JprnWQ==", "requires": {"@aws-sdk/client-sso": "3.414.0", "@aws-sdk/token-providers": "3.413.0", "@aws-sdk/types": "3.413.0", "@smithy/property-provider": "^2.0.0", "@smithy/shared-ini-file-loader": "^2.0.6", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@aws-sdk/credential-provider-web-identity": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-web-identity/-/credential-provider-web-identity-3.413.0.tgz", "integrity": "sha512-5cdA1Iq9JeEHtg59ERV9fdMQ7cS0JF6gH/BWA7HYEUGdSVPXCuwyEggPtG64QgpNU7SmxH+QdDG+Ldxz09ycIA==", "requires": {"@aws-sdk/types": "3.413.0", "@smithy/property-provider": "^2.0.0", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@aws-sdk/middleware-host-header": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-host-header/-/middleware-host-header-3.413.0.tgz", "integrity": "sha512-r9PQx468EzPHo9wRzZLfgROpKtVdbkteMrdhsuM12bifVHjU1OHr7yfhc1OdWv39X8Xiv6F8n5r+RBQEM0S6+g==", "requires": {"@aws-sdk/types": "3.413.0", "@smithy/protocol-http": "^3.0.3", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@aws-sdk/middleware-logger": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-logger/-/middleware-logger-3.413.0.tgz", "integrity": "sha512-jqcXDubcKvoqBy+kkEa0WoNjG6SveDeyNy+gdGnTV+DEtYjkcHrHJei4q0W5zFl0mzc+dP+z8tJF44rv95ZY3Q==", "requires": {"@aws-sdk/types": "3.413.0", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@aws-sdk/middleware-recursion-detection": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-recursion-detection/-/middleware-recursion-detection-3.413.0.tgz", "integrity": "sha512-C6k0IKJk/A4/VBGwUjxEPG+WOjjnmWAZVRBUzaeM7PqRh+g5rLcuIV356ntV3pREVxyiSTePTYVYIHU9YXkLKQ==", "requires": {"@aws-sdk/types": "3.413.0", "@smithy/protocol-http": "^3.0.3", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@aws-sdk/middleware-sdk-sts": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-sdk-sts/-/middleware-sdk-sts-3.413.0.tgz", "integrity": "sha512-t0u//JUyaEZRVnH5q+Ur3tWnuyIsTdwA0XOdDCZXcSlLYzGp2MI/tScLjn9IydRrceIFpFfmbjk4Nf/Q6TeBTQ==", "requires": {"@aws-sdk/middleware-signing": "3.413.0", "@aws-sdk/types": "3.413.0", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@aws-sdk/middleware-signing": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-signing/-/middleware-signing-3.413.0.tgz", "integrity": "sha512-QFEnVvIKYPCermM+ESxEztgUgXzGSKpnPnohMYNvSZySqmOLu/4VvxiZbRO/BX9J3ZHcUgaw4vKm5VBZRrycxw==", "requires": {"@aws-sdk/types": "3.413.0", "@smithy/property-provider": "^2.0.0", "@smithy/protocol-http": "^3.0.3", "@smithy/signature-v4": "^2.0.0", "@smithy/types": "^2.3.1", "@smithy/util-middleware": "^2.0.0", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@aws-sdk/middleware-user-agent": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-user-agent/-/middleware-user-agent-3.413.0.tgz", "integrity": "sha512-eVMJyeWxNBqerhfD+sE9sTjDtwQiECrfU6wpUQP5fGPhJD2cVVZPxuTuJGDZCu/4k/V61dF85IYlsPUNLdVQ6w==", "requires": {"@aws-sdk/types": "3.413.0", "@aws-sdk/util-endpoints": "3.413.0", "@smithy/protocol-http": "^3.0.3", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@aws-sdk/node-http-handler": {"version": "3.374.0", "resolved": "https://registry.npmjs.org/@aws-sdk/node-http-handler/-/node-http-handler-3.374.0.tgz", "integrity": "sha512-v1Z6m0wwkf65/tKuhwrtPRqVoOtNkDTRn2MBMtxCwEw+8V8Q+YRFqVgGN+J1n53ktE0G5OYVBux/NHiAjJHReQ==", "requires": {"@smithy/node-http-handler": "^1.0.2", "tslib": "^2.5.0"}, "dependencies": {"@smithy/abort-controller": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@smithy/abort-controller/-/abort-controller-1.1.0.tgz", "integrity": "sha512-5imgGUlZL4dW4YWdMYAKLmal9ny/tlenM81QZY7xYyb76z9Z/QOg7oM5Ak9HQl8QfFTlGVWwcMXl+54jroRgEQ==", "requires": {"@smithy/types": "^1.2.0", "tslib": "^2.5.0"}}, "@smithy/node-http-handler": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@smithy/node-http-handler/-/node-http-handler-1.1.0.tgz", "integrity": "sha512-d3kRriEgaIiGXLziAM8bjnaLn1fthCJeTLZIwEIpzQqe6yPX0a+yQoLCTyjb2fvdLwkMoG4p7THIIB5cj5lkbg==", "requires": {"@smithy/abort-controller": "^1.1.0", "@smithy/protocol-http": "^1.2.0", "@smithy/querystring-builder": "^1.1.0", "@smithy/types": "^1.2.0", "tslib": "^2.5.0"}}, "@smithy/protocol-http": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/@smithy/protocol-http/-/protocol-http-1.2.0.tgz", "integrity": "sha512-GfGfruksi3nXdFok5RhgtOnWe5f6BndzYfmEXISD+5gAGdayFGpjWu5pIqIweTudMtse20bGbc+7MFZXT1Tb8Q==", "requires": {"@smithy/types": "^1.2.0", "tslib": "^2.5.0"}}, "@smithy/querystring-builder": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@smithy/querystring-builder/-/querystring-builder-1.1.0.tgz", "integrity": "sha512-gDEi4LxIGLbdfjrjiY45QNbuDmpkwh9DX4xzrR2AzjjXpxwGyfSpbJaYhXARw9p17VH0h9UewnNQXNwaQyYMDA==", "requires": {"@smithy/types": "^1.2.0", "@smithy/util-uri-escape": "^1.1.0", "tslib": "^2.5.0"}}, "@smithy/types": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/@smithy/types/-/types-1.2.0.tgz", "integrity": "sha512-z1r00TvBqF3dh4aHhya7nz1HhvCg4TRmw51fjMrh5do3h+ngSstt/yKlNbHeb9QxJmFbmN8KEVSWgb1bRvfEoA==", "requires": {"tslib": "^2.5.0"}}, "@smithy/util-uri-escape": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@smithy/util-uri-escape/-/util-uri-escape-1.1.0.tgz", "integrity": "sha512-/jL/V1xdVRt5XppwiaEU8Etp5WHZj609n0xMTuehmCqdoOFbId1M+aEeDWZsQ+8JbEB/BJ6ynY2SlYmOaKtt8w==", "requires": {"tslib": "^2.5.0"}}, "tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@aws-sdk/protocol-http": {"version": "3.374.0", "resolved": "https://registry.npmjs.org/@aws-sdk/protocol-http/-/protocol-http-3.374.0.tgz", "integrity": "sha512-9WpRUbINdGroV3HiZZIBoJvL2ndoWk39OfwxWs2otxByppJZNN14bg/lvCx5e8ggHUti7IBk5rb0nqQZ4m05pg==", "requires": {"@smithy/protocol-http": "^1.1.0", "tslib": "^2.5.0"}, "dependencies": {"@smithy/protocol-http": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/@smithy/protocol-http/-/protocol-http-1.2.0.tgz", "integrity": "sha512-GfGfruksi3nXdFok5RhgtOnWe5f6BndzYfmEXISD+5gAGdayFGpjWu5pIqIweTudMtse20bGbc+7MFZXT1Tb8Q==", "requires": {"@smithy/types": "^1.2.0", "tslib": "^2.5.0"}}, "@smithy/types": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/@smithy/types/-/types-1.2.0.tgz", "integrity": "sha512-z1r00TvBqF3dh4aHhya7nz1HhvCg4TRmw51fjMrh5do3h+ngSstt/yKlNbHeb9QxJmFbmN8KEVSWgb1bRvfEoA==", "requires": {"tslib": "^2.5.0"}}, "tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@aws-sdk/region-config-resolver": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/region-config-resolver/-/region-config-resolver-3.413.0.tgz", "integrity": "sha512-h90e6yyOhvoc+1F5vFk3C5mxwB8RSDEMKTO/fxexyur94seczZ1yxyYkTMZv30oc9RUiToABlHNrh/wxL7TZPQ==", "requires": {"@smithy/node-config-provider": "^2.0.10", "@smithy/types": "^2.3.1", "@smithy/util-config-provider": "^2.0.0", "@smithy/util-middleware": "^2.0.0", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@aws-sdk/signature-v4": {"version": "3.374.0", "resolved": "https://registry.npmjs.org/@aws-sdk/signature-v4/-/signature-v4-3.374.0.tgz", "integrity": "sha512-2xLJvSdzcZZAg0lsDLUAuSQuihzK0dcxIK7WmfuJeF7DGKJFmp9czQmz5f3qiDz6IDQzvgK1M9vtJSVCslJbyQ==", "requires": {"@smithy/signature-v4": "^1.0.1", "tslib": "^2.5.0"}, "dependencies": {"@smithy/eventstream-codec": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@smithy/eventstream-codec/-/eventstream-codec-1.1.0.tgz", "integrity": "sha512-3tEbUb8t8an226jKB6V/Q2XU/J53lCwCzULuBPEaF4JjSh+FlCMp7TmogE/Aij5J9DwlsZ4VAD/IRDuQ/0ZtMw==", "requires": {"@aws-crypto/crc32": "3.0.0", "@smithy/types": "^1.2.0", "@smithy/util-hex-encoding": "^1.1.0", "tslib": "^2.5.0"}}, "@smithy/is-array-buffer": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@smithy/is-array-buffer/-/is-array-buffer-1.1.0.tgz", "integrity": "sha512-twpQ/n+3OWZJ7Z+xu43MJErmhB/WO/mMTnqR6PwWQShvSJ/emx5d1N59LQZk6ZpTAeuRWrc+eHhkzTp9NFjNRQ==", "requires": {"tslib": "^2.5.0"}}, "@smithy/signature-v4": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@smithy/signature-v4/-/signature-v4-1.1.0.tgz", "integrity": "sha512-fDo3m7YqXBs7neciOePPd/X9LPm5QLlDMdIC4m1H6dgNLnXfLMFNIxEfPyohGA8VW9Wn4X8lygnPSGxDZSmp0Q==", "requires": {"@smithy/eventstream-codec": "^1.1.0", "@smithy/is-array-buffer": "^1.1.0", "@smithy/types": "^1.2.0", "@smithy/util-hex-encoding": "^1.1.0", "@smithy/util-middleware": "^1.1.0", "@smithy/util-uri-escape": "^1.1.0", "@smithy/util-utf8": "^1.1.0", "tslib": "^2.5.0"}}, "@smithy/types": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/@smithy/types/-/types-1.2.0.tgz", "integrity": "sha512-z1r00TvBqF3dh4aHhya7nz1HhvCg4TRmw51fjMrh5do3h+ngSstt/yKlNbHeb9QxJmFbmN8KEVSWgb1bRvfEoA==", "requires": {"tslib": "^2.5.0"}}, "@smithy/util-buffer-from": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@smithy/util-buffer-from/-/util-buffer-from-1.1.0.tgz", "integrity": "sha512-9m6NXE0ww+ra5HKHCHig20T+FAwxBAm7DIdwc/767uGWbRcY720ybgPacQNB96JMOI7xVr/CDa3oMzKmW4a+kw==", "requires": {"@smithy/is-array-buffer": "^1.1.0", "tslib": "^2.5.0"}}, "@smithy/util-hex-encoding": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@smithy/util-hex-encoding/-/util-hex-encoding-1.1.0.tgz", "integrity": "sha512-7UtIE9eH0u41zpB60Jzr0oNCQ3hMJUabMcKRUVjmyHTXiWDE4vjSqN6qlih7rCNeKGbioS7f/y2Jgym4QZcKFg==", "requires": {"tslib": "^2.5.0"}}, "@smithy/util-middleware": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@smithy/util-middleware/-/util-middleware-1.1.0.tgz", "integrity": "sha512-6hhckcBqVgjWAqLy2vqlPZ3rfxLDhFWEmM7oLh2POGvsi7j0tHkbN7w4DFhuBExVJAbJ/qqxqZdRY6Fu7/OezQ==", "requires": {"tslib": "^2.5.0"}}, "@smithy/util-uri-escape": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@smithy/util-uri-escape/-/util-uri-escape-1.1.0.tgz", "integrity": "sha512-/jL/V1xdVRt5XppwiaEU8Etp5WHZj609n0xMTuehmCqdoOFbId1M+aEeDWZsQ+8JbEB/BJ6ynY2SlYmOaKtt8w==", "requires": {"tslib": "^2.5.0"}}, "@smithy/util-utf8": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@smithy/util-utf8/-/util-utf8-1.1.0.tgz", "integrity": "sha512-p/MYV+JmqmPyjdgyN2UxAeYDj9cBqCjp0C/NsTWnnjoZUVqoeZ6IrW915L9CAKWVECgv9lVQGc4u/yz26/bI1A==", "requires": {"@smithy/util-buffer-from": "^1.1.0", "tslib": "^2.5.0"}}, "tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@aws-sdk/token-providers": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/token-providers/-/token-providers-3.413.0.tgz", "integrity": "sha512-NfP1Ib9LAWVLMTOa/1aJwt4TRrlRrNyukCpVZGfNaMnNNEoP5Rakdbcs8KFVHe/MJzU+GdKVzxQ4TgRkLOGTrA==", "requires": {"@aws-crypto/sha256-browser": "3.0.0", "@aws-crypto/sha256-js": "3.0.0", "@aws-sdk/middleware-host-header": "3.413.0", "@aws-sdk/middleware-logger": "3.413.0", "@aws-sdk/middleware-recursion-detection": "3.413.0", "@aws-sdk/middleware-user-agent": "3.413.0", "@aws-sdk/types": "3.413.0", "@aws-sdk/util-endpoints": "3.413.0", "@aws-sdk/util-user-agent-browser": "3.413.0", "@aws-sdk/util-user-agent-node": "3.413.0", "@smithy/config-resolver": "^2.0.8", "@smithy/fetch-http-handler": "^2.1.3", "@smithy/hash-node": "^2.0.7", "@smithy/invalid-dependency": "^2.0.7", "@smithy/middleware-content-length": "^2.0.9", "@smithy/middleware-endpoint": "^2.0.7", "@smithy/middleware-retry": "^2.0.10", "@smithy/middleware-serde": "^2.0.7", "@smithy/middleware-stack": "^2.0.0", "@smithy/node-config-provider": "^2.0.10", "@smithy/node-http-handler": "^2.1.3", "@smithy/property-provider": "^2.0.0", "@smithy/protocol-http": "^3.0.3", "@smithy/shared-ini-file-loader": "^2.0.6", "@smithy/smithy-client": "^2.1.4", "@smithy/types": "^2.3.1", "@smithy/url-parser": "^2.0.7", "@smithy/util-base64": "^2.0.0", "@smithy/util-body-length-browser": "^2.0.0", "@smithy/util-body-length-node": "^2.1.0", "@smithy/util-defaults-mode-browser": "^2.0.8", "@smithy/util-defaults-mode-node": "^2.0.10", "@smithy/util-retry": "^2.0.0", "@smithy/util-utf8": "^2.0.0", "tslib": "^2.5.0"}, "dependencies": {"@aws-crypto/sha256-js": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/sha256-js/-/sha256-js-3.0.0.tgz", "integrity": "sha512-PnNN7os0+yd1XvXAy23CFOmTbMaDxgxXtTKHybrJ39Y8kGzBATgBFibWJKH6BhytLI/Zyszs87xCOBNyBig6vQ==", "requires": {"@aws-crypto/util": "^3.0.0", "@aws-sdk/types": "^3.222.0", "tslib": "^1.11.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}}}, "@aws-crypto/util": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@aws-crypto/util/-/util-3.0.0.tgz", "integrity": "sha512-2OJlpeJpCR48CC8r+uKVChzs9Iungj9wkZrl8Z041DWEWvyIHILYKCPNzJghKsivj+S3mLo6BVc7mBNzdxA46w==", "requires": {"@aws-sdk/types": "^3.222.0", "@aws-sdk/util-utf8-browser": "^3.0.0", "tslib": "^1.11.1"}, "dependencies": {"tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}}}, "tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@aws-sdk/types": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/types/-/types-3.413.0.tgz", "integrity": "sha512-j1xib0f/TazIFc5ySIKOlT1ujntRbaoG4LJFeEezz4ji03/wSJMI8Vi4KjzpBp8J1tTu0oRDnsxRIGixsUBeYQ==", "requires": {"@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@aws-sdk/util-endpoints": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-endpoints/-/util-endpoints-3.413.0.tgz", "integrity": "sha512-VAwr7cITNb1L6/2XUPIbCOuhKGm0VtKCRblurrfUF2bxqG/wtuw/2Fm4ahYJPyxklOSXAMSq+RHdFWcir0YB/g==", "requires": {"@aws-sdk/types": "3.413.0", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@aws-sdk/util-locate-window": {"version": "3.310.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-locate-window/-/util-locate-window-3.310.0.tgz", "integrity": "sha512-qo2t/vBTnoXpjKxlsC2e1gBrRm80M3bId27r0BRB2VniSSe7bL1mmzM+/HFtujm0iAxtPM+aLEflLJlJeDPg0w==", "requires": {"tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@aws-sdk/util-user-agent-browser": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-user-agent-browser/-/util-user-agent-browser-3.413.0.tgz", "integrity": "sha512-7j/qWcRO2OBZBre2fC6V6M0PAS9n7k6i+VtofPkkhxC2DZszLJElqnooF9hGmVGYK3zR47Np4WjURXKIEZclWg==", "requires": {"@aws-sdk/types": "3.413.0", "@smithy/types": "^2.3.1", "bowser": "^2.11.0", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@aws-sdk/util-user-agent-node": {"version": "3.413.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-user-agent-node/-/util-user-agent-node-3.413.0.tgz", "integrity": "sha512-vHm9TVZIzfWMeDvdmoOky6VarqOt8Pr68CESHN0jyuO6XbhCDnr9rpaXiBhbSR+N1Qm7R/AfJgAhQyTMu2G1OA==", "requires": {"@aws-sdk/types": "3.413.0", "@smithy/node-config-provider": "^2.0.10", "@smithy/types": "^2.3.1", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@aws-sdk/util-utf8-browser": {"version": "3.259.0", "resolved": "https://registry.npmjs.org/@aws-sdk/util-utf8-browser/-/util-utf8-browser-3.259.0.tgz", "integrity": "sha512-UvFa/vR+e19XookZF8RzFZBrw2EUkQWxiBW0yYQAhvk3C+QVGl0H3ouca8LDBlBfQKXwmW3huo/59H8rwb1wJw==", "requires": {"tslib": "^2.3.1"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/abort-controller": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/@smithy/abort-controller/-/abort-controller-2.0.8.tgz", "integrity": "sha512-2SOdVj5y0zE37Y9scSXoizoxgi6mgnDabi7a/SOfhl0p+50I0rIkuJTfyAuTPDtQ7e5dD6tSZPCLB3c/YM6Zig==", "requires": {"@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/config-resolver": {"version": "2.0.9", "resolved": "https://registry.npmjs.org/@smithy/config-resolver/-/config-resolver-2.0.9.tgz", "integrity": "sha512-QBkGPLUqyPmis9Erz8v4q5lo/ErnF7+GD5WZHa6JZiXopUPfaaM+B21n8gzS5xCkIXZmnwzNQhObP9xQPu8oqQ==", "requires": {"@smithy/node-config-provider": "^2.0.11", "@smithy/types": "^2.3.2", "@smithy/util-config-provider": "^2.0.0", "@smithy/util-middleware": "^2.0.1", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/credential-provider-imds": {"version": "2.0.11", "resolved": "https://registry.npmjs.org/@smithy/credential-provider-imds/-/credential-provider-imds-2.0.11.tgz", "integrity": "sha512-uJJs8dnM5iXkn8a2GaKvlKMhcOJ+oJPYqY9gY3CM/EieCVObIDjxUtR/g8lU/k/A+OauA78GzScAfulmFjPOYA==", "requires": {"@smithy/node-config-provider": "^2.0.11", "@smithy/property-provider": "^2.0.9", "@smithy/types": "^2.3.2", "@smithy/url-parser": "^2.0.8", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/eventstream-codec": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/@smithy/eventstream-codec/-/eventstream-codec-2.0.8.tgz", "integrity": "sha512-onO4to8ujCKn4m5XagReT9Nc6FlNG5vveuvjp1H7AtaG7njdet1LOl6/jmUOkskF2C/w+9jNw3r9Ak+ghOvN0A==", "requires": {"@aws-crypto/crc32": "3.0.0", "@smithy/types": "^2.3.2", "@smithy/util-hex-encoding": "^2.0.0", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/fetch-http-handler": {"version": "2.1.4", "resolved": "https://registry.npmjs.org/@smithy/fetch-http-handler/-/fetch-http-handler-2.1.4.tgz", "integrity": "sha512-SL24M9W5ERByoXaVicRx+bj9GJVujDnPn+QO7GY7adhY0mPGa6DSF58pVKsgIh4r5Tx/k3SWCPlH4BxxSxA/fQ==", "requires": {"@smithy/protocol-http": "^3.0.4", "@smithy/querystring-builder": "^2.0.8", "@smithy/types": "^2.3.2", "@smithy/util-base64": "^2.0.0", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/hash-node": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/@smithy/hash-node/-/hash-node-2.0.8.tgz", "integrity": "sha512-yZL/nmxZzjZV5/QX5JWSgXlt0HxuMTwFO89CS++jOMMPiCMZngf6VYmtNdccs8IIIAMmfQeTzwu07XgUE/Zd3Q==", "requires": {"@smithy/types": "^2.3.2", "@smithy/util-buffer-from": "^2.0.0", "@smithy/util-utf8": "^2.0.0", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/invalid-dependency": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/@smithy/invalid-dependency/-/invalid-dependency-2.0.8.tgz", "integrity": "sha512-88VOS7W3KzUz/bNRc+Sl/F/CDIasFspEE4G39YZRHIh9YmsXF7GUyVaAKURfMNulTie62ayk6BHC9O0nOBAVgQ==", "requires": {"@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/is-array-buffer": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@smithy/is-array-buffer/-/is-array-buffer-2.0.0.tgz", "integrity": "sha512-z3PjFjMyZNI98JFRJi/U0nGoLWMSJlDjAW4QUX2WNZLas5C0CmVV6LJ01JI0k90l7FvpmixjWxPFmENSClQ7ug==", "requires": {"tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/middleware-content-length": {"version": "2.0.10", "resolved": "https://registry.npmjs.org/@smithy/middleware-content-length/-/middleware-content-length-2.0.10.tgz", "integrity": "sha512-EGSbysyA4jH0p3xI6G0jdXoj9Iz9GUnAta6aEaHtXm3wVWtenRf80y2TeVvNkVSr5jwKOdSCjKIRI2l1A/oZLA==", "requires": {"@smithy/protocol-http": "^3.0.4", "@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/middleware-endpoint": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/@smithy/middleware-endpoint/-/middleware-endpoint-2.0.8.tgz", "integrity": "sha512-yOpogfG2d2V0cbJdAJ6GLAWkNOc9pVsL5hZUfXcxJu408N3CUCsXzIAFF6+70ZKSE+lCfG3GFErcSXv/UfUbjw==", "requires": {"@smithy/middleware-serde": "^2.0.8", "@smithy/types": "^2.3.2", "@smithy/url-parser": "^2.0.8", "@smithy/util-middleware": "^2.0.1", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/middleware-retry": {"version": "2.0.11", "resolved": "https://registry.npmjs.org/@smithy/middleware-retry/-/middleware-retry-2.0.11.tgz", "integrity": "sha512-pknfokumZ+wvBERSuKAI2vVr+aK3ZgPiWRg6+0ZG4kKJogBRpPmDGWw+Jht0izS9ZaEbIobNzueIb4wD33JJVg==", "requires": {"@smithy/node-config-provider": "^2.0.11", "@smithy/protocol-http": "^3.0.4", "@smithy/service-error-classification": "^2.0.1", "@smithy/types": "^2.3.2", "@smithy/util-middleware": "^2.0.1", "@smithy/util-retry": "^2.0.1", "tslib": "^2.5.0", "uuid": "^8.3.2"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/middleware-serde": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/@smithy/middleware-serde/-/middleware-serde-2.0.8.tgz", "integrity": "sha512-Is0sm+LiNlgsc0QpstDzifugzL9ehno1wXp109GgBgpnKTK3j+KphiparBDI4hWTtH9/7OUsxuspNqai2yyhcg==", "requires": {"@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/middleware-stack": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/@smithy/middleware-stack/-/middleware-stack-2.0.1.tgz", "integrity": "sha512-UexsfY6/oQZRjTQL56s9AKtMcR60tBNibSgNYX1I2WXaUaXg97W9JCkFyth85TzBWKDBTyhLfenrukS/kyu54A==", "requires": {"@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/node-config-provider": {"version": "2.0.11", "resolved": "https://registry.npmjs.org/@smithy/node-config-provider/-/node-config-provider-2.0.11.tgz", "integrity": "sha512-CaR1dciSSGKttjhcefpytYjsfI/Yd5mqL8am4wfmyFCDxSiPsvnEWHl8UjM/RbcAjX0klt+CeIKPSHEc0wGvJA==", "requires": {"@smithy/property-provider": "^2.0.9", "@smithy/shared-ini-file-loader": "^2.0.10", "@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/node-http-handler": {"version": "2.1.4", "resolved": "https://registry.npmjs.org/@smithy/node-http-handler/-/node-http-handler-2.1.4.tgz", "integrity": "sha512-8Rw/AusvWDyC6SK8esAcVBeTlQHf94NMFv805suFUJCQ2gwlh0oLDNh+6s2MDOrxcjvLxjjzv1mytM0Mt+0cPQ==", "requires": {"@smithy/abort-controller": "^2.0.8", "@smithy/protocol-http": "^3.0.4", "@smithy/querystring-builder": "^2.0.8", "@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/property-provider": {"version": "2.0.9", "resolved": "https://registry.npmjs.org/@smithy/property-provider/-/property-provider-2.0.9.tgz", "integrity": "sha512-25pPZ8f8DeRwYI5wbPRZaoMoR+3vrw8DwbA0TjP+GsdiB2KxScndr4HQehiJ5+WJ0giOTWhLz0bd+7Djv1qpUQ==", "requires": {"@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/protocol-http": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/@smithy/protocol-http/-/protocol-http-3.0.4.tgz", "integrity": "sha512-CGfSWk6TRlbwa8YgrSXdn80Yu7pov3EV/h7TSfiCHhq6/LO3WymmqnzgH1f0qV2bdTDipIkTNp5dGCGN3Af/5g==", "requires": {"@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/querystring-builder": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/@smithy/querystring-builder/-/querystring-builder-2.0.8.tgz", "integrity": "sha512-+vzIMwjC8Saz97/ptPn+IJRCRRZ+pP95ZIWDRqEqZV/a6hiKbaFoMSa2iCKsnKzR696U2JZXrDqMu3e/FD1+2g==", "requires": {"@smithy/types": "^2.3.2", "@smithy/util-uri-escape": "^2.0.0", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/querystring-parser": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/@smithy/querystring-parser/-/querystring-parser-2.0.8.tgz", "integrity": "sha512-ArbanNuR7O/MmTd90ZqhDqGOPPDYmxx3huHxD+R3cuCnazcK/1tGQA+SnnR5307T7ZRb5WTpB6qBggERuibVSA==", "requires": {"@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/service-error-classification": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/@smithy/service-error-classification/-/service-error-classification-2.0.1.tgz", "integrity": "sha512-QHa9+t+v4s0cMuDCcbjIJN67mNZ42/+fc3jKe8P6ZMPXZl5ksKk6a8vhZ/m494GZng5eFTc3OePv+NF9cG83yg==", "requires": {"@smithy/types": "^2.3.2"}}, "@smithy/shared-ini-file-loader": {"version": "2.0.10", "resolved": "https://registry.npmjs.org/@smithy/shared-ini-file-loader/-/shared-ini-file-loader-2.0.10.tgz", "integrity": "sha512-jWASteSezRKohJ7GdA7pHDvmr7Q7tw3b5mu3xLHIkZy/ICftJ+O7aqNaF8wklhI7UNFoQ7flFRM3Rd0KA+1BbQ==", "requires": {"@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/signature-v4": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/@smithy/signature-v4/-/signature-v4-2.0.8.tgz", "integrity": "sha512-qrtiYMzaLlQ5HSJOaFwnyTQ3JLjmPY+3+pr9IBDpCVM6YtVj22cBLVB9bPOiZMIpkdI7ZRdxLBFlIjh5CO1Bhw==", "requires": {"@smithy/eventstream-codec": "^2.0.8", "@smithy/is-array-buffer": "^2.0.0", "@smithy/types": "^2.3.2", "@smithy/util-hex-encoding": "^2.0.0", "@smithy/util-middleware": "^2.0.1", "@smithy/util-uri-escape": "^2.0.0", "@smithy/util-utf8": "^2.0.0", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/smithy-client": {"version": "2.1.5", "resolved": "https://registry.npmjs.org/@smithy/smithy-client/-/smithy-client-2.1.5.tgz", "integrity": "sha512-7S865uKzsxApM8W8Q6zkij7tcUFgaG8PuADMFdMt1yL/ku3d0+s6Zwrg3N7iXCPM08Gu/mf0BIfTXIu/9i450Q==", "requires": {"@smithy/middleware-stack": "^2.0.1", "@smithy/types": "^2.3.2", "@smithy/util-stream": "^2.0.11", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/types": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/@smithy/types/-/types-2.3.2.tgz", "integrity": "sha512-iH0cdKi7HQlzfAM3w2shFk/qZYKAqJWswtpmQpPtlruF+uFZeGEpMJjgDRyhWiddfVM4e2oP4nMaOBsMy6lXgg==", "requires": {"tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/url-parser": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/@smithy/url-parser/-/url-parser-2.0.8.tgz", "integrity": "sha512-wQw7j004ScCrBRJ+oNPXlLE9mtofxyadSZ9D8ov/rHkyurS7z1HTNuyaGRj6OvKsEk0SVQsuY0C9+EfM75XTkw==", "requires": {"@smithy/querystring-parser": "^2.0.8", "@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/util-base64": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@smithy/util-base64/-/util-base64-2.0.0.tgz", "integrity": "sha512-Zb1E4xx+m5Lud8bbeYi5FkcMJMnn+1WUnJF3qD7rAdXpaL7UjkFQLdmW5fHadoKbdHpwH9vSR8EyTJFHJs++tA==", "requires": {"@smithy/util-buffer-from": "^2.0.0", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/util-body-length-browser": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@smithy/util-body-length-browser/-/util-body-length-browser-2.0.0.tgz", "integrity": "sha512-JdDuS4ircJt+FDnaQj88TzZY3+njZ6O+D3uakS32f2VNnDo3vyEuNdBOh/oFd8Df1zSZOuH1HEChk2AOYDezZg==", "requires": {"tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/util-body-length-node": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/@smithy/util-body-length-node/-/util-body-length-node-2.1.0.tgz", "integrity": "sha512-/li0/kj/y3fQ3vyzn36NTLGmUwAICb7Jbe/CsWCktW363gh1MOcpEcSO3mJ344Gv2dqz8YJCLQpb6hju/0qOWw==", "requires": {"tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/util-buffer-from": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@smithy/util-buffer-from/-/util-buffer-from-2.0.0.tgz", "integrity": "sha512-/YNnLoHsR+4W4Vf2wL5lGv0ksg8Bmk3GEGxn2vEQt52AQaPSCuaO5PM5VM7lP1K9qHRKHwrPGktqVoAHKWHxzw==", "requires": {"@smithy/is-array-buffer": "^2.0.0", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/util-config-provider": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@smithy/util-config-provider/-/util-config-provider-2.0.0.tgz", "integrity": "sha512-xCQ6UapcIWKxXHEU4Mcs2s7LcFQRiU3XEluM2WcCjjBtQkUN71Tb+ydGmJFPxMUrW/GWMgQEEGipLym4XG0jZg==", "requires": {"tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/util-defaults-mode-browser": {"version": "2.0.9", "resolved": "https://registry.npmjs.org/@smithy/util-defaults-mode-browser/-/util-defaults-mode-browser-2.0.9.tgz", "integrity": "sha512-JONLJVQWT8165XoSV36ERn3SVlZLJJ4D6IeGsCSePv65Uxa93pzSLE0UMSR9Jwm4zix7rst9AS8W5QIypZWP8Q==", "requires": {"@smithy/property-provider": "^2.0.9", "@smithy/smithy-client": "^2.1.5", "@smithy/types": "^2.3.2", "bowser": "^2.11.0", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/util-defaults-mode-node": {"version": "2.0.11", "resolved": "https://registry.npmjs.org/@smithy/util-defaults-mode-node/-/util-defaults-mode-node-2.0.11.tgz", "integrity": "sha512-tmqjNsfj+bgZN6jXBe6efZnukzILA7BUytHkzqikuRLNtR+0VVchQHvawD0w6vManh76rO81ydhioe7i4oBzuA==", "requires": {"@smithy/config-resolver": "^2.0.9", "@smithy/credential-provider-imds": "^2.0.11", "@smithy/node-config-provider": "^2.0.11", "@smithy/property-provider": "^2.0.9", "@smithy/smithy-client": "^2.1.5", "@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/util-hex-encoding": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@smithy/util-hex-encoding/-/util-hex-encoding-2.0.0.tgz", "integrity": "sha512-c5xY+NUnFqG6d7HFh1IFfrm3mGl29lC+vF+geHv4ToiuJCBmIfzx6IeHLg+OgRdPFKDXIw6pvi+p3CsscaMcMA==", "requires": {"tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/util-middleware": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/@smithy/util-middleware/-/util-middleware-2.0.1.tgz", "integrity": "sha512-LnsBMi0Mg3gfz/TpNGLv2Jjcz2ra1OX5HR/4IaCepIYmtPQzqMWDdhX/XTW1LS8OZ0xbQuyQPcHkQ+2XkhWOVQ==", "requires": {"@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/util-retry": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/@smithy/util-retry/-/util-retry-2.0.1.tgz", "integrity": "sha512-naj4X0IafJ9yJnVJ58QgSMkCNLjyQOnyrnKh/T0f+0UOUxJiT8vuFn/hS7B/pNqbo2STY7PyJ4J4f+5YqxwNtA==", "requires": {"@smithy/service-error-classification": "^2.0.1", "@smithy/types": "^2.3.2", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/util-stream": {"version": "2.0.11", "resolved": "https://registry.npmjs.org/@smithy/util-stream/-/util-stream-2.0.11.tgz", "integrity": "sha512-2MeWfqSpZKdmEJ+tH8CJQSgzLWhH5cmdE24X7JB0hiamXrOmswWGGuPvyj/9sQCTclo57pNxLR2p7KrP8Ahiyg==", "requires": {"@smithy/fetch-http-handler": "^2.1.4", "@smithy/node-http-handler": "^2.1.4", "@smithy/types": "^2.3.2", "@smithy/util-base64": "^2.0.0", "@smithy/util-buffer-from": "^2.0.0", "@smithy/util-hex-encoding": "^2.0.0", "@smithy/util-utf8": "^2.0.0", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/util-uri-escape": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@smithy/util-uri-escape/-/util-uri-escape-2.0.0.tgz", "integrity": "sha512-ebkxsqinSdEooQduuk9CbKcI+wheijxEb3utGXkCoYQkJnwTnLbH1JXGimJtUkQwNQbsbuYwG2+aFVyZf5TLaw==", "requires": {"tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@smithy/util-utf8": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@smithy/util-utf8/-/util-utf8-2.0.0.tgz", "integrity": "sha512-rctU1VkziY84n5OXe3bPNpKR001ZCME2JCaBBFgtiM2hfKbHFudc/BkMuPab8hRbLd0j3vbnBTTZ1igBf0wgiQ==", "requires": {"@smithy/util-buffer-from": "^2.0.0", "tslib": "^2.5.0"}, "dependencies": {"tslib": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz", "integrity": "sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q=="}}}, "@types/aws4": {"version": "1.11.3", "resolved": "https://registry.npmjs.org/@types/aws4/-/aws4-1.11.3.tgz", "integrity": "sha512-Ka2xKf04xZUH0N7wIYpqcNdavgfPQnaJ1T6GieZs1ydo21vao93aCbHyrA6uKXnaTXzvBcMJkgMsBfT9XvypFQ==", "dev": true, "requires": {"@types/node": "*"}}, "@types/node": {"version": "20.6.2", "resolved": "https://registry.npmjs.org/@types/node/-/node-20.6.2.tgz", "integrity": "sha512-Y+/1vGBHV/cYk6OI1Na/LHzwnlNCAfU3ZNGrc1LdRe/LAIbdDPTTv/HU3M7yXN448aTVDq3eKRm2cg7iKLb8gw==", "dev": true}, "asynckit": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="}, "available-typed-arrays": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.5.tgz", "integrity": "sha512-DMD0KiN46eipeziST1LPP/STfDU0sufISXmjSgvVsoU2tqxctQeASejWcfNtxYKqETM1UxQ8sp2OrSBWpHY6sw=="}, "aws-sdk": {"version": "2.1459.0", "resolved": "https://registry.npmjs.org/aws-sdk/-/aws-sdk-2.1459.0.tgz", "integrity": "sha512-My45PgQYhRTh6fOeZ94ELUoXzza/6gTy0J22aK4iy0DEA+uE5gjr1VthnIwbLYNMeEqn8xwJZuNJqvi/WaUUcQ==", "requires": {"buffer": "4.9.2", "events": "1.1.1", "ieee754": "1.1.13", "jmespath": "0.16.0", "querystring": "0.2.0", "sax": "1.2.1", "url": "0.10.3", "util": "^0.12.4", "uuid": "8.0.0", "xml2js": "0.5.0"}, "dependencies": {"uuid": {"version": "8.0.0", "resolved": "https://registry.npmjs.org/uuid/-/uuid-8.0.0.tgz", "integrity": "sha512-jOXGuXZAWdsTH7eZLtyXMqUb9EcWMGZNbL9YcGBJl4MH4nrxHmZJhEHvyLFrkxo+28uLb/NYRcStH48fnD0Vzw=="}}}, "aws4": {"version": "1.12.0", "resolved": "https://registry.npmjs.org/aws4/-/aws4-1.12.0.tgz", "integrity": "sha512-NmWvPnx0F1SfrQbYwOi7OeaNGokp9XhzNioJ/CSBs8Qa4vxug81mhJEAVZwxXuBmYB5KDRfMq/F3RR0BIU7sWg=="}, "axios": {"version": "1.11.0", "resolved": "https://registry.npmjs.org/axios/-/axios-1.11.0.tgz", "integrity": "sha512-1Lx3WLFQWm3ooKDYZD1eXmoGO9fxYQjrycfHFC8P0sCfQVXyROp0p9PFWBehewBOdCwHc+f/b8I0fMto5eSfwA==", "requires": {"follow-redirects": "^1.15.6", "form-data": "^4.0.4", "proxy-from-env": "^1.1.0"}}, "base64-js": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA=="}, "bowser": {"version": "2.11.0", "resolved": "https://registry.npmjs.org/bowser/-/bowser-2.11.0.tgz", "integrity": "sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA=="}, "buffer": {"version": "4.9.2", "resolved": "https://registry.npmjs.org/buffer/-/buffer-4.9.2.tgz", "integrity": "sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg==", "requires": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}}, "call-bind": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==", "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "call-bind-apply-helpers": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==", "requires": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}}, "combined-stream": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "requires": {"delayed-stream": "~1.0.0"}}, "delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ=="}, "dunder-proto": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz", "integrity": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==", "requires": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}}, "es-define-property": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz", "integrity": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g=="}, "es-errors": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz", "integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw=="}, "es-object-atoms": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "integrity": "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==", "requires": {"es-errors": "^1.3.0"}}, "es-set-tostringtag": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz", "integrity": "sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==", "requires": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}}, "events": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/events/-/events-1.1.1.tgz", "integrity": "sha512-kEcvvCBByWXGnZy6JUlgAp2gBIUjfCAV6P6TgT1/aaQKcmuAEC4OZTV1I4EWQLz2gxZw76atuVyvHhTxvi0Flw=="}, "fast-xml-parser": {"version": "4.2.5", "resolved": "https://registry.npmjs.org/fast-xml-parser/-/fast-xml-parser-4.2.5.tgz", "integrity": "sha512-B9/wizE4WngqQftFPmdaMYlXoJlJOYxGQOanC77fq9k8+Z0v5dDSVh+3glErdIROP//s/jgb7ZuxKfB8nVyo0g==", "requires": {"strnum": "^1.0.5"}}, "follow-redirects": {"version": "1.15.11", "resolved": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.11.tgz", "integrity": "sha512-deG2P0JfjrTxl50XGCDyfI97ZGVCxIpfKYmfyrQ54n5FO/0gfIES8C/Psl6kWVDolizcaaxZJnTS0QSMxvnsBQ=="}, "for-each": {"version": "0.3.3", "resolved": "https://registry.npmjs.org/for-each/-/for-each-0.3.3.tgz", "integrity": "sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==", "requires": {"is-callable": "^1.1.3"}}, "form-data": {"version": "4.0.4", "resolved": "https://registry.npmjs.org/form-data/-/form-data-4.0.4.tgz", "integrity": "sha512-KrGhL9Q4zjj0kiUt5OO4Mr/A/jlI2jDYs5eHBpYHPcBEVSiipAvn2Ko2HnPe20rmcuuvMHNdZFp+4IlGTMF0Ow==", "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "hasown": "^2.0.2", "mime-types": "^2.1.12"}}, "function-bind": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA=="}, "get-intrinsic": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "integrity": "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==", "requires": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}}, "get-proto": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz", "integrity": "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==", "requires": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}}, "gopd": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz", "integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg=="}, "has-symbols": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz", "integrity": "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ=="}, "has-tostringtag": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz", "integrity": "sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==", "requires": {"has-symbols": "^1.0.3"}}, "hasown": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz", "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "requires": {"function-bind": "^1.1.2"}}, "ieee754": {"version": "1.1.13", "resolved": "https://registry.npmjs.org/ieee754/-/ieee754-1.1.13.tgz", "integrity": "sha512-4vf7I2LYV/HaWerSo3XmlMkp5eZ83i+/CDluXi/IGTs/O1sejBNhTtnxzmRZfvOUqj7lZjqHkeTvpgSFDlWZTg=="}, "inherits": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="}, "is-arguments": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/is-arguments/-/is-arguments-1.1.1.tgz", "integrity": "sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==", "requires": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}}, "is-callable": {"version": "1.2.7", "resolved": "https://registry.npmjs.org/is-callable/-/is-callable-1.2.7.tgz", "integrity": "sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA=="}, "is-generator-function": {"version": "1.0.10", "resolved": "https://registry.npmjs.org/is-generator-function/-/is-generator-function-1.0.10.tgz", "integrity": "sha512-jsEjy9l3yiXEQ+PsXdmBwEPcOxaXWLspKdplFUVI9vq1iZgIekeC0L167qeu86czQaxed3q/Uzuw0swL0irL8A==", "requires": {"has-tostringtag": "^1.0.0"}}, "is-typed-array": {"version": "1.1.12", "resolved": "https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.12.tgz", "integrity": "sha512-Z14TF2JNG8Lss5/HMqt0//T9JeHXttXy5pH/DBU4vi98ozO2btxzq9MwYDZYnKwU8nRsz/+GVFVRDq3DkVuSPg==", "requires": {"which-typed-array": "^1.1.11"}}, "isarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "integrity": "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ=="}, "jmespath": {"version": "0.16.0", "resolved": "https://registry.npmjs.org/jmespath/-/jmespath-0.16.0.tgz", "integrity": "sha512-9FzQjJ7MATs1tSpnco1K6ayiYE3figslrXA72G2HQ/n76RzvYlofyi5QM+iX4YRs/pu3yzxlVQSST23+dMDknw=="}, "math-intrinsics": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "integrity": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g=="}, "mime-db": {"version": "1.52.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg=="}, "mime-types": {"version": "2.1.35", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "requires": {"mime-db": "1.52.0"}}, "proxy-from-env": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "integrity": "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg=="}, "punycode": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/punycode/-/punycode-1.3.2.tgz", "integrity": "sha512-RofWgt/7fL5wP1Y7fxE7/EmTLzQVnB0ycyibJ0OOHIlJqTNzglYFxVwETOcIoJqJmpDXJ9xImDv+Fq34F/d4Dw=="}, "querystring": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/querystring/-/querystring-0.2.0.tgz", "integrity": "sha512-X/xY82scca2tau62i9mDyU9K+I+djTMUsvwf7xnUX5GLvVzgJybOJf4Y6o9Zx3oJK/LSXg5tTZBjwzqVPaPO2g=="}, "sax": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/sax/-/sax-1.2.1.tgz", "integrity": "sha512-8I2a3LovHTOpm7NV5yOyO8IHqgVsfK4+UuySrXU8YXkSRX7k6hCV9b3HrkKCr3nMpgj+0bmocaJJWpvp1oc7ZA=="}, "strnum": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/strnum/-/strnum-1.0.5.tgz", "integrity": "sha512-J8bbNyKKXl5qYcR36TIO8W3mVGVHrmmxsd5PAItGkmyzwJvybiw2IVq5nqd0i4LSNSkB/sx9VHllbfFdr9k1JA=="}, "tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="}, "url": {"version": "0.10.3", "resolved": "https://registry.npmjs.org/url/-/url-0.10.3.tgz", "integrity": "sha512-hzSUW2q06EqL1gKM/a+obYHLIO6ct2hwPuviqTTOcfFVc61UbfJ2Q32+uGL/HCPxKqrdGB5QUwIe7UqlDgwsOQ==", "requires": {"punycode": "1.3.2", "querystring": "0.2.0"}}, "util": {"version": "0.12.5", "resolved": "https://registry.npmjs.org/util/-/util-0.12.5.tgz", "integrity": "sha512-kZf/K6hEIrWHI6XqOFUiiMa+79wE/D8Q+NCNAWclkyg3b4d2k7s0QGepNjiABc+aR3N1PAyHL7p6UcLY6LmrnA==", "requires": {"inherits": "^2.0.3", "is-arguments": "^1.0.4", "is-generator-function": "^1.0.7", "is-typed-array": "^1.1.3", "which-typed-array": "^1.1.2"}}, "uuid": {"version": "8.3.2", "resolved": "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz", "integrity": "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg=="}, "which-typed-array": {"version": "1.1.11", "resolved": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.11.tgz", "integrity": "sha512-qe9UWWpkeG5yzZ0tNYxDmd7vo58HDBc39mZ0xWWpolAGADdFOzkfamWLDxkOWcvHQKVmdTyQdLD4NOfjLWTKew==", "requires": {"available-typed-arrays": "^1.0.5", "call-bind": "^1.0.2", "for-each": "^0.3.3", "gopd": "^1.0.1", "has-tostringtag": "^1.0.0"}}, "xml2js": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/xml2js/-/xml2js-0.5.0.tgz", "integrity": "sha512-drPFnkQJik/O+uPKpqSgr22mpuFHqKdbS835iAQrUC73L2F5WkboIRd63ai/2Yg6I1jzifPFKH2NTK+cfglkIA==", "requires": {"sax": ">=0.6.0", "xmlbuilder": "~11.0.0"}}, "xmlbuilder": {"version": "11.0.1", "resolved": "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-11.0.1.tgz", "integrity": "sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA=="}}}