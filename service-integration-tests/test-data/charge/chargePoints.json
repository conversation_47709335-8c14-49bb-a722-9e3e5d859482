[{"__typename": "Chargepoint", "siteId": "BPCM-421", "provider": "BPCM", "apolloInternalId": "BPCM-24777", "apolloExternalId": "BPCM-24777", "providerInternalId": "24777", "providerExternalId": "24777", "free": false, "lastUsed": "2023-02-03T12:34:38.675Z", "hasConnectorsAvailable": true, "location": {"lat": 52.02182, "lon": -0.691694, "__typename": "GeoPoint"}, "site": {"siteId": "BPCM-421", "isOpen24Hours": false, "provider": "BPCM", "siteDetails": {"hotline": "+44 800 464 3444", "address": "Blackberry Court, Walnut Tree", "city": "Milton Keynes", "postcode": "MK7 7PB", "country": "UK", "geohash": "gcpxbq234", "siteProviderIds": [{"id": "421", "type": "serial", "__typename": "SiteProviderId"}], "location": {"lat": 52.02182, "lon": -0.691694, "__typename": "GeoPoint"}, "hours": {"mon1": "", "mon2": "", "tue1": "", "tue2": "", "wed1": "", "wed2": "", "thu1": "", "thu2": "", "fri1": "", "fri2": "", "sat1": "", "sat2": "", "sun1": "", "sun2": "", "__typename": "Hours"}, "__typename": "SiteDetails"}, "__typename": "Site"}, "connectors": [{"connectorInternalId": "24777-1", "connectorExternalId": "1", "connectorNumber": 1, "type": "CCS2", "rating": 50, "phase": "DC", "availability": {"state": "Available", "lastUpdate": "2023-02-22T19:00:49.433Z", "__typename": "ConnectorAvailability"}, "preAuth": {"amount": 40, "currency": "GBP", "__typename": "PreAuth"}, "__typename": "Connector"}, {"connectorInternalId": "24777-2", "connectorExternalId": "2", "connectorNumber": 2, "type": "CHADEMO", "rating": 50, "phase": "DC", "availability": {"state": "Available", "lastUpdate": "2023-02-22T19:00:49.433Z", "__typename": "ConnectorAvailability"}, "preAuth": {"amount": 40, "currency": "GBP", "__typename": "PreAuth"}, "__typename": "Connector"}, {"connectorInternalId": "24777-3", "connectorExternalId": "3", "connectorNumber": 3, "type": "TYPE_2", "rating": 43, "phase": "AC", "availability": {"state": "Available", "lastUpdate": "2023-02-22T19:00:49.433Z", "__typename": "ConnectorAvailability"}, "preAuth": {"amount": 40, "currency": "GBP", "__typename": "PreAuth"}, "__typename": "Connector"}], "evPrice": [{"connectorInternalId": "24777-1", "connectorExternalId": "1", "grossUnitCost": 0.63, "unit": "kWh", "currency": "GBP", "blockingFee": {"price": 10, "duration": 90, "unit": "HOUR", "__typename": "BlockingFee"}, "__typename": "ConnectorPrice"}, {"connectorInternalId": "24777-2", "connectorExternalId": "2", "grossUnitCost": 0.63, "unit": "kWh", "currency": "GBP", "blockingFee": {"price": 10, "duration": 90, "unit": "HOUR", "__typename": "BlockingFee"}, "__typename": "ConnectorPrice"}, {"connectorInternalId": "24777-3", "connectorExternalId": "3", "grossUnitCost": 0.63, "unit": "kWh", "currency": "GBP", "blockingFee": {"price": 10, "duration": 90, "unit": "HOUR", "__typename": "BlockingFee"}, "__typename": "ConnectorPrice"}], "costOfCharge": [{"tariff": {"type": "SUBSCRIBER", "cost": "0.52", "unit": "GBP", "multiplier": "Energy", "__typename": "CostOfChargeTariff"}, "__typename": "CostOfCharge"}, {"tariff": {"type": "PAYG", "cost": "0.63", "unit": "GBP", "multiplier": "Energy", "__typename": "CostOfChargeTariff"}, "__typename": "CostOfCharge"}, {"tariff": {"type": "GUEST", "cost": "0.65", "unit": "GBP", "multiplier": "Energy", "__typename": "CostOfChargeTariff"}, "__typename": "CostOfCharge"}], "schemes": [{"schemeId": 16, "__typename": "ChargepointScheme"}, {"schemeId": 92, "__typename": "ChargepointScheme"}], "availability": {"available": "Available", "lastUsed": "2023-02-03T12:34:38.675Z", "connectors": [{"connectorInternalId": "24777-1", "connectorExternalId": "1", "state": "Available", "__typename": "ConnectorAvailability"}, {"connectorInternalId": "24777-2", "connectorExternalId": "2", "state": "Available", "__typename": "ConnectorAvailability"}, {"connectorInternalId": "24777-3", "connectorExternalId": "3", "state": "Available", "__typename": "ConnectorAvailability"}], "__typename": "ChargepointAvailability"}}, {"__typename": "Chargepoint", "siteId": "aral-14091200", "provider": "hasToBe", "apolloInternalId": "hasToBe-585c13b0-ad84-439c-8656-f603999ce296", "apolloExternalId": "hasToBe-DE*BPE*E0F460*01", "providerInternalId": "585c13b0-ad84-439c-8656-f603999ce296", "providerExternalId": "DE*BPE*E0F460*01", "free": false, "lastUsed": null, "hasConnectorsAvailable": true, "location": {"lat": 51.4774873, "lon": 7.2278114, "__typename": "GeoPoint"}, "site": {"siteId": "aral-14091200", "isOpen24Hours": true, "provider": "aral", "siteDetails": {"hotline": "+49 800 1353511", "address": "Wittener Straße 66", "city": "Bochum", "postcode": "44789", "country": "DE", "geohash": "u1jhu7pn5", "siteProviderIds": [{"id": "0F460", "type": "SAP_ID", "__typename": "SiteProviderId"}], "location": {"lat": 51.477485, "lon": 7.227767, "__typename": "GeoPoint"}, "hours": {"mon1": "00:00", "mon2": "23:59", "tue1": "00:00", "tue2": "23:59", "wed1": "00:00", "wed2": "23:59", "thu1": "00:00", "thu2": "23:59", "fri1": "00:00", "fri2": "23:59", "sat1": "00:00", "sat2": "23:59", "sun1": "00:00", "sun2": "23:59", "__typename": "Hours"}, "__typename": "SiteDetails"}, "__typename": "Site"}, "connectors": [{"connectorInternalId": "595ffda7-7e9f-447b-a42a-399bf55d9de5", "connectorExternalId": "DE*BPE*E0F460*01*01", "connectorNumber": 1, "type": "CCS2", "rating": 300, "phase": "DC", "availability": {"state": "Available", "lastUpdate": null, "__typename": "ConnectorAvailability"}, "preAuth": {"amount": 60, "currency": "EUR", "__typename": "PreAuth"}, "__typename": "Connector"}, {"connectorInternalId": "93d5fa78-8661-4a76-ac87-aa15bd9de838", "connectorExternalId": "DE*BPE*E0F460*01*02", "connectorNumber": 2, "type": "CHADEMO", "rating": 100, "phase": "DC", "availability": {"state": "Available", "lastUpdate": null, "__typename": "ConnectorAvailability"}, "preAuth": {"amount": 60, "currency": "EUR", "__typename": "PreAuth"}, "__typename": "Connector"}, {"connectorInternalId": "52fb9707-6181-420e-8a79-2ecd99db6165", "connectorExternalId": "DE*BPE*E0F460*01*03", "connectorNumber": 3, "type": "CCS2", "rating": 300, "phase": "DC", "availability": {"state": "Available", "lastUpdate": null, "__typename": "ConnectorAvailability"}, "preAuth": {"amount": 60, "currency": "EUR", "__typename": "PreAuth"}, "__typename": "Connector"}], "evPrice": [], "costOfCharge": [], "schemes": [], "availability": {"available": "Available", "lastUsed": null, "connectors": [{"connectorInternalId": "595ffda7-7e9f-447b-a42a-399bf55d9de5", "connectorExternalId": "DE*BPE*E0F460*01*01", "state": "Available", "__typename": "ConnectorAvailability"}, {"connectorInternalId": "93d5fa78-8661-4a76-ac87-aa15bd9de838", "connectorExternalId": "DE*BPE*E0F460*01*02", "state": "Available", "__typename": "ConnectorAvailability"}, {"connectorInternalId": "52fb9707-6181-420e-8a79-2ecd99db6165", "connectorExternalId": "DE*BPE*E0F460*01*03", "state": "Available", "__typename": "ConnectorAvailability"}], "__typename": "ChargepointAvailability"}}, {"siteId": "DCS-ES:DCS:CHARGING_STATION:40db846b-7a2c-4735-b411-fc30596cb507", "apolloInternalId": "DCS-ES:DCS:CHARGE_POINT:40db846b-7a2c-4735-b411-fc30596cb507", "apolloExternalId": "DCS-ES*ION*E412101", "provider": "DCS", "providerInternalId": "ES:DCS:CHARGE_POINT:40db846b-7a2c-4735-b411-fc30596cb507", "providerExternalId": "ES*ION*E412101", "free": false, "lastUsed": null, "hasConnectorsAvailable": true, "location": {"lat": 38.824986782590436, "lon": -3.396253172136492, "__typename": "GeoPoint"}, "site": {"siteId": "DCS-ES:DCS:CHARGING_STATION:40db846b-7a2c-4735-b411-fc30596cb507", "isOpen24Hours": true, "provider": "DCS", "siteDetails": {"hotline": "+31887755444", "address": "Estación de Servicio Cepsa El Hidalgo A-4 km", "postcode": "13300", "city": "Valdepeñas", "country": "ES", "geohash": "eyvsfeygv", "siteProviderIds": [], "location": {"lat": 38.824986782590436, "lon": -3.396253172136492, "__typename": "GeoPoint"}, "hours": {"mon1": "", "mon2": "", "tue1": "", "tue2": "", "wed1": "", "wed2": "", "thu1": "", "thu2": "", "fri1": "", "fri2": "", "sat1": "", "sat2": "", "sun1": "", "sun2": "", "__typename": "Hours"}, "__typename": "SiteDetails"}, "__typename": "Site"}, "__typename": "Chargepoint", "availability": {"available": "Available", "lastUsed": null, "connectors": [{"connectorInternalId": "ES:DCS:CHARGE_POINT:40db846b-7a2c-4735-b411-fc30596cb507", "connectorExternalId": "ES*ION*E412101*1", "state": "Available", "__typename": "ConnectorAvailability"}], "__typename": "ChargepointAvailability"}, "evPrice": [{"connectorInternalId": "ES:DCS:CHARGE_POINT:40db846b-7a2c-4735-b411-fc30596cb507", "connectorExternalId": "DCS-ES*ION*E412101*1", "grossUnitCost": 0.49, "unit": "kWh", "currency": "EUR", "blockingFee": {"duration": 120, "unit": "MIN", "price": 0.15, "__typename": "BlockingFee"}, "__typename": "ConnectorPrice"}], "connectors": [{"connectorInternalId": "ES:DCS:CHARGE_POINT:40db846b-7a2c-4735-b411-fc30596cb507", "connectorExternalId": "ES*ION*E412101*1", "connectorNumber": null, "type": "TYPE_2", "rating": 11, "phase": null, "availability": {"state": "Available", "lastUpdate": null, "__typename": "ConnectorAvailability"}, "preAuth": {"amount": 45, "currency": "EUR", "__typename": "PreAuth"}, "__typename": "Connector"}], "costOfCharge": [], "schemes": []}]