import request from 'supertest';
const ngeohash = require('ngeohash');
require('dotenv').config();
const Ajv = require('ajv');
import CryptoJS from 'crypto-js';
const process = require('process');
import * as Const from './constants';
import * as fs from 'fs';
import * as path from 'path';
import pdfParse from 'pdf-parse';
import moment from 'moment-timezone';
import { v4 as uuidv4 } from 'uuid';
import puppeteer, { Page } from 'puppeteer';

class Utils {
  static decodeBase64(encoded: string): string {
    return Buffer.from(encoded, 'base64').toString('utf-8');
  }

  static decodeHtmlEntities(encodedUrl: string): string {
    return encodedUrl.replace(/&#x3D;/g, '=').replace(/&amp;/g, '&');
  }

  static async sendHttpRequest(
    method,
    endpoint,
    path = '',
    params = {},
    body = {},
    headers = {},
  ) {
    try {
      let requestInstance = request(endpoint)[method.toLowerCase()](path);

      for (const [key, value] of Object.entries(headers)) {
        requestInstance = requestInstance.set(key, value);
      }

      if (Object.keys(params).length > 0) {
        requestInstance = requestInstance.query(params);
      }

      if (
        ['post', 'put', 'patch', 'delete'].includes(method.toLowerCase()) &&
        Object.keys(body).length > 0
      ) {
        requestInstance = requestInstance.send(body); // Send JSON body
      }

      const response = await requestInstance;
      return response;
    } catch (error) {
      console.error(`Error in sending ${method} request:`, error);
      throw error;
    }
  }

  static generateGeohashesInViewport(viewport, precision = 5) {
    const { northeast, southwest } = viewport;
    const geohashes = new Set();
    const stepLat = (northeast.lat - southwest.lat) / 10;
    const stepLng = (northeast.lng - southwest.lng) / 10;

    for (let lat = southwest.lat; lat <= northeast.lat; lat += stepLat) {
      for (let lng = southwest.lng; lng <= northeast.lng; lng += stepLng) {
        const geohash = ngeohash.encode(lat, lng, precision);
        geohashes.add(geohash);
      }
    }
    return Array.from(geohashes);
  }

  static generateNearbyGeohashes(lat, lon, precision = 6, range = 5) {
    const geohashes = [];
    // Create a range of geohashes by modifying the coordinates slightly
    for (let i = -range; i <= range; i++) {
      for (let j = -range; j <= range; j++) {
        const newLat = lat + i * 0.001; // Adjust the latitude slightly
        const newLon = lon + j * 0.001; // Adjust the longitude slightly
        const geohash = ngeohash.encode(newLat, newLon, precision);
        geohashes.push(geohash);
      }
    }
    return geohashes;
  }

  static validateSchema(schema, data) {
    const ajv = new Ajv({ allErrors: true, strict: false }); // allErrors ensures it doesn't stop at the first error
    const validate = ajv.compile(schema);
    const valid = validate(data);

    if (!valid) {
      const errorMessages = validate.errors
        .map((err) => {
          if (err.keyword === 'additionalProperties') {
            return `${err.instancePath || 'root'} has unknown property '${
              err.params.additionalProperty
            }'`;
          }
          return `${err.instancePath || 'root'} ${err.message}`;
        })
        .join('; ');

      console.error(`Schema validation failed: ${errorMessages}`);
      return false;
    }

    return true;
  }

  static getRandomObject(jsonArray) {
    if (!Array.isArray(jsonArray) || jsonArray.length === 0) {
      throw new Error('The input must be a non-empty array');
    }
    return jsonArray[Math.floor(Math.random() * jsonArray.length)];
  }

  static getRandomItems(arr) {
    if (!Array.isArray(arr) || arr.length === 0) {
      return [];
    }
    const count = Math.floor(Math.random() * arr.length) + 1;
    return Array.from(
      new Set(
        Array.from(
          { length: count },
          () => arr[Math.floor(Math.random() * arr.length)],
        ),
      ),
    );
  }

  static extractSiteIds(data) {
    const siteIds = [];
    data.data.markersResponse.markers.forEach((marker) => {
      if (marker.siteIds) {
        siteIds.push(...marker.siteIds); // Add all items from siteIds array
      }
      // Check and add siteId if it exists (from MarkerPoint)
      if (marker.site && marker.site.siteId) {
        siteIds.push(marker.site.siteId);
      }
    });
    return siteIds;
  }

  static getConnectorTypes(json) {
    const connectorTypes = [];
    json.data.sites.forEach((site) => {
      site.chargepoints.forEach((chargepoint) => {
        chargepoint.connectors.forEach((connector) => {
          if (!connectorTypes.includes(connector.type)) {
            connectorTypes.push(connector.type); // Only add if not already present
          }
        });
      });
    });
    return connectorTypes;
  }

  static async delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  static findAvailableConnector(jsonData) {
    const connectors = jsonData.data.results[0].result.connectors;
    for (let connector of connectors) {
      if (connector.availability.state === 'Available') {
        return connector.connectorInternalId;
      }
    }
    return null;
  }

  static async readTwillioMessage(toNumber) {
    console.log(
      `Fetching SMS code for number ${toNumber} at server time ${await Utils.getServerTime()}`, //Logging server time to debug incorrect time on Twilio or the pipeline server.
    );
    const accountSid = Const.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    const credentials = Buffer.from(accountSid + ':' + authToken).toString(
      'base64',
    );

    const header = {
      Authorization: `Basic ${credentials}`,
    };

    const params = {
      To: toNumber,
      PageSize: 1,
    };

    let response;
    async function sendRequest() {
      try {
        response = await Utils.sendHttpRequest(
          'GET',
          'https://api.twilio.com',
          '/2010-04-01/Accounts/' + accountSid + '/Messages.json',
          params,
          {},
          header,
        );
      } catch (error) {
        console.error('Request failed:', error);
      }
    }

    const startTime = Date.now();
    const timeoutMs = 120_000;
    // waiting 120 seconds for the latest SMS message not older then 30 seconds
    while (Date.now() - startTime <= timeoutMs) {
      await sendRequest();
      const messages = response.body.messages;
      let timeDiff;
      if (messages.length > 0) {
        let dateSent = new Date(messages[0].date_sent).getTime();
        timeDiff = Math.abs(dateSent - startTime);
      }
      if (timeDiff < 30_000) {
        return messages[0];
      }
      await Utils.delay(2_000);
    }
    console.error('Cannot get message sent to ', toNumber);
  }

  static getStringBetween(
    fullString: string,
    startString: string,
    endString: string,
  ): string {
    const startIndex = fullString.indexOf(startString) + startString.length;
    const endIndex = fullString.indexOf(endString, startIndex);
    return fullString.substring(startIndex, endIndex);
  }

  static removeWhitespaces(str: string): string {
    return str.replace(/\s+/g, '');
  }

  static getStringBefore(str: string, delimiter: string): string {
    const index = str.indexOf(delimiter);
    if (index === -1) {
      return str; // delimiter not found, return whole string
    }
    return str.substring(0, index);
  }

  static getStringAfter(fullString: string, startString: string): string {
    const startIndex = fullString.indexOf(startString) + startString.length;
    return fullString.substring(startIndex).trim();
  }

  static generateRandomString = (length) => {
    let text = '';
    const possible =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._';
    for (let i = 0; i < length; i += 1) {
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
  };

  static generateCodeChallenge = (codeVerifier) => {
    const hash = CryptoJS.SHA256(codeVerifier);
    const codeChallenge = this.base64URL(hash);
    return codeChallenge;
  };

  static generateUUID(): string {
    return uuidv4();
  }

  static base64URL = (string) => {
    let base64string = '';
    if (typeof string === 'object') {
      base64string = string.toString(CryptoJS.enc.Base64);
    } else {
      const wordArray = CryptoJS.enc.Utf8.parse(string);
      base64string = CryptoJS.enc.Base64.stringify(wordArray);
    }
    return base64string
      .replace(/[=]/g, '')
      .replace(/\+/g, '-')
      .replace(/\//g, '_');
  };

  static buildUrl(
    baseUrl: string,
    path: string,
    params: Record<string, string>,
  ): string {
    const fullPath = `${baseUrl.replace(/\/+$/, '')}/${path.replace(
      /^\/+/,
      '',
    )}`;

    const queryString = Object.entries(params)
      .map(([key, value]) => {
        if (key === 'redirect_uri') {
          return `${key}=${value}`; // Prevents encoding of redirect_uri
        }
        return `${key}=${encodeURIComponent(value)}`; // Encodes other parameters
      })
      .join('&');

    return `${fullPath}?${queryString}`;
  }

  static replaceSubstring(originalString, targetSubstring, replacement) {
    return originalString.replace(targetSubstring, replacement);
  }

  static compareArrays(arr1: string[], arr2: string[]): boolean {
    if (arr1.length !== arr2.length) {
      return false;
    }
    const sortedArr1 = arr1.slice().sort();
    const sortedArr2 = arr2.slice().sort();
    return sortedArr1.every((value, index) => value === sortedArr2[index]);
  }

  /**
   * Creates from e.g. '<EMAIL>' 'BP_PULSE_TESTER'
   * @param email - user's email
   */
  static emailToEnvVar(email) {
    //creates from e.g. '<EMAIL>' 'BP_PULSE_TESTER'
    return email
      .split('@')[0] // Remove the domain
      .split('+')[0] // Remove everything after +
      .replace(/[^a-zA-Z0-9]/g, '_') // Replace non-alphanumeric characters with _
      .toUpperCase(); // Convert to uppercase
  }

  /**
   * Creates from e.g. '<EMAIL>' the '<EMAIL>'
   * @param email - user's email in either formats above
   */
  static sanitizeEmail(email: string): string {
    //creates from e.g. '<EMAIL>' '<EMAIL>'
    return email.replace(/\+[^@]+/, '');
  }

  static saveStringToFile(filename: string, data: string): void {
    const dir = path.dirname(filename); // Get the directory path

    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true }); // Create folder if it doesn't exist
    }

    fs.writeFileSync(filename, data, 'utf8'); // Write data to the file
  }

  static async saveBrowserScreenshot(
    filename: string,
    page: Page,
  ): Promise<void> {
    const dir = Const.LOGS_FILE_PATH;
    if (!fs.existsSync(dir)) {
      // Create folder if it doesn't exist
      fs.mkdirSync(dir, { recursive: true });
    }
    const testFileName = Utils.getStringBefore(
      path.basename(expect.getState().testPath),
      '.test.ts',
    );
    const fullPath = path.join(dir, testFileName + '.' + filename); // Construct the file name with the test name
    await page.screenshot({ path: fullPath, fullPage: true });
  }

  static readFileAsString(filePath: string): string {
    if (fs.existsSync(filePath)) {
      return fs.readFileSync(filePath, 'utf8');
    }
    return null;
  }

  static readJsonFile<T>(filePath: string): T | null {
    try {
      const data = fs.readFileSync(filePath, 'utf-8'); // Read file as string
      return JSON.parse(data) as T; // Parse JSON and return it
    } catch (error) {
      console.error(`Error reading JSON file: ${error.message}`);
      return null;
    }
  }

  /**
   * Finds the first 6-digit number in the given string where the number after "use this code:"
   * @param body - The string to search for the 6-digit number.
   * @returns The first 6-digit number as a string, or `null` if no match is found.
   */
  static findSixDigitNumber(body: string): string | null {
    const regex = /^\s*(\d{6})\s*$/m; // Match a 6-digit number on its own line
    const match = body.match(regex);
    return match ? match[1] : null;
  }

  static updateUserId(email, newExternalId, filePath) {
    const users = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    // Find user by email
    const user = users.find((u) => u.email === email);
    if (user) {
      user.externalId = newExternalId; // Update externalId
      fs.writeFileSync(filePath, JSON.stringify(users, null, 2), 'utf8');
    } else {
      console.log(`User with email ${email} not found!`);
    }
  }

  /**
   * Checks if the given JWT token is not expired.
   * @param {string} token - The JWT token.
   * @returns {boolean} - Returns true if the token is not expired, otherwise false.
   */
  static isTokenValid(token) {
    if (!token) return false;
    try {
      const payloadBase64 = token.split('.')[1]; // Extract the payload
      const payload = JSON.parse(atob(payloadBase64)); // Decode Base64 JSON
      if (!payload.exp) return false; // If no expiration field, assume invalid
      const currentTime = Math.floor(Date.now() / 1_000); // Get current time in seconds
      return payload.exp > currentTime; // Return true if token is not expired
    } catch (error) {
      console.error('Invalid token format:', error);
      return false;
    }
  }

  static parseNumber(value: string): number {
    const parsed = parseFloat(value.replace(',', '.'));
    return isNaN(parsed) ? 0 : parsed; // Return 0 if conversion fails
  }

  static getTimeDifferenceInSeconds(time1: string, time2: string): number {
    const date1 = new Date(time1).getTime(); // Convert to milliseconds
    const date2 = new Date(time2).getTime(); // Convert to milliseconds
    return Math.floor((date1 - date2) / 1_000); // Convert to seconds
  }

  static deleteFileIfExists(filePath: string): void {
    if (Utils.fileExists(filePath)) {
      try {
        fs.unlinkSync(filePath);
      } catch (error) {
        console.error(`Error deleting file: ${error.message}`);
      }
    }
  }

  static fileExists(filePath: string): boolean {
    return fs.existsSync(filePath);
  }

  static getFileAgeInSeconds(filePath: string): number | null {
    if (!Utils.fileExists(filePath)) {
      return null;
    }
    try {
      const stats = fs.statSync(filePath);
      const fileModifiedTime = stats.mtime.getTime(); // Last modified time in milliseconds
      const currentTime = Date.now(); // Current time in milliseconds
      return Math.floor((currentTime - fileModifiedTime) / 1_000); // Convert to seconds
    } catch (error) {
      console.error(`Error reading file: ${error.message}`);
      return null;
    }
  }

  static async downloadAndSavePDF(pdfUrl: string, savePath: string) {
    try {
      const response = await this.sendHttpRequest(
        'get',
        pdfUrl,
        '',
        {},
        {},
        { responseType: 'arraybuffer' },
      );

      if (!response || !response.body) {
        throw new Error('No PDF content received');
      }

      await Utils.saveStringToFile(savePath, response.body);
      return response;
    } catch (error) {
      console.error('Error downloading the PDF:', error);
    }
  }

  static async parsePDF(filePath: string) {
    try {
      const dataBuffer = fs.readFileSync(filePath); // Read the PDF file
      const data = await pdfParse(dataBuffer);
      return data.text;
    } catch (error) {
      console.error('Error parsing PDF:', error);
      return null;
    }
  }

  static convertTimeToUTC(dateStr: string, timeZone: string): Date | null {
    if (!moment.tz.zone(timeZone)) {
      console.error('Invalid time zone.');
      return null;
    }

    const localTime = moment.tz(dateStr, 'DD-MM-YYYY HH:mm', timeZone);
    if (!localTime.isValid()) {
      console.error('Invalid date string format.');
      return null;
    }

    return localTime.utc().toDate(); // Returns JavaScript Date in UTC
  }

  static truncateToMinute(isoTimestamp: string): string {
    return moment
      .utc(isoTimestamp)
      .startOf('minute')
      .format('YYYY-MM-DDTHH:mm:ss[Z]');
  }

  static getDateFromTimestamp(timestamp: string): string {
    const date = new Date(timestamp);
    const day = String(date.getUTCDate()).padStart(2, '0');
    const month = String(date.getUTCMonth() + 1).padStart(2, '0'); // Months are 0-based
    const year = date.getUTCFullYear();

    return `${day}-${month}-${year}`;
  }

  static formatDate(input: string, format: string): string {
    const date = new Date(input);
    const day = date.getUTCDate();
    const monthIndex = date.getUTCMonth(); // Months are zero-based
    const year = date.getUTCFullYear();

    const monthNames = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    const monthName = monthNames[monthIndex];
    const dayPadded = day.toString().padStart(2, '0');
    const monthNumber = (monthIndex + 1).toString().padStart(2, '0');

    return format
      .replace('dd', dayPadded)
      .replace('d', day.toString())
      .replace('MMMM', monthName)
      .replace('MM', monthNumber)
      .replace('yyyy', year.toString());
  }

  static getCurrencySymbol(currencyCode: string): string {
    const currencySymbols: { [key: string]: string } = {
      EUR: '€',
      GBP: '£',
      USD: '$',
    };
    return currencySymbols[currencyCode] || currencyCode;
  }

  static getJwtPayload<T = any>(jwt: string): T | null {
    try {
      const parts = jwt.split('.');
      if (parts.length !== 3) {
        throw new Error('Invalid JWT format');
      }

      const payloadBase64 = parts[1].replace(/-/g, '+').replace(/_/g, '/');

      // Add padding if needed
      const padded =
        payloadBase64 + '='.repeat((4 - (payloadBase64.length % 4)) % 4);

      const decodedPayload = Buffer.from(padded, 'base64').toString('utf-8');
      return JSON.parse(decodedPayload);
    } catch (error) {
      console.error('Failed to decode JWT payload:', error);
      return null;
    }
  }

  static async getServerTime(): Promise<String> {
    const params = {
      timeZone: 'UTC',
    };
    const response = await this.sendHttpRequest(
      'GET',
      'https://timeapi.io',
      '/api/Time/current/zone',
      params,
    );
    return response.body.dateTime;
  }
}

export default Utils;
