import { querySearchConnector } from '../tests/map/queries/searchConnector';
import { clearChargeSessionQuery } from '../tests/charge/queries/clearChargeSession';
import { startChargeQuery } from '../tests/charge/queries/startCharge';
import { stopChargeQuery } from '../tests/charge/queries/stopCharge';
import { getChargepointByConnectorIdQuery } from '../tests/charge/queries/getChargepointByConnectorId';
import * as Const from './constants';
import Utils from './utils';
import puppeteer, { <PERSON>, Browser } from 'puppeteer';
import * as userHelpers from '../shared/userHelpers';
import { ImapReader } from './imapReader';
import CIP_API from '../shared/cipApi';
import * as paymentHelper from '../shared/paymentHelper';
import creditCards from '../test-data/wallet/creditCards.json';
import * as path from 'path';
import getOnboardingStatusSchema from '../test-data/schema/getOnboardingStatus.json';
import { registeredPreAuthQuery } from '../tests/charge/queries/registeredPreAuth';

const googleMapsKey = process.env.G_MAPS_KEY;
const testFileName = path.basename(expect.getState().testPath);

export const findGeoHashes = async (locationName) => {
  let placeId = await searchLocation(locationName);
  let viewport = await getViewport(placeId);
  return await Utils.generateGeohashesInViewport(viewport);
};

export const searchLocation = async (locationName) => {
  const path = '/maps/api/place/autocomplete/json';
  const params = {
    key: googleMapsKey,
    input: locationName,
  };

  let response;
  async function makeRequest() {
    try {
      response = await Utils.sendHttpRequest(
        'POST',
        Const.G_MAPS_ENDPOINT,
        path,
        params,
      );
    } catch (error) {
      console.error('Request failed:', error);
    }
  }

  await makeRequest();
  expect(response.status).toBe(200);
  if (!response || !response.body || !response.body.predictions) {
    console.error('No predictions found in the response.');
    return null;
  }
  let foundPlaces = response.body.predictions;
  let foundPlace = foundPlaces.find((obj) => obj.description === locationName);
  if (!foundPlace) {
    console.log(`Place not found for city: ${locationName}`);
    return null;
  }
  let foundPlaceId = foundPlace.place_id;
  return foundPlaceId;
};

export const getViewport = async (placeId) => {
  const path = '/maps/api/place/details/json';
  const params = {
    key: googleMapsKey,
    place_id: placeId,
  };
  try {
    const response = await Utils.sendHttpRequest(
      'GET',
      Const.G_MAPS_ENDPOINT,
      path,
      params,
    );
    if (
      !response ||
      !response.body ||
      !response.body.result ||
      !response.body.result.geometry ||
      !response.body.result.geometry.viewport
    ) {
      console.error('Viewport data not found in response.');
      return null;
    }
    const viewport = response.body.result.geometry.viewport;
    return viewport;
  } catch (error) {
    console.error('Request failed:', error);
    return null;
  }
};

export const searchConnectorById = async (connectorId, token) => {
  const body = {
    operationName: 'SEARCH_QUERY',
    query: querySearchConnector,
    variables: {
      input: connectorId,
    },
  };
  const header = {
    'x-api-key': process.env.API_GATEWAY_KEY,
    Authorization: token,
    'user-agent': Const.USER_AGENT,
  };

  let response;
  async function sendRequest() {
    try {
      response = await Utils.sendHttpRequest(
        'POST',
        process.env.GATEWAY_URL,
        process.env.GATEWAY_USER_PATH,
        {},
        body,
        header,
      );
    } catch (error) {
      console.error('Request failed:', error);
    }
  }
  await sendRequest();
  return response;
};

export const clearChargeSession = async (userId, token) => {
  let body = {
    operationName: 'clearChargeSession',
    variables: {
      userId: userId,
    },
    query: clearChargeSessionQuery,
  };
  const header = {
    'x-api-key': process.env.API_GATEWAY_KEY,
    Authorization: token,
    'user-agent': Const.USER_AGENT,
  };

  let response;
  async function sendRequest() {
    try {
      response = await Utils.sendHttpRequest(
        'POST',
        process.env.GATEWAY_URL,
        process.env.GATEWAY_USER_PATH,
        {},
        body,
        header,
      );
    } catch (error) {
      console.error('Request failed:', error);
    }
  }
  await sendRequest();
  return response;
};

export const getChargepointByConnectorId = async (token, connectorIds) => {
  let body = {
    operationName: 'getChargepointByConnectorId',
    query: getChargepointByConnectorIdQuery,
    variables: {
      connectorIds: connectorIds,
    },
  };
  const header = {
    'x-api-key': process.env.API_GATEWAY_KEY,
    Authorization: token,
    'user-agent': Const.USER_AGENT,
  };

  let response;
  async function sendRequest() {
    try {
      response = await Utils.sendHttpRequest(
        'POST',
        process.env.GATEWAY_URL,
        process.env.GATEWAY_USER_PATH,
        {},
        body,
        header,
      );
    } catch (error) {
      console.error('Request failed:', error);
    }
  }
  await sendRequest();
  return response;
};

export const startCharge = async (
  userId,
  token,
  apolloInternalId,
  connectorInternalId,
) => {
  let body = {
    operationName: 'startCharge',
    variables: {
      userId: userId,
      apolloInternalId: apolloInternalId,
      connectorInternalId: connectorInternalId,
    },
    query: startChargeQuery,
  };
  const header = {
    'x-api-key': process.env.API_GATEWAY_KEY,
    Authorization: token,
    'user-agent': Const.USER_AGENT,
  };

  let response;
  async function sendRequest() {
    try {
      response = await Utils.sendHttpRequest(
        'POST',
        process.env.GATEWAY_URL,
        process.env.GATEWAY_USER_PATH,
        {},
        body,
        header,
      );
    } catch (error) {
      console.error('Request failed:', error);
    }
  }
  await sendRequest();
  await sendRequest();
  return response;
};

export const stopCharge = async (
  userId,
  token,
  apolloInternalId,
  connectorInternalId,
) => {
  let body = {
    operationName: 'stopCharge',
    variables: {
      apolloInternalId: apolloInternalId,
      connectorInternalId: connectorInternalId,
      userId: userId,
    },
    query: stopChargeQuery,
  };
  const header = {
    'x-api-key': process.env.API_GATEWAY_KEY,
    Authorization: token,
    'user-agent': Const.USER_AGENT,
  };

  let response;
  async function sendRequest() {
    try {
      response = await Utils.sendHttpRequest(
        'POST',
        process.env.GATEWAY_URL,
        process.env.GATEWAY_USER_PATH,
        {},
        body,
        header,
      );
    } catch (error) {
      console.error('Request failed:', error);
    }
  }

  await sendRequest();
  return response;
};

export const loginUser = async (user) => {
  //valid token may be already saved in this file
  const accessTokenPath =
    Const.TEMP_FOLDER + user.email + Const.USER_TOKEN_SUFFIX;
  const userAccessToken = Utils.readFileAsString(accessTokenPath);

  if (userAccessToken && Utils.isTokenValid(userAccessToken)) {
    console.log('Using saved access token for user ' + user.email);
    return userAccessToken;
  }
  let testContext = {
    loginUrl: '',
    codeChallenge: '',
    state: '',
    authorizeUrl: '',
    locationCode: '',
    accessToken: '',
    codeVerifier: '',
    gmailAccessToken: '',
    emailCode: '',
  };

  let browser: Browser;
  let page: Page;
  let userResponse;
  let tokenResponse;

  testContext = await generateLoginUrl(testContext);
  expect(testContext.loginUrl).toContain(Const.CIP_LOGIN_URL);

  browser = await puppeteer.launch({ headless: true });
  page = await browser.newPage();
  await enterEmailOrPhone(page, testContext, user.email);
  await verifyCorrectEmail(page, user.email);

  testContext.emailCode = await getEmailLoginCode(user.email);
  expect(testContext.emailCode.length).toBe(6);

  await enterEmailCode(page, testContext.emailCode);
  await skipPasskeysPrompt(page);
  testContext.locationCode = await getLocationCode(page);
  console.log(
    `Fetched location code: ${testContext.locationCode} for user ${user.email}`,
  );
  expect(testContext.locationCode).toBeDefined();
  expect(testContext.locationCode.length).toBeGreaterThan(20);

  tokenResponse = await userHelpers.getUserAccessToken(testContext);
  expect(tokenResponse.status).toBe(200);
  expect(tokenResponse.body?.access_token?.length).toBeGreaterThan(100);
  testContext.accessToken = tokenResponse.body.access_token;
  const accessTokenPyload = Utils.getJwtPayload(testContext.accessToken);
  console.log(
    `reading user external_id from jwt for user ${
      user.email
    }\r\n ${JSON.stringify(
      accessTokenPyload.external_id,
      null,
      2,
    )}\r\n for test ${testFileName}`,
  );
  userResponse = await userHelpers.getUserInformation(testContext.accessToken);
  expect(userResponse.status).toBe(200);
  expect(userResponse.body?.email?.toLowerCase()).toBe(
    user.email.toLowerCase(),
  );

  if (browser) {
    await browser.close();
  }
  Utils.saveStringToFile(accessTokenPath, testContext.accessToken);
  return testContext.accessToken;
};

export const generateLoginUrl = async (testContext) => {
  testContext.codeVerifier = Utils.generateRandomString(128);
  testContext.codeChallenge = Utils.generateCodeChallenge(
    testContext.codeVerifier,
  );
  testContext.state = Utils.generateRandomString(16);

  testContext.authorizeUrl = Utils.buildUrl(
    Const.CIP_BASE_URL,
    Const.CIP_AUTHORIZE_PATH,
    {
      client_id: 'PulseontheGo',
      code_challenge: testContext.codeChallenge,
      code_challenge_method: 'S256',
      collectEmail: 'enforce',
      disableSocialAuthentication: 'false',
      locale: 'en-US',
      redirect_uri: Const.CIP_REDIRECT_URL + Const.CIP_REDIRECT_PATH,
      response_type: 'code',
      scope: 'openid email phone profile b2c-consent b2c-contact b2c-profile',
      startJourney: 'Login',
      state: testContext.state,
    },
  );

  testContext.loginUrl = Utils.buildUrl(
    Const.CIP_LOGIN_URL,
    Const.CIP_LOGIN_PATH,
    {
      goto: testContext.authorizeUrl,
      realm: '/bravo',
      locale: 'en-US',
    },
  );
  return testContext;
};

export const enterRegistrationPhoneNumber = async (page, testContext, user) => {
  await page.goto(testContext.registrationUrl, { waitUntil: 'networkidle0' });
  let inputPhone;
  try {
    inputPhone = await page.waitForSelector(
      'xpath=//input[@data-testid="registration.mobile-number.input"]',
      { visible: true, timeout: 60_000 },
    );
  } catch (error) {
    await Utils.saveBrowserScreenshot('inputPhone.png', page);
    throw new Error('Phone number input field not found.');
  }

  await inputPhone.type(user.phoneNumber);
  await Utils.delay(1_000);
  const enteredPhone = await page.evaluate(
    (el) => (el as HTMLInputElement).value,
    inputPhone,
  );
  try {
    console.log(
      `Entered phone number: ${user.phoneNumber} for test ${testFileName}`,
    );
    expect(enteredPhone).toBe(user.phoneNumber);
  } catch (error) {
    console.error(
      '❌ Assertion failed: enteredPhone does not match user.phoneNumber',
    );
    await Utils.saveBrowserScreenshot('enteredPhone.png', page);
    throw error;
  }

  let buttonContinue;
  try {
    buttonContinue = await page.waitForSelector(
      'xpath=//*[@data-testid="continue-button" and not (@disabled)]',
      { visible: true, timeout: 5_000 },
    );
  } catch (error) {
    await Utils.saveBrowserScreenshot('enteredPhoneContinue.png', page);
    throw new Error(
      'Continue button after entering the phone number not found.',
    );
  }

  await buttonContinue.click();
};

export const enterEmailOrPhone = async (page, testContext, phoneOrEmail) => {
  await page.goto(testContext.loginUrl, { waitUntil: 'networkidle0' });
  try {
    await page.waitForFunction(
      (expectedTitle) => document.title === expectedTitle,
      { timeout: 2_000 },
      'Sign in',
    );
  } catch (error) {
    await Utils.saveBrowserScreenshot('signIn.png', page);
    throw new Error('Sign in page not found');
  }

  const inputPhoneEmail = await page.waitForSelector(
    'xpath=//input[@data-testid="login-landing-page.login-input"]',
    { visible: true },
  );
  await inputPhoneEmail.type(phoneOrEmail);

  const enteredPhoneEmail = await page.evaluate(
    (el) => (el as HTMLInputElement).value,
    inputPhoneEmail,
  );

  try {
    expect(enteredPhoneEmail).toBe(phoneOrEmail);
    console.log(
      `Entered phone or email: ${enteredPhoneEmail} for test ${testFileName}`,
    );
  } catch (error) {
    console.error(
      '❌ Assertion failed: enteredPhoneEmail does not match phoneOrEmail',
    );
    await Utils.saveBrowserScreenshot('enteredPhoneEmail.png', page);
    throw error; // Re-throw to ensure the test still fails
  }

  try {
    const buttonContinue = await page.waitForSelector(
      'xpath=//*[@data-testid="login-landing-page.continue-button" and not (@disabled)]',
      { visible: true },
    );
    await buttonContinue.click();
  } catch (error) {
    await Utils.saveBrowserScreenshot('enteredPhoneEmailContinue.png', page);
    throw new Error('Continue button after entering phone or email not found.');
  }
  await Utils.delay(1_000);
};

export const verifyCorrectEmail = async (page, email) => {
  let textSentEmailTo; //Verify on 'Verify your email' page the email address
  try {
    textSentEmailTo = await page.waitForSelector(
      'xpath=//*[@data-testid="login-magic-link.label"]/a',
      { visible: true, timeout: 10_000 },
    );
  } catch (error) {
    await Utils.saveBrowserScreenshot('verifyYourEmail.png', page);
    throw new Error('Verify your email page not found.');
  }

  const sentEmailText = await page.evaluate(
    (el) => el.textContent?.trim(),
    textSentEmailTo,
  );
  console.log(`Email sent text: ${sentEmailText} for test ${testFileName}`);
  expect(sentEmailText).toEqual(email);
};

export const verifyCorrectPhone = async (page, phone) => {
  let sentSmsCodeToEl; //Verify on 'Enter your Code page' the phone number
  try {
    sentSmsCodeToEl = await page.waitForSelector(
      'xpath=//*[@data-testid="form-input-otp-label-copy"]',
      { visible: true, timeout: 10_000 },
    );
  } catch (error) {
    await Utils.saveBrowserScreenshot('enterSmsCode.png', page);
    throw new Error('Enter your Code page not found.');
  }

  const sentSmsCodeToText = await page.evaluate(
    (el) => el.textContent?.trim(),
    sentSmsCodeToEl,
  );
  expect(sentSmsCodeToText).toContain(phone);
  console.log(`${sentSmsCodeToText} for test ${testFileName}`);
};

const getEmailMessage = async (
  email: string,
  subject: string,
  from: string,
  parseFn: (message: string) => string,
): Promise<string> => {
  const baseEmail = Utils.sanitizeEmail(email);
  const emailVar = Utils.emailToEnvVar(baseEmail);
  const appPassword = process.env['GMAIL_APP_PASSWORD_' + emailVar];

  const imapConfig = {
    user: email,
    password: appPassword,
    host: 'imap.gmail.com',
    port: 993,
    tls: true,
    connTimeout: 20_000,
    authTimeout: 20_000,
    tlsOptions: { rejectUnauthorized: false },
  };

  const emailProps = { from, to: email, subject };
  const reader = new ImapReader(imapConfig);
  let messageBody = '';
  try {
    messageBody = await reader.getEmail(emailProps);
  } catch (err) {
    console.error(err);
  } finally {
    reader.disconnect();
  }

  return parseFn(messageBody);
};

export const getVerificationLink = async (email: string): Promise<string> => {
  return getEmailMessage(
    email,
    'Verify your email address',
    '"bp Accounts Team" <<EMAIL>>',
    (message) =>
      Utils.getStringBetween(message, 'Tap to verify account\n[', ']'),
  );
};

export const getEmailVerificationCode = async (
  email: string,
): Promise<string> => {
  return getEmailMessage(
    email,
    'Verify your email address',
    '"bp Accounts Team" <<EMAIL>>',
    (message) => {
      const code = Utils.findSixDigitNumber(message);
      console.log(`Received verification code ${code} for user ${email}`);
      return code;
    },
  );
};

export const getEmailLoginCode = async (email: string): Promise<string> => {
  return getEmailMessage(
    email,
    'One-time login code',
    '"bp Accounts Team" <<EMAIL>>',
    (message) => {
      const code = Utils.findSixDigitNumber(message);
      console.log(`Received login code ${code} for user ${email}`);
      return code;
    },
  );
};

export const verifyDeleteAccountConfirmationEmail = async (
  email: string,
): Promise<string> => {
  return getEmailMessage(
    email,
    'Account Deletion Requested',
    '"bp Data Privacy Team" <<EMAIL>>',
    (message) => {
      return message;
    },
  );
};

export const getVerifyYourEmailLink = async (
  email: string,
): Promise<string> => {
  return getEmailMessage(
    email,
    'Verify your email',
    '"bp Accounts Team" <<EMAIL>>',
    (message) => Utils.getStringBetween(message, 'Tap to verify email\n[', ']'),
  );
};

export const getSmsCode = async (phoneNumber) => {
  const latestMessage = await Utils.readTwillioMessage(phoneNumber);
  expect(Object.keys(latestMessage).length).toBeGreaterThan(0);

  const smsCode = Utils.getStringAfter(
    latestMessage.body,
    'Your bp verification code is: ',
  );
  expect(smsCode.length).toBeGreaterThan(5);
  console.log(`Received SMS code ${smsCode} for mobile number ${phoneNumber}`);
  return smsCode;
};

export const enterSmsCode = async (page, smsCode) => {
  let inputSms;
  try {
    inputSms = 'xpath=//*[contains(@data-testid, "form-input-otp-input-")]';
    await page.waitForSelector(inputSms, { visible: true });
  } catch (error) {
    await Utils.saveBrowserScreenshot('enterSmsCode.png', page);
    throw new Error('SMS code input field not found.');
  }

  const elements = await page.$$(inputSms);
  for (let i = 0; i < elements.length; i++) {
    await elements[i].evaluate((el) => el);
    elements[i].type(smsCode[i]);
    expect(smsCode[i]).toBe(smsCode[i]);
  }
  console.log(`Entered SMS code ${smsCode} for test ${testFileName}`);

  try {
    const buttonContinue = await page.waitForSelector(
      'xpath=//*[@data-testid="login-otp-confirmation-submit" and not(@disabled)]',
      { visible: true, timeout: 5_000 },
    );
    await buttonContinue.click();
  } catch (error) {
    await Utils.saveBrowserScreenshot('enteredSmsCode.png', page);
    throw new Error('Continue button after SMS code entry was not found.');
  }
};

export const enterRegistrationEmail = async (page, email) => {
  let inpuEmail;
  try {
    inpuEmail = await page.waitForSelector(
      'xpath=//input[@data-testid="registration.email-capture.email-field"]',
      { visible: true },
    );
    await inpuEmail.click();
    await inpuEmail.type(email);
  } catch (error) {
    await Utils.saveBrowserScreenshot('addEmailAddress.png', page);
    throw new Error('Add email address page not found.');
  }

  const enteredEmail = await page.evaluate(
    (el) => (el as HTMLInputElement).value,
    inpuEmail,
  );
  try {
    expect(enteredEmail).toBe(email);
  } catch (error) {
    console.error(
      '❌ Assertion failed: enteredEmail does not match user email',
    );
    await Utils.saveBrowserScreenshot('enteredRegEmail.png', page);
    throw error; // Re-throw to ensure the test still fails
  }

  try {
    const buttonContinue = await page.waitForSelector(
      'xpath=//*[@data-testid="registration.email-capture.continue-button" and not (@disabled)]',
      { visible: true },
    );
    await buttonContinue.click();
  } catch (error) {
    await Utils.saveBrowserScreenshot('regEmailContinue.png', page);
    throw new Error(
      'Continue button after entering registration email not found.',
    );
  }

  let sentEmailTo;
  try {
    sentEmailTo = await page.waitForSelector(
      'xpath=//*[@data-testid="registration-email-otp.label"]',
      { visible: true },
    );
  } catch (error) {
    await Utils.saveBrowserScreenshot('verifyYourEmail.png', page);
    throw new Error('Verify your email page not found.');
  }

  const sentEmailText = await page.evaluate(
    (el) => el.textContent,
    sentEmailTo,
  );
  expect(await sentEmailText).toContain(email);
};

export const enterEmailCode = async (page, emailCode) => {
  let inputCode;
  try {
    inputCode = 'xpath=//*[contains(@data-testid, "form-input-otp-input-")]';
    await page.waitForSelector(inputCode, { visible: true });
  } catch (error) {
    await Utils.saveBrowserScreenshot('checkYourEmail.png', page);
    throw new Error('Check Your Email page was not found.');
  }

  const elements = await page.$$(inputCode);
  for (let i = 0; i < elements.length; i++) {
    await elements[i].evaluate((el) => el);
    elements[i].type(emailCode[i]);
    expect(emailCode[i]).toBe(emailCode[i]);
  }
  console.log(`Entered login code ${emailCode} for test ${testFileName}`);

  try {
    const buttonContinue = await page.waitForSelector(
      'xpath=//*[@data-testid="magic-link-sent.submit-passcode" and not(@disabled)] | //*[@data-testid="email-otp-sent.submit-otp" and not(@disabled)]',
      { visible: true, timeout: 10_000 },
    );
    await buttonContinue.click();
  } catch (error) {
    await Utils.saveBrowserScreenshot('continueEnteredEmailCode.png', page);
    throw new Error('Continue button after email code entry was not found.');
  }
};

export const skipPasskeysPrompt = async (page) => {
  // Try to skip optional passkey sign-in prompt
  try {
    const skipButton = await page.waitForSelector(
      'xpath=//*[@data-testid="passkey-registration-prompt.skip-button"]',
      { visible: true, timeout: 3_000 },
    );
    await skipButton.click();
    console.info('Passkey prompt was shown and skipped.');
  } catch {
    console.info('No passkey prompt shown — continuing.');
  }
};

export const getLocationCode = async (page) => {
  // ✅ Wait for a response that contains "code="
  const response = await page.waitForResponse((response) => {
    const location = response.headers()['location'];
    return location && location.includes('code=');
  });

  const location = response.headers()['location'];
  if (!location) {
    console.error('Cannot get location from headers: ', response.headers());
  }

  const locationCode = Utils.getStringBetween(location, 'code=', '&iss=');
  expect(locationCode.length).toBeGreaterThan(20);
  return locationCode;
};

export const setConsent = async (accessToken, consent) => {
  let response = await userHelpers.setUserConsent(accessToken, consent);
  expect(response.status).toBe(200);
  expect(response.body.length).toBe(1);
  expect(response.body[0].success).toBe(true);
  expect(response.body[0].errors.length).toBe(0);
  expect(response.body[0].consentId.length).toBeGreaterThan(0);
};

export const waitForChargeSessionData = async (filePath, TIMEOUT_MS) => {
  // waiting for the chargeSession file to be created by the charge.test.ts
  const startTime = Date.now();
  let chargeSession;
  while (Date.now() - startTime <= TIMEOUT_MS) {
    const fileAge = Utils.getFileAgeInSeconds(filePath);
    if (fileAge && fileAge < 10) {
      //make sure we use just created file
      chargeSession = Utils.readJsonFile(filePath);
      break;
    }
    await Utils.delay(3_000);
  }
  expect(chargeSession).not.toBeNull();
  return chargeSession;
};

export const deleteUser = async (user) => {
  const forgerockToken = await userHelpers.getForgerockToken();
  expect(forgerockToken.status).toBe(200);
  const forgerockAccessToken = forgerockToken.body.access_token;
  expect(forgerockAccessToken.length).toBeGreaterThan(100);
  let userIdResponse = await userHelpers.getForgerockUserId(
    forgerockAccessToken,
    'mail',
    user.email,
  );
  expect(userIdResponse.status).toBe(200);

  if (userIdResponse.body.resultCount > 0) {
    expect(userIdResponse.body.resultCount).toBe(1);
    expect(userIdResponse.body.result[0].mail).toBe(user.email);
    const forgerockUserId = userIdResponse.body.result[0]._id;
    expect(forgerockUserId.length).toBeGreaterThan(1);

    let deleteResponse = await userHelpers.deleteForgerockUser(
      forgerockAccessToken,
      forgerockUserId,
    );
    expect(deleteResponse.status).toBe(200);
    expect(deleteResponse.body._id).toBe(forgerockUserId);
  }

  const startTime = Date.now();
  const timeoutMs = 5_000;
  while (Date.now() - startTime <= timeoutMs) {
    // waiting for the user to be deleted
    userIdResponse = await userHelpers.getForgerockUserId(
      forgerockAccessToken,
      'mail',
      user.email,
    );
    expect(userIdResponse.status).toBe(200);
    if (userIdResponse.body.resultCount === 0) {
      break;
    }
    await Utils.delay(2_000);
  }
  Utils.deleteFileIfExists(
    Const.TEMP_FOLDER + user.email + Const.USER_TOKEN_SUFFIX,
  );
  //to not use saved token based on the email which is not usefull anymore afte the user deletion
};

export const createCipUser = async (user) => {
  //Creating a test user that does not require a mobile number.
  let response = await CIP_API.getAccessToken();
  expect(response.status === 200).toBeTruthyWithMessage(
    'Unexpected response \n' + JSON.stringify(response, null, 2),
  );

  expect(response.body.access_token.length).toBeGreaterThan(100);
  let userCreatedResponse = await CIP_API.createUser(
    response.body.access_token,
    user.email,
  );
  expect(userCreatedResponse.status).toBe(201);
  expect(userCreatedResponse.body.externalId.length).toBeGreaterThan(10);
  user.externalId = userCreatedResponse.body.externalId;
};

export const getUserAccessToken = async (user, testContext) => {
  let tokenResponse = await userHelpers.getUserAccessToken(testContext);
  expect(tokenResponse.status).toBe(200);
  expect(tokenResponse.body.access_token.length).toBeGreaterThan(100);
  expect(tokenResponse.body.refresh_token.length).toBeGreaterThan(100);
  expect(tokenResponse.body.id_token.length).toBeGreaterThan(100);
  return tokenResponse.body.access_token;
};

export const setUserContactInfo = async (user, accessToken) => {
  let response = await userHelpers.setContactInfo(accessToken, user);
  expect(response.status).toBe(200);
  expect(response.body.message).toBe(
    'Request received and queued for processing.',
  );
};

export const setUserConsent = async (user, testContext, consent) => {
  if (user.countryCode !== 'UK') {
    //adding country postfix for some consents
    if (consent.consentType === 'EV Privacy Policy') {
      consent.consentType = consent.consentType + ' ' + user.countryCode;
    }
    if (consent.consentType === 'EV Terms and Conditions') {
      consent.consentType = consent.consentType + ' ' + user.countryCode;
    }
  }
  let consentData = [
    {
      consentType: consent.consentType,
      accepted: consent.accepted,
      channel: consent.channel,
    },
  ];
  await setConsent(testContext.accessToken, consentData);
};

export const verifyUserContactInfo = async (user, testContext) => {
  let response;
  const startTime = Date.now();
  const timeoutMs = 20_000;
  // waiting until the contact information are populated
  while (Date.now() - startTime <= timeoutMs) {
    response = await userHelpers.getContactInformation(testContext.accessToken);
    expect(response.status).toBe(200);
    if (
      response.body &&
      response.body.firstName != null &&
      response.body.lastName != null &&
      response.body.country != null
    ) {
      break;
    }
    await Utils.delay(1_000);
  }
  expect(response.body.firstName).toBe(user.firstName);
  expect(response.body.lastName).toBe(user.lastName);
  expect(response.body.country).toBe(user.countryCode);
  expect(response.body.email.toLowerCase()).toBe(user.email.toLowerCase());
  expect(response.body.identityExternalId).toBe(user.externalId);
  //Utils.updateUserId(user.email, user.externalId, userFilePath);
};

export const verifyUserConsents = async (user, testContext) => {
  let response = await userHelpers.getUserConsent(testContext.accessToken);
  expect(response.status).toBe(200);
  expect(response.body.records.length).toBe(user.consents.length);

  user.consents.forEach((consent) => {
    const currentConsent = response.body.records.find(
      (record) => record.consentType === consent.consentType,
    );
    expect(currentConsent.accepted).toBe(consent.accepted);
    if (consent.channel) {
      expect(consent.channel).toContain(currentConsent.channel);
    }
    expect(currentConsent.identityExternalId).toBe(user.externalId);
  });
};

export const onboardAccount = async (user, testContext) => {
  let response = await userHelpers.onboardAccount(
    testContext.accessToken,
    user,
  );
  expect(response.status).toBe(200);
  expect(response.body.data.onboardAccount.success).toBe(true);
  expect(response.body.data.onboardAccount.message).toBe(
    'User account onboarding request completed',
  );
};

export const verifyUserIsOnboarded = async (user, testContext) => {
  let response = await userHelpers.getOnboardingStatus(
    testContext.accessToken,
    user.externalId,
  );
  expect(response.status).toBe(200);
  expect(response.body.data.onboardingStatus.account).toBe(true);
  expect(response.body.data.onboardingStatus.country).toBe(user.countryCode);
  expect(response.body.data.onboardingStatus.providers.length).toBeGreaterThan(
    0,
  );

  expect(
    Utils.validateSchema(getOnboardingStatusSchema, response.body),
  ).toBeTruthyWithMessage(
    'Schema validation failed for the response: \n' +
      JSON.stringify(response.body, null, 2),
  );
};

export const reRegisterUser = async (user, testContext) => {
  await deleteUser(user);
  await createCipUser(user);
  testContext = await generateLoginUrl(testContext);
  expect(testContext.loginUrl).toContain(Const.CIP_LOGIN_URL);

  let browser: Browser | null = null;
  let page: Page;

  browser = await puppeteer.launch({ headless: true });
  page = await browser.newPage();
  await enterEmailOrPhone(page, testContext, user.email);
  await verifyCorrectEmail(page, user.email);
  testContext.emailCode = await getEmailLoginCode(user.email);
  console.log(
    `Fetched login code: ${testContext.emailCode} for user ${user.email}`,
  );
  expect(testContext.emailCode.length).toBe(6);

  await enterEmailCode(page, testContext.emailCode);

  await skipPasskeysPrompt(page);

  const buttonContinueTerms = await page.waitForSelector(
    'xpath=//*[@data-testid="login.terms-and-conditions.continue-button" and not(@disabled)]',
    { visible: true, timeout: 5_000 },
  );
  await buttonContinueTerms.click();
  testContext.locationCode = await getLocationCode(page);

  expect(testContext.locationCode.length).toBeGreaterThan(20);

  if (browser) {
    await browser.close();
  }

  testContext.accessToken = await getUserAccessToken(user, testContext);
  await setUserContactInfo(user, testContext.accessToken);

  for (const consent of user.consents) {
    await setUserConsent(user, testContext, consent);
  }

  await verifyUserContactInfo(user, testContext);
  await verifyUserConsents(user, testContext);
  await onboardAccount(user, testContext);
  await verifyUserIsOnboarded(user, testContext);
};

export const addPaymentMethodIfNotExists = async (user, testContext) => {
  //add payment method if it doesn't exists or is in 'pending deletion state' to be able to charge
  let paymentMethodsResponse = await paymentHelper.getPaymentMethodsWallet(
    user.externalId,
    testContext.accessToken,
  );
  expect(paymentMethodsResponse.status).toBe(200);
  let paymentMethods = paymentMethodsResponse.body.data.getPaymentMethodsWallet;
  if (
    !paymentMethods ||
    paymentMethods?.length === 0 ||
    (paymentMethods?.length === 1 && paymentMethods[0]?.deleteDate != null)
  ) {
    if (!testContext.cardToAdd) {
      testContext.cardToAdd = Utils.getRandomObject(creditCards);
    }

    let spreadlyResponse = await paymentHelper.getSpreadlyToken(
      testContext.cardToAdd,
    );
    expect(spreadlyResponse.status).toBe(201);
    const tokenSpreadly =
      spreadlyResponse.body.transaction.payment_method.token;
    expect(tokenSpreadly.length).toBeGreaterThan(0);

    let response = await paymentHelper.addPaymentMethods(
      user,
      testContext.accessToken,
      tokenSpreadly,
    );

    expect(response.status).toBe(200);
    expect(response.body.data.addPaymentMethodWallet.length).toBeGreaterThan(0);

    paymentMethodsResponse = await paymentHelper.getPaymentMethodsWallet(
      user.externalId,
      testContext.accessToken,
    );

    expect(paymentMethodsResponse.status).toBe(200);
    paymentMethods = paymentMethodsResponse.body.data.getPaymentMethodsWallet;
    expect(paymentMethods.length).toBeGreaterThan(0);
    expect(paymentMethods[0].deleteDate).toBe(null);
  }
};

type RegisteredPreAuthProps = {
  userId: string;
  appCountry: string;
  paymentId: string;
  paymentMethodId: string;
  threeDS: Object;
  token: string;
};

export const registeredPreAuth = async (props: RegisteredPreAuthProps) => {
  const body = {
    operationName: 'registeredPreAuth',
    variables: {
      userId: props.userId,
      appCountry: props.appCountry,
      paymentId: props.paymentId,
      paymentMethodId: props.paymentMethodId,
      threeDS: props.threeDS,
    },
    query: registeredPreAuthQuery,
  };
  const header = {
    'x-api-key': process.env.API_GATEWAY_KEY,
    Authorization: props.token,
    'user-agent': Const.USER_AGENT,
  };

  const response = await Utils.sendHttpRequest(
    'POST',
    process.env.GATEWAY_URL,
    process.env.GATEWAY_USER_PATH,
    {},
    body,
    header,
  ).catch((error) => {
    console.error('Request failed:', error);
    return undefined;
  });

  return response;
};
