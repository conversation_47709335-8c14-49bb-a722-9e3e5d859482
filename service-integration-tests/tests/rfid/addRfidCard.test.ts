import { userFilePath } from '../../jest.globalSetup';
const users = require(userFilePath);
import * as common from '../../shared/common';
import defaultConsent from '../../test-data/user/defaultConsent.json';
import * as rfidHelper from '../../shared/rfidHelper';
import * as userHelpers from '../../shared/userHelpers';
import { getUserInfo_userIdCountryQuery } from '../user/queries/getUserInfo_userIdCountry';
import Utils from '../../shared/utils';
import requestRFID_Schema from '../../test-data/schema/requestRFID.json';

describe('register the user and order a new RFID card', () => {
  jest.setTimeout(100_000);

  let user = users.find((user) => user.testScope === 'addRfid');
  user.consents = defaultConsent;

  let testContext = {
    accessToken: '',
  };

  beforeAll(async () => {
    //delete and re-register the user to be able to order a new RFID card
    await common.reRegisterUser(user, testContext);
    await common.addPaymentMethodIfNotExists(user, testContext);
  });

  it('order RFID card', async () => {
    let response = await rfidHelper.requestRFID(user, testContext.accessToken);
    expect(response.status).toBe(200);
    expect(
      Utils.validateSchema(requestRFID_Schema, response.body),
    ).toBeTruthyWithMessage(
      'Schema validation failed for the response: \n' +
        JSON.stringify(response.body, null, 2),
    );
    expect(response.body.data.requestRFID.data.salesforceID).toBe(
      user.externalId,
    );
  });

  it('verify RFID card is ordered', async () => {
    const variables = {
      userId: user.externalId,
      appCountry: user.countryCode,
    };
    let response = await userHelpers.getUserInfo(
      testContext.accessToken,
      variables,
      getUserInfo_userIdCountryQuery,
    );
    expect(response.status).toBe(200);
    expect(response.body.data.userInfo.tagIds).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          tagStatus: 'CUSTOMER_REQUESTED',
        }),
        expect.objectContaining({
          tagNotes: 'physical-RFID',
        }),
      ]),
    );
  });
});
