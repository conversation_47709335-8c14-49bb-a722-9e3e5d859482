import puppeteer, { <PERSON>, <PERSON><PERSON><PERSON> } from 'puppeteer';
import Utils from '../../shared/utils';
import { userFilePath } from '../../jest.globalSetup';
const users = require(userFilePath);
import * as userHelpers from '../../shared/userHelpers';
import * as Const from '../../shared/constants';
import * as common from '../../shared/common';
import defaultConsent from '../../test-data/user/defaultConsent.json';
import onboardAccountSchema from '../../test-data/schema/onboardAccount.json';
import getOnboardingStatusSchema from '../../test-data/schema/getOnboardingStatus.json';

const testUsers = [
  users.find((user) => user.testScope === 'registerUser_UK'),
  users.find((user) => user.testScope === 'registerUser_DE'),
];

testUsers.forEach((user) => {
  describe(
    'register ' +
      user.countryCode +
      ' user using mobile number and email address',
    () => {
      jest.setTimeout(120_000);

      user.consents = defaultConsent;

      let testContext = {
        registrationUrl: '',
        codeChallenge: '',
        state: '',
        authorizeUrl: '',
        smsCode: '',
        locationCode: '',
        accessToken: '',
        codeVerifier: '',
        gmailAccessToken: '',
        emailCode: '',
      };

      let browser: Browser;
      let page: Page;

      beforeAll(async () => {
        //the tests is using always the same mobile number so need to delete the user associated with this number
        const forgerockToken = await userHelpers.getForgerockToken();
        expect(forgerockToken.status).toBe(200);
        const forgerockAccessToken = forgerockToken.body.access_token;
        expect(forgerockAccessToken.length).toBeGreaterThan(100);
        const userIdResponse = await userHelpers.getForgerockUserId(
          forgerockAccessToken,
          'mail',
          user.email,
        );
        expect(userIdResponse.status).toBe(200);

        if (userIdResponse.body.resultCount > 0) {
          expect(userIdResponse.body.resultCount).toBe(1);
          expect(userIdResponse.body.result[0].mail).toBe(user.email);

          const forgerockUserId = userIdResponse.body.result[0]._id;
          expect(forgerockUserId.length).toBeGreaterThan(1);

          let deleteResponse = await userHelpers.deleteForgerockUser(
            forgerockAccessToken,
            forgerockUserId,
          );
          expect(deleteResponse.status).toBe(200);
          expect(deleteResponse.body._id).toBe(forgerockUserId);
        }
      });

      afterAll(async () => {
        if (browser) {
          await browser.close();
        }
      });

      it('generate registration url', async () => {
        testContext.codeVerifier = Utils.generateRandomString(128);
        testContext.codeChallenge = Utils.generateCodeChallenge(
          testContext.codeVerifier,
        );
        testContext.state = Utils.generateRandomString(16);
        testContext.authorizeUrl = Utils.buildUrl(
          Const.CIP_BASE_URL,
          Const.CIP_AUTHORIZE_PATH,
          {
            client_id: 'PulseontheGo',
            code_challenge: testContext.codeChallenge,
            code_challenge_method: 'S256',
            collectEmail: 'enforce',
            disableSocialAuthentication: 'true',
            locale: 'en-US',
            redirect_uri: Const.CIP_REDIRECT_URL + Const.CIP_REDIRECT_PATH,
            response_type: 'code',
            scope:
              'openid email phone profile b2c-consent b2c-contact b2c-profile',
            startJourney: 'Registration',
            state: testContext.state,
          },
        );

        testContext.registrationUrl = Utils.buildUrl(
          Const.CIP_LOGIN_URL,
          Const.CIP_REGISTRATION_PATH,
          {
            goto: testContext.authorizeUrl,
            realm: '/bravo',
            locale: 'en-US',
          },
        );
      });

      it('open the login page and enter the mobile number', async () => {
        browser = await puppeteer.launch({ headless: true });
        page = await browser.newPage();
        await common.enterRegistrationPhoneNumber(page, testContext, user);
      });

      it('reading SMS code from Twillio sent to the user phone number', async () => {
        testContext.smsCode = await common.getSmsCode(
          user.phonePrefix + user.phoneNumber,
        );
        expect(testContext.smsCode.length).toBeGreaterThan(5);
      });

      it('entering the SMS code', async () => {
        await common.verifyCorrectPhone(
          page,
          user.phonePrefix + user.phoneNumber,
        );
        await common.enterSmsCode(page, testContext.smsCode);
      });

      it('entering the email address', async () => {
        await common.enterRegistrationEmail(page, user.email);
      });

      it('getting the verification email code', async () => {
        testContext.emailCode = await common.getEmailVerificationCode(
          user.email,
        );
        expect(testContext.emailCode.length).toBe(6);
      });

      it('entering the login code', async () => {
        await common.enterEmailCode(page, testContext.emailCode);
      });

      it('getting the location code', async () => {
        testContext.locationCode = await common.getLocationCode(page);
      });

      it('get the user access token', async () => {
        const headers = {
          'Content-Type': 'application/x-www-form-urlencoded',
        };
        const params = {
          client_id: 'PulseontheGo',
        };
        const requestBody =
          'code=' +
          testContext.locationCode +
          '&code_verifier=' +
          testContext.codeVerifier +
          '&grant_type=authorization_code&redirect_uri=https%3A%2F%2Fredirect-stg-energyid.bpglobal.com%2Fredirect%2FPulseontheGo';

        let response;
        async function sendRequest() {
          try {
            response = await Utils.sendHttpRequest(
              'POST',
              Const.CIP_BASE_URL,
              Const.CIP_ACCESS_TOKEN_PATH,
              params,
              requestBody,
              headers,
            );
          } catch (error) {
            console.error('Request failed:', error);
          }
        }
        await sendRequest();
        expect(response.status).toBe(200);
        expect(response.body.access_token.length).toBeGreaterThan(100);
        expect(response.body.refresh_token.length).toBeGreaterThan(100);
        expect(response.body.id_token.length).toBeGreaterThan(100);
        testContext.accessToken = response.body.access_token;
      });

      it('verify contact information before the country, first and last name are set', async () => {
        let response;
        const startTime = Date.now();
        const timeoutMs = 5_000;
        while (Date.now() - startTime <= timeoutMs) {
          // waiting until the contact information are populated
          response = await userHelpers.getContactInformation(
            testContext.accessToken,
          );
          expect(response.status).toBe(200);
          if (
            response.body &&
            response.body.sfContactId &&
            response.body.email &&
            response.body.identityExternalId
          ) {
            break;
          }
          await Utils.delay(1_000);
        }

        expect(response.status).toBe(200);
        user.externalId = response.body.identityExternalId;
        expect(response.body.customerHasUserIdentity).toBe(true);
        expect(response.body.firstName).toBe(null);
        expect(response.body.lastName).toBe(null);
        expect(response.body.nickName).toBe(null);
        expect(response.body.email.toLowerCase()).toBe(
          user.email.toLowerCase(),
        );
        expect(response.body.mobileNumber).toBe(
          user.phonePrefix + user.phoneNumber,
        );
        expect(response.body.isActive).toBe(true);
        expect(response.body.identityExternalId.length).toBeGreaterThan(0);
      });

      it('verify empty consent identity before account is onboarded', async () => {
        let response = await userHelpers.getUserConsent(
          testContext.accessToken,
        );
        expect(response.status).toBe(200);
        expect(response.body.records.length).toBe(0);
      });

      it('verify user information', async () => {
        let response = await userHelpers.getUserInformation(
          testContext.accessToken,
        );
        expect(response.status).toBe(200);
        expect(response.body.externalId).toBe(user.externalId);
        expect(response.body.phoneNumber).toBe(
          user.phonePrefix + user.phoneNumber,
        );
        expect(response.body.phoneNumberVerified).toBe(true);
        expect(response.body.emailVerified).toBe(true);
        expect(response.body.email.toLowerCase()).toBe(
          user.email.toLowerCase(),
        );
      });

      it('verify user is not yet onboarded', async () => {
        let response = await userHelpers.getOnboardingStatus(
          testContext.accessToken,
          user.externalId,
        );
        expect(response.status).toBe(200);
        expect(response.body.data.onboardingStatus.account).toBe(false);
        expect(response.body.data.onboardingStatus.charging).toBe(false);
        expect(response.body.data.onboardingStatus.roaming).toBe(false);
        expect(response.body.data.onboardingStatus.country).toBe(null);
        expect(response.body.data.onboardingStatus.providers.length).toBe(0);
      });

      it('set first name, last name and country', async () => {
        let response = await userHelpers.setContactInfo(
          testContext.accessToken,
          user,
        );
        expect(response.status).toBe(200);
        expect(response.body.message).toBe(
          'Request received and queued for processing.',
        );
      });

      user.consents.forEach((consent) => {
        it(
          'set consent ' + consent.consentType + ' to ' + consent.accepted,
          async () => {
            if (user.countryCode !== 'UK') {
              //adding country posfix for some consents
              if (consent.consentType === 'EV Privacy Policy') {
                consent.consentType =
                  consent.consentType + ' ' + user.countryCode;
              }
              if (consent.consentType === 'EV Terms and Conditions') {
                consent.consentType =
                  consent.consentType + ' ' + user.countryCode;
              }
            }

            let consentData = [
              {
                consentType: consent.consentType,
                accepted: consent.accepted,
                channel: consent.channel,
              },
            ];
            common.setConsent(testContext.accessToken, consentData);
          },
        );
      });

      it('verify contact information  after the country, first and last name are set', async () => {
        let response;
        const startTime = Date.now();
        const timeoutMs = 20_000;
        // waiting until the contact information are populated
        while (Date.now() - startTime <= timeoutMs) {
          response = await userHelpers.getContactInformation(
            testContext.accessToken,
          );
          expect(response.status).toBe(200);
          if (
            response.body &&
            response.body.firstName != null &&
            response.body.lastName != null &&
            response.body.country != null
          ) {
            break;
          }
          await Utils.delay(1_000);
        }
        expect(response.body.firstName).toBe(user.firstName);
        expect(response.body.lastName).toBe(user.lastName);
        expect(response.body.country).toBe(user.countryCode);
        expect(response.body.identityExternalId).toBe(user.externalId);
        expect(response.body.mobileNumber).toBe(
          user.phonePrefix + user.phoneNumber,
        );
        expect(response.body.email.toLowerCase()).toBe(
          user.email.toLowerCase(),
        );
      });

      it('verify all consents are set', async () => {
        let response;
        const startTime = Date.now();
        const timeoutMs = 10_000;
        // waiting until the contact information are populated
        while (Date.now() - startTime <= timeoutMs) {
          response = await userHelpers.getUserConsent(testContext.accessToken);
          expect(response.status).toBe(200);
          if (response.body.records?.length === user.consents.length) {
            break;
          }
          await Utils.delay(1_000);
        }

        user.consents.forEach((consent) => {
          const currentConsent = response.body.records.find(
            (record) => record.consentType === consent.consentType,
          );
          expect(currentConsent.accepted).toBe(consent.accepted);
          if (consent.channel) {
            expect(consent.channel).toContain(currentConsent.channel);
          }
          expect(currentConsent.identityExternalId).toBe(user.externalId);
        });
      });

      it('onboard account', async () => {
        let response = await userHelpers.onboardAccount(
          testContext.accessToken,
          user,
        );
        expect(response.status).toBe(200);
        expect(response.body.data.onboardAccount.success).toBe(true);
        expect(response.body.data.onboardAccount.message).toBe(
          'User account onboarding request completed',
        );

        expect(
          Utils.validateSchema(onboardAccountSchema, response.body),
        ).toBeTruthyWithMessage(
          'Schema validation failed for the response: \n' +
            JSON.stringify(response.body, null, 2),
        );
      });

      it('verify user is successfully onboarded', async () => {
        await common.verifyUserIsOnboarded(user, testContext);
      });
    },
  );
});
