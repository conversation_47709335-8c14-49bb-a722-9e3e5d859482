import puppeteer, { <PERSON>, <PERSON><PERSON><PERSON> } from 'puppeteer';
import Utils from '../../shared/utils';
import { userFilePath } from '../../jest.globalSetup';
const users = require(userFilePath);
import * as userHelpers from '../../shared/userHelpers';
import * as Const from '../../shared/constants';
import * as common from '../../shared/common';
import defaultConsent from '../../test-data/user/defaultConsent.json';

describe('login to bp pulse app user email address', () => {
  jest.setTimeout(60_000);

  let user = users.find((user) => user.testScope === 'loginEmail');
  user.consents = defaultConsent;

  let testContext = {
    loginUrl: '',
    codeChallenge: '',
    state: '',
    authorizeUrl: '',
    locationCode: '',
    accessToken: '',
    codeVerifier: '',
    gmailAccessToken: '',
    loginCode: '',
  };

  let browser: Browser;
  let page: Page;

  afterAll(async () => {
    if (browser) {
      await browser.close();
    }
  });

  it('generate login url', async () => {
    testContext = await common.generateLoginUrl(testContext);
    expect(testContext.loginUrl).toContain(Const.CIP_LOGIN_URL);
  });

  it('open the login page and enter the email address', async () => {
    browser = await puppeteer.launch({ headless: true });
    page = await browser.newPage();
    await common.enterEmailOrPhone(page, testContext, user.email);
  });

  it('getting the verification code from the email', async () => {
    await common.verifyCorrectEmail(page, user.email);
    testContext.loginCode = await common.getEmailLoginCode(user.email);
    expect(testContext.loginCode.length).toBe(6);
  });

  it('entering the login code', async () => {
    await common.enterEmailCode(page, testContext.loginCode);
  });

  it('skips passkeys prompt', async () => {
    await common.skipPasskeysPrompt(page);
  });

  it('getting the location code', async () => {
    testContext.locationCode = await common.getLocationCode(page);
  });

  it('get the user access token', async () => {
    let tokenResponse = await userHelpers.getUserAccessToken(testContext);
    expect(tokenResponse.status).toBe(200);
    expect(tokenResponse.body.access_token.length).toBeGreaterThan(100);
    expect(tokenResponse.body.refresh_token.length).toBeGreaterThan(100);
    expect(tokenResponse.body.id_token.length).toBeGreaterThan(100);
    testContext.accessToken = tokenResponse.body.access_token;
  });

  it('verify user information', async () => {
    let response = await userHelpers.getUserInformation(
      testContext.accessToken,
    );
    expect(response.status).toBe(200);
    expect(response.body.externalId.length).toBeGreaterThan(10);
    user.externalId = response.body.externalId;
    if (user.phonePrefix && user.phoneNumber) {
      //user registered with phone
      expect(response.body.phoneNumber).toBe(
        user.phonePrefix + user.phoneNumber,
      );
      expect(response.body.phoneNumberVerified).toBe(true);
    }
    expect(response.body.emailVerified).toBe(true);
    expect(response.body.email.toLowerCase()).toBe(user.email.toLowerCase());
  });

  it('verify contact information', async () => {
    let response;
    const startTime = Date.now();
    const timeoutMs = 20_000;
    // waiting until the contact information are populated
    while (Date.now() - startTime <= timeoutMs) {
      response = await userHelpers.getContactInformation(
        testContext.accessToken,
      );
      expect(response.status).toBe(200);
      if (
        response.body &&
        response.body.firstName != null &&
        response.body.lastName != null &&
        response.body.country != null
      ) {
        break;
      }
      await Utils.delay(1_000);
    }
    expect(response.body.firstName).toBe(user.firstName);
    expect(response.body.lastName).toBe(user.lastName);
    expect(response.body.country).toBe(user.countryCode);
    expect(response.body.identityExternalId).toBe(user.externalId);
    if (user.phonePrefix && user.phoneNumber) {
      //user registered with phone
      expect(response.body.mobileNumber).toBe(
        user.phonePrefix + user.phoneNumber,
      );
    }
    expect(response.body.email.toLowerCase()).toBe(user.email.toLowerCase());
  });

  it('verify all consents are set', async () => {
    let response = await userHelpers.getUserConsent(testContext.accessToken);
    expect(response.status).toBe(200);
    expect(response.body.records.length).toBe(user.consents.length);
    user.consents.forEach(({ consentType, accepted }) => {
      const currentConsent = response.body.records.find(
        (record) => record.consentType === consentType,
      );
      expect(currentConsent.accepted === accepted).toBeTruthyWithMessage(
        'The actual consent ' +
          currentConsent.consentType +
          ' does not match the expected accepted value ' +
          accepted,
      );

      expect(
        currentConsent.identityExternalId === user.externalId,
      ).toBeTruthyWithMessage(
        'The actual identityExternalId \n' +
          JSON.stringify(currentConsent, null, 2) +
          '\n does not match the expected user.externalId ' +
          user.externalId,
      );
    });
  });

  it('verify user is successfully onboarded', async () => {
    let response = await userHelpers.getOnboardingStatus(
      testContext.accessToken,
      user.externalId,
    );
    expect(response.status).toBe(200);
    expect(response.body.data.onboardingStatus.account).toBe(true);
    expect(response.body.data.onboardingStatus.charging).toBe(true);
    expect(response.body.data.onboardingStatus.roaming).toBe(false);
    expect(response.body.data.onboardingStatus.country).toBe(user.countryCode);
    expect(
      response.body.data.onboardingStatus.providers.length,
    ).toBeGreaterThan(0);
  });
});
