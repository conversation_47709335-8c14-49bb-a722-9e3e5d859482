import { userFilePath } from '../../jest.globalSetup';
const users = require(userFilePath);
import * as common from '../../shared/common';
import defaultConsent from '../../test-data/user/defaultConsent.json';

describe('Create user without mobile number, set user contact information and consent', () => {
  jest.setTimeout(80_000);

  let user = users.find((user) => user.testScope === 'yourUser');
  user.consents = defaultConsent;

  let testContext = {
    accessToken: '',
  };

  it('create CIP user', async () => {
    await common.reRegisterUser(user, testContext);
  });
});
