import Utils from '../../shared/utils';
import { userFilePath } from '../../jest.globalSetup';
const users = require(userFilePath);
import * as Const from '../../shared/constants';
import * as common from '../../shared/common';
import localizationMap from '../../test-data/invoice/localizationMap.json';
import * as historryHelper from '../../shared/historyHelper';

const TIMEOUT_MS = 400_000; //to make sure we have enough time to wait for the charge test to login and finish the charging session
jest.setTimeout(TIMEOUT_MS);
const testUsers = [
  users.find((user) => user.testScope === 'charge_UK'),
  users.find((user) => user.testScope === 'charge_UK_preauth'),
  users.find((user) => user.testScope === 'charge_DE'),
  users.find((user) => user.testScope === 'charge_DE_preauth'),
  users.find((user) => user.testScope === 'charge_ES'),
];

testUsers.forEach((user) => {
  describe(`${user.paymentAuthType} | ${user.countryCode} user can retrieve receipt URL`, () => {
    let testContext = {
      token: '',
      chargeSession: null,
    };

    const savedInvoceFilePath =
      Const.TEMP_FOLDER + user.testScope + '_invoice.pdf';

    beforeAll(async () => {
      testContext.chargeSession = await common.waitForChargeSessionData(
        Const.TEMP_FOLDER + user.testScope + '_chargeSession.json',
        TIMEOUT_MS,
      );
      testContext.token = await common.loginUser(user);
      user.externalId = Utils.getJwtPayload(testContext.token).external_id;
    });

    it('should fetch receipt URL and parse PDF content', async () => {
      let response;
      const startTime = Date.now();
      const timeoutMs = 20_000; //waiting for the receipt URL
      while (Date.now() - startTime <= timeoutMs) {
        response = await historryHelper.getReceiptURL(
          testContext.token,
          testContext.chargeSession.chargeSessionId,
        );
        expect(response.status).toBe(200);
        if (response.body.data.getReceiptURL.url) {
          break;
        }
        await Utils.delay(2_000);
      }

      const receiptURL = response.body.data.getReceiptURL.url;
      expect(receiptURL).not.toBeNull();
      expect(receiptURL).toContain(testContext.chargeSession.chargeSessionId);
      await Utils.downloadAndSavePDF(
        response.body.data.getReceiptURL.url,
        savedInvoceFilePath,
      );
      const pdfText = await Utils.parsePDF(savedInvoceFilePath);

      const chargePointCountry = testContext.chargeSession.chargepoint.country;
      const localizedText = localizationMap[chargePointCountry];
      expect(pdfText).toContain(localizedText.invoiceTitle);

      const latestTransaction = await historryHelper.getHistoryRecord(
        testContext.token,
        user,
        testContext.chargeSession.chargeSessionId,
      );
      const expectedInvoiceNumber =
        latestTransaction.body.data.getHistoryRecord.receiptId;

      //Getting it from e.g. Factuurnummer: ELDEIN25000000000000161226-03-2025
      const invoiceNumberAndDate = Utils.getStringBetween(
        pdfText,
        localizedText.invoiceNumber + ': ',
        '\n',
      );
      expect(invoiceNumberAndDate).toContain(expectedInvoiceNumber);

      let expectedInvoiceDate;
      let invoiceDisplayAddress;
      let invoiceEnergyConsumed = Utils.parseNumber(
        Utils.getStringBetween(
          pdfText,
          localizedText.energyConsumed + ':',
          ' kWh',
        ),
      );
      let totalToPay;
      let currencySymbol;

      if (user.countryCode === 'UK') {
        expectedInvoiceDate = Utils.formatDate(
          testContext.chargeSession.timestamp.stop,
          'd MMMM yyyy',
        ); // Output e.g. "5 June 2025"
        invoiceDisplayAddress = Utils.getStringBetween(
          pdfText,
          localizedText.displayAddress + ':',
          localizedText.startDate,
        );

        totalToPay = Utils.getStringBetween(
          pdfText,
          localizedText.chargeBaseFeeGross + ':',
          '*',
        );
        currencySymbol = totalToPay.charAt(0);
      } else {
        expectedInvoiceDate = Utils.formatDate(
          testContext.chargeSession.timestamp.stop,
          'dd-MM-yyyy',
        ); // Outpute.g. "05-06-2025"
        invoiceDisplayAddress = Utils.getStringBetween(
          pdfText,
          localizedText.displayAddress + ':',
          '\n',
        );

        const invoiceStartTimeString = Utils.getStringBetween(
          pdfText,
          localizedText.startTime + ':',
          '\n',
        );
        const invoiceStartTime = Utils.convertTimeToUTC(
          invoiceStartTimeString,
          user.timeZone,
        );
        const chargeSessionStartTime = new Date(
          Utils.truncateToMinute(testContext.chargeSession.timestamp.start),
        );
        const diffStart = Math.abs(
          invoiceStartTime.getTime() - chargeSessionStartTime.getTime(),
        );
        expect(diffStart).toBe(0);

        const invoiceEndTimeString = Utils.getStringBetween(
          pdfText,
          localizedText.stopTime + ':',
          '\n',
        );
        const invoiceEndTime = Utils.convertTimeToUTC(
          invoiceEndTimeString,
          user.timeZone,
        );
        const chargeSessionEndTime = new Date(
          Utils.truncateToMinute(testContext.chargeSession.timestamp.stop),
        );
        const diffEnd = Math.abs(
          invoiceEndTime.getTime() - chargeSessionEndTime.getTime(),
        );
        expect(diffEnd).toBe(0);

        // parsing prices from the invoice separated by new line e.g.
        // € 3,45
        // € 0,65
        // € 4,10
        let invoicePrices = Utils.getStringBetween(
          pdfText,
          localizedText.chargeBaseFeeGross + '\n',
          '\n' + localizedText.paidWith,
        );
        let prices = invoicePrices.split('\n');
        totalToPay = prices[2];
        currencySymbol = totalToPay.charAt(totalToPay.length - 1);
      }

      expect(invoiceNumberAndDate).toContain(expectedInvoiceDate);
      expect(invoiceDisplayAddress).toContain(
        testContext.chargeSession.chargepoint.displayAddress,
      );
      expect(invoiceEnergyConsumed).toBe(
        testContext.chargeSession.chargeDetails.energyConsumed.value,
      );

      expect(currencySymbol).toBe(
        Utils.getCurrencySymbol(
          testContext.chargeSession.chargeDetails.currency,
        ),
      );

      totalToPay = Utils.parseNumber(totalToPay.replace(currencySymbol, ''));

      expect(totalToPay).toBe(
        Utils.parseNumber(testContext.chargeSession.chargeDetails.totalGross),
      );
    });
  });
});
