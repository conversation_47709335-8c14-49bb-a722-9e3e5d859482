import Utils from '../../shared/utils';
import sitesDetailedSchema from '../../test-data/schema/sitesDetailed.json';
import * as mapHelper from '../../shared/mapHelper';

describe('Prices Server', () => {
  it('should get the evPrices for the ev provided site', async () => {
    const queryVariables = {
      powerTypes: ['EV'],
      countries: ['UK', 'US'],
      size: 30,
    };
    const sitesResponse = await mapHelper.sitesDetailed(queryVariables);

    expect(sitesResponse.status).toBe(200);
    expect(Utils.validateSchema(sitesDetailedSchema, sitesResponse.body)).toBe(
      true,
    );
    const site = Utils.getRandomObject(sitesResponse.body.data.sites);
    expect(site.hasEvCharging).toBe(true);
    expect(site.chargepoints[0].evPrice.length).toBeGreaterThan(0);
  });

  it('should get the fuelPrices for the fuel provided site', async () => {
    const queryVariables = {
      powerTypes: ['fuel'],
      countries: ['DE'],
      providers: ['aral'],
      size: 30,
    };
    const sitesResponse = await mapHelper.sitesDetailed(queryVariables);

    expect(sitesResponse.status).toBe(200);
    expect(Utils.validateSchema(sitesDetailedSchema, sitesResponse.body)).toBe(
      true,
    );
    const site = Utils.getRandomObject(sitesResponse.body.data.sites);
    expect(site.hasFuel).toBe(true);
    expect(site.fuelPrices.length).toBeGreaterThan(0);
  });
});
