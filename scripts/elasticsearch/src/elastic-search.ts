import { Client, errors, estypes } from '@elastic/elasticsearch';
import { ESIndex, Site } from './types/elastic-search.types';

export const client = (function () {
  let instance: Client;

  return () => {
    if (!instance) {
      instance = new Client({ node: 'http://127.0.0.1:9200' });
    }

    return instance;
  };
})();

export type CreateIndexMappings = {
  mappings: unknown;
};

export async function createIndex(
  index: ESIndex,
  mappings: CreateIndexMappings,
) {
  try {
    const params = { index, body: mappings };
    const response = await client().indices.create<
      estypes.CreateIndexResponse,
      CreateIndexMappings
    >(params);

    return response.body;
  } catch (e) {
    const err = e as errors.ResponseError;
    console.error(
      `[${err.statusCode}] ElasticSearch - Failed to create index:`,
      err.body,
    );

    throw e;
  }
}

export async function deleteIndex(index: ESIndex) {
  try {
    const params = { index };
    const response = await client().indices.delete<estypes.DeleteIndexResponse>(
      params,
    );

    return response.body;
  } catch (e) {
    const err = e as errors.ResponseError;

    if (err.body?.error?.type !== 'index_not_found_exception') {
      console.error(
        `[${err.statusCode}] ElasticSearch - Failed to delete index:`,
        err.body ?? err,
      );
      throw e;
    }
  }
}

export async function bulk(sites: Array<Site>) {
  try {
    const body = sites
      .map((site) => {
        const _index = ESIndex.SITES;
        const _id = site.siteId;

        return [{ index: { _index, _id } }, site];
      })
      .flat();

    return await client().bulk<estypes.BulkResponse>({ body });
  } catch (e) {
    const err = e as errors.ResponseError;
    console.error(
      `[${err.statusCode}] ElasticSearch - Failed to bulk:`,
      err.body,
    );

    throw e;
  }
}
