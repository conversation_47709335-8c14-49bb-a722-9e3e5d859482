const tariffsData = [
  {
    PutRequest: {
      Item: {
        tariffId: 'cdb5a5b1-c3d9-43bb-ac56-9d9dd6cde49f',
        countryType: 'UK-PAYG_WALLET',
        versionTimestamp: '2024-10-23T10:07:19.281Z',
        country: 'UK',
        tariffType: 'PAYG_WALLET',
        currency: 'GBP',
        validFrom: '2024-10-01T11:30:31.840Z',
        lastUpdated: '2024-10-23T10:07:19.281Z',
        elements: [
          {
            priceComponents: [
              {
                type: 'PARKING_TIME',
                price: 10,
                vat: null,
                stepSize: 60,
              },
            ],
            restrictions: {
              minPower: null,
              maxPower: null,
              minDuration: 90,
              maxDuration: null,
            },
          },
          {
            priceComponents: [
              {
                type: 'ENERGY',
                price: 0.59,
                vat: 20,
                stepSize: 1,
              },
            ],
            restrictions: {
              minPower: null,
              maxPower: 39,
              minDuration: null,
              maxDuration: null,
            },
          },
          {
            priceComponents: [
              {
                type: 'ENERGY',
                price: 0.77,
                vat: 20,
                stepSize: 1,
              },
            ],
            restrictions: {
              minPower: 40,
              maxPower: 99,
              minDuration: null,
              maxDuration: null,
            },
          },
          {
            priceComponents: [
              {
                type: 'ENERGY',
                price: 0.83,
                vat: 20,
                stepSize: 1,
              },
            ],
            restrictions: {
              minPower: 100,
              maxPower: null,
              minDuration: null,
              maxDuration: null,
            },
          },
        ],
      },
    },
  },
  {
    PutRequest: {
      Item: {
        tariffId: '61308a57-cad7-4fe8-9735-c9f437ba83f4',
        countryType: 'UK-SUBS_WALLET',
        versionTimestamp: '2024-10-23T10:07:24.830Z',
        country: 'UK',
        tariffType: 'SUBS_WALLET',
        currency: 'GBP',
        validFrom: '2024-10-01T11:30:31.840Z',
        lastUpdated: '2024-10-23T10:07:24.830Z',
        elements: [
          {
            priceComponents: [
              {
                type: 'PARKING_TIME',
                price: 10,
                vat: null,
                stepSize: 60,
              },
            ],
            restrictions: {
              minPower: null,
              maxPower: null,
              minDuration: 90,
              maxDuration: null,
            },
          },
          {
            priceComponents: [
              {
                type: 'ENERGY',
                price: 0.44,
                vat: 20,
                stepSize: 1,
              },
            ],
            restrictions: {
              minPower: null,
              maxPower: 39,
              minDuration: null,
              maxDuration: null,
            },
          },
          {
            priceComponents: [
              {
                type: 'ENERGY',
                price: 0.63,
                vat: 20,
                stepSize: 1,
              },
            ],
            restrictions: {
              minPower: 40,
              maxPower: 99,
              minDuration: null,
              maxDuration: null,
            },
          },
          {
            priceComponents: [
              {
                type: 'ENERGY',
                price: 0.69,
                vat: 20,
                stepSize: 1,
              },
            ],
            restrictions: {
              minPower: 100,
              maxPower: null,
              minDuration: null,
              maxDuration: null,
            },
          },
        ],
      },
    },
  },
  {
    PutRequest: {
      Item: {
        tariffId: '95c3f2ed-0bdb-4174-b820-af960203457a',
        countryType: 'UK-UBER',
        versionTimestamp: '2024-10-23T10:07:29.933Z',
        country: 'UK',
        tariffType: 'UBER',
        currency: 'GBP',
        validFrom: '2024-10-01T11:30:31.840Z',
        lastUpdated: '2024-10-23T10:07:29.933Z',
        elements: [
          {
            priceComponents: [
              {
                type: 'PARKING_TIME',
                price: 10,
                vat: null,
                stepSize: 60,
              },
            ],
            restrictions: {
              minPower: null,
              maxPower: null,
              minDuration: 90,
              maxDuration: null,
            },
          },
          {
            priceComponents: [
              {
                type: 'ENERGY',
                price: 0.44,
                vat: 20,
                stepSize: 1,
              },
            ],
            restrictions: {
              minPower: null,
              maxPower: 39,
              minDuration: null,
              maxDuration: null,
            },
          },
          {
            priceComponents: [
              {
                type: 'ENERGY',
                price: 0.63,
                vat: 20,
                stepSize: 1,
              },
            ],
            restrictions: {
              minPower: 40,
              maxPower: 99,
              minDuration: null,
              maxDuration: null,
            },
          },
          {
            priceComponents: [
              {
                type: 'ENERGY',
                price: 0.69,
                vat: 20,
                stepSize: 1,
              },
            ],
            restrictions: {
              minPower: 100,
              maxPower: null,
              minDuration: null,
              maxDuration: null,
            },
          },
        ],
      },
    },
  },
  {
    PutRequest: {
      Item: {
        tariffId: 'f14f7e69-ff6c-4b56-b834-60685cdffa94',
        countryType: 'UK-GUEST',
        versionTimestamp: '2024-10-23T10:07:09.386Z',
        country: 'UK',
        tariffType: 'GUEST',
        currency: 'GBP',
        validFrom: '2024-10-01T11:30:31.840Z',
        lastUpdated: '2024-10-23T10:07:09.386Z',
        elements: [
          {
            priceComponents: [
              {
                type: 'PARKING_TIME',
                price: 10,
                vat: null,
                stepSize: 60,
              },
            ],
            restrictions: {
              minPower: null,
              maxPower: null,
              minDuration: 90,
              maxDuration: null,
            },
          },
          {
            priceComponents: [
              {
                type: 'ENERGY',
                price: 0.59,
                vat: 20,
                stepSize: 1,
              },
            ],
            restrictions: {
              minPower: null,
              maxPower: 39,
              minDuration: null,
              maxDuration: null,
            },
          },
          {
            priceComponents: [
              {
                type: 'ENERGY',
                price: 0.79,
                vat: 20,
                stepSize: 1,
              },
            ],
            restrictions: {
              minPower: 40,
              maxPower: 99,
              minDuration: null,
              maxDuration: null,
            },
          },
          {
            priceComponents: [
              {
                type: 'ENERGY',
                price: 0.85,
                vat: 20,
                stepSize: 1,
              },
            ],
            restrictions: {
              minPower: 100,
              maxPower: null,
              minDuration: null,
              maxDuration: null,
            },
          },
        ],
      },
    },
  },
];

module.exports = {
  tariffsData,
};
