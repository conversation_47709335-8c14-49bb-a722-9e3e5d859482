// do add a new table or change the configuration of a table update this object
const capacity = 10;
module.exports = {
  voucherCodes: {
    TableName: 'voucherCodesV2',
    KeySchema: [
      { AttributeName: 'voucher_code', KeyType: 'HASH' }, // Partition key
    ],
    AttributeDefinitions: [
      { AttributeName: 'voucher_code', AttributeType: 'S' },
      { AttributeName: 'voucher_used', AttributeType: 'S' },
      { AttributeName: 'voucher_type', AttributeType: 'S' },
      { AttributeName: 'charge_session_id', AttributeType: 'S' },
    ],
    GlobalSecondaryIndexes: [
      {
        IndexName: 'voucher_used_index',
        KeySchema: [
          {
            AttributeName: 'voucher_used',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'voucher_type',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
      {
        IndexName: 'charge_session_id_index',
        KeySchema: [
          {
            AttributeName: 'charge_session_id',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'voucher_type',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'KEYS_ONLY',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
    ],
    ProvisionedThroughput: {
      ReadCapacityUnits: 10,
      WriteCapacityUnits: 10,
    },
  },
  anonSession: {
    TableName: 'anonSession',
    KeySchema: [
      { AttributeName: 'anon_user_id', KeyType: 'HASH' }, // Partition key
    ],
    AttributeDefinitions: [
      { AttributeName: 'anon_user_id', AttributeType: 'S' },
    ],
    GlobalSecondaryIndexes: [
      {
        IndexName: 'anon_user_index',
        KeySchema: [
          {
            AttributeName: 'anon_user_id',
            KeyType: 'HASH',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
    ],

    ProvisionedThroughput: {
      ReadCapacityUnits: 10,
      WriteCapacityUnits: 10,
    },
  },
  favouriteUserCP: {
    TableName: 'favouriteUserCP',
    KeySchema: [
      { AttributeName: 'user_id', KeyType: 'HASH' }, // Partition key
    ],
    AttributeDefinitions: [{ AttributeName: 'user_id', AttributeType: 'S' }],
    ProvisionedThroughput: {
      ReadCapacityUnits: 10,
      WriteCapacityUnits: 10,
    },
  },
  receiptDetails: {
    TableName: 'receiptDetails',
    KeySchema: [
      { AttributeName: 'receipt_id', KeyType: 'HASH' }, // Partition key
    ],
    AttributeDefinitions: [
      { AttributeName: 'receipt_id', AttributeType: 'S' },
      { AttributeName: 'provider', AttributeType: 'S' },
      { AttributeName: 'platform', AttributeType: 'S' },
      { AttributeName: 'receipt_number', AttributeType: 'N' },
      { AttributeName: 'transaction_id', AttributeType: 'S' },
      { AttributeName: 'charge_session_id', AttributeType: 'S' },
      { AttributeName: 'acquirer', AttributeType: 'S' },
      { AttributeName: 'acquirer_prefix', AttributeType: 'S' },
    ],
    GlobalSecondaryIndexes: [
      {
        IndexName: 'platform_index',
        KeySchema: [
          {
            AttributeName: 'platform',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'receipt_number',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
      {
        IndexName: 'provider_index',
        KeySchema: [
          {
            AttributeName: 'provider',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'receipt_number',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
      {
        IndexName: 'transaction_index',
        KeySchema: [
          {
            AttributeName: 'transaction_id',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'receipt_id',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
      {
        IndexName: 'acquirer_index',
        KeySchema: [
          {
            AttributeName: 'acquirer_prefix',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'receipt_number',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
      {
        IndexName: 'charge_session_index',
        KeySchema: [
          {
            AttributeName: 'charge_session_id',
            KeyType: 'HASH',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
    ],
    ProvisionedThroughput: {
      ReadCapacityUnits: 10,
      WriteCapacityUnits: 10,
    },
  },
  // Replaced chargeHistory (which was V2)
  chargeHistoryV3: {
    TableName: 'chargeHistoryV3',
    KeySchema: [
      { AttributeName: 'charge_session_id', KeyType: 'HASH' }, // Partition key
    ],
    AttributeDefinitions: [
      { AttributeName: 'transaction_search_id', AttributeType: 'S' },
      { AttributeName: 'charge_session_id', AttributeType: 'S' },
      { AttributeName: 'transactionId', AttributeType: 'S' },
      { AttributeName: 'sessionId', AttributeType: 'S' },
      { AttributeName: 'user_id', AttributeType: 'S' },
      { AttributeName: 'date_start', AttributeType: 'S' },
      { AttributeName: 'date_end', AttributeType: 'S' },
    ],
    GlobalSecondaryIndexes: [
      {
        IndexName: 'start_transaction',
        KeySchema: [
          {
            AttributeName: 'transactionId',
            KeyType: 'HASH',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 1,
          WriteCapacityUnits: 1,
        },
      },
      {
        IndexName: 'start_session',
        KeySchema: [
          {
            AttributeName: 'sessionId',
            KeyType: 'HASH',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 1,
          WriteCapacityUnits: 1,
        },
      },
      {
        IndexName: 'start_time_index',
        KeySchema: [
          {
            AttributeName: 'user_id',
            KeyType: 'HASH',
          },

          {
            AttributeName: 'date_start',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
      {
        IndexName: 'end_time_index',
        KeySchema: [
          {
            AttributeName: 'user_id',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'date_end',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
      {
        IndexName: 'transaction_index',
        KeySchema: [
          {
            AttributeName: 'transaction_search_id',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'date_end',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: capacity,
          WriteCapacityUnits: capacity,
        },
      },
    ],
    ProvisionedThroughput: {
      ReadCapacityUnits: 10,
      WriteCapacityUnits: 10,
    },
  },
  paymentDetailsV2: {
    TableName: 'paymentDetailsV2',
    KeySchema: [
      { AttributeName: 'payment_id', KeyType: 'HASH' }, // Partition key
    ],
    AttributeDefinitions: [
      { AttributeName: 'payment_id', AttributeType: 'S' },
      { AttributeName: 'charge_session_id', AttributeType: 'S' },
      { AttributeName: 'user_id', AttributeType: 'S' },
      { AttributeName: 'transaction_id', AttributeType: 'S' },
      { AttributeName: 'auth_id', AttributeType: 'S' },
      { AttributeName: 'mem_group', AttributeType: 'S' },
      { AttributeName: 'order_started', AttributeType: 'N' },
      { AttributeName: 'reference_charge_session_id', AttributeType: 'S' },
      { AttributeName: 'refunded_date', AttributeType: 'N' },
      { AttributeName: 'user_type', AttributeType: 'S' },
    ],
    GlobalSecondaryIndexes: [
      {
        IndexName: 'charge_session_id_index',
        KeySchema: [
          {
            AttributeName: 'charge_session_id',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'order_started',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
      {
        IndexName: 'order_index',
        KeySchema: [
          {
            AttributeName: 'payment_id',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'order_started',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
      {
        IndexName: 'payment_index',
        KeySchema: [
          {
            AttributeName: 'payment_id',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'order_started',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
      {
        IndexName: 'user_index',
        KeySchema: [
          {
            AttributeName: 'user_id',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'order_started',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
      {
        IndexName: 'user_type_index',
        KeySchema: [
          {
            AttributeName: 'user_type',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'order_started',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
      {
        IndexName: 'start_time_index',
        KeySchema: [
          {
            AttributeName: 'mem_group',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'order_started',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
      {
        IndexName: 'transaction_index',
        KeySchema: [
          {
            AttributeName: 'transaction_id',
            KeyType: 'HASH',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
      {
        IndexName: 'auth_id_index',
        KeySchema: [
          {
            AttributeName: 'auth_id',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'order_started',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
      {
        IndexName: 'reference_charge_session_index',
        KeySchema: [
          {
            AttributeName: 'reference_charge_session_id',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'refunded_date',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
    ],
    ProvisionedThroughput: {
      ReadCapacityUnits: 10,
      WriteCapacityUnits: 10,
    },
  },
  userFeedback: {
    TableName: 'userFeedback',
    KeySchema: [
      { AttributeName: 'feedback_id', KeyType: 'HASH' }, // Partition key
    ],
    AttributeDefinitions: [
      { AttributeName: 'feedback_id', AttributeType: 'S' },
    ],
    ProvisionedThroughput: {
      ReadCapacityUnits: 10,
      WriteCapacityUnits: 10,
    },
  },
  qa: {
    TableName: 'WS-00F0-QA-Token-Users',
    KeySchema: [
      { AttributeName: 'user_id', KeyType: 'HASH' }, // Partition key
    ],
    AttributeDefinitions: [
      { AttributeName: 'user_id', AttributeType: 'S' },
      { AttributeName: 'country_code', AttributeType: 'S' },
      { AttributeName: 'feature', AttributeType: 'S' },
      { AttributeName: 'feature_country_code', AttributeType: 'S' },
    ],
    GlobalSecondaryIndexes: [
      {
        IndexName: 'country_code_index',
        KeySchema: [
          {
            AttributeName: 'user_id',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'country_code',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
      {
        IndexName: 'feature_index',
        KeySchema: [
          {
            AttributeName: 'user_id',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'feature',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
      {
        IndexName: 'feature_country_code_index',
        KeySchema: [
          {
            AttributeName: 'user_id',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'feature_country_code',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
    ],
    ProvisionedThroughput: {
      ReadCapacityUnits: 10,
      WriteCapacityUnits: 10,
    },
  },
  wallet: {
    TableName: 'wallet',
    KeySchema: [
      { AttributeName: 'user_id', KeyType: 'HASH' }, // Partition key
    ],
    AttributeDefinitions: [
      { AttributeName: 'user_id', AttributeType: 'S' },
      { AttributeName: 'delete_date', AttributeType: 'N' },
      { AttributeName: 'delete_key', AttributeType: 'S' },
      { AttributeName: 'default_method_id', AttributeType: 'S' },
    ],
    GlobalSecondaryIndexes: [
      {
        IndexName: 'delete_index',
        KeySchema: [
          {
            AttributeName: 'delete_key',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'delete_date',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
      {
        IndexName: 'default_method_id_index',
        KeySchema: [
          {
            AttributeName: 'default_method_id',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'delete_date',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
    ],
    ProvisionedThroughput: {
      ReadCapacityUnits: 10,
      WriteCapacityUnits: 10,
    },
  },
  rfid: {
    TableName: 'rfid',
    KeySchema: [
      { AttributeName: 'rfid_card_number', KeyType: 'HASH' }, // Partition key
    ],
    AttributeDefinitions: [
      { AttributeName: 'country', AttributeType: 'S' },
      { AttributeName: 'rfid_card_number', AttributeType: 'S' },
      { AttributeName: 'rfid_status', AttributeType: 'S' },
      { AttributeName: 'request_identifier', AttributeType: 'S' },
    ],
    GlobalSecondaryIndexes: [
      {
        IndexName: 'rfid_status_index',
        KeySchema: [
          {
            AttributeName: 'rfid_status',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'country',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
      {
        IndexName: 'rfid_identifier_index',
        KeySchema: [
          {
            AttributeName: 'rfid_status',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'request_identifier',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
    ],

    ProvisionedThroughput: {
      ReadCapacityUnits: 10,
      WriteCapacityUnits: 10,
    },
  },
  OCPIGuestTokens: {
    TableName: 'ocpi-guest-tokens',
    KeySchema: [
      { AttributeName: 'token_id', KeyType: 'HASH' }, // Partition key
    ],
    AttributeDefinitions: [
      { AttributeName: 'token_id', AttributeType: 'S' },
      { AttributeName: 'available', AttributeType: 'S' },
    ],
    GlobalSecondaryIndexes: [
      {
        IndexName: 'available',
        KeySchema: [
          {
            AttributeName: 'available',
            KeyType: 'HASH',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
    ],
    ProvisionedThroughput: {
      ReadCapacityUnits: 10,
      WriteCapacityUnits: 10,
    },
  },
  OCPICredentialsTable: {
    TableName: 'ocpi-credentials-table',
    KeySchema: [
      { AttributeName: 'OCPIIdentifier', KeyType: 'HASH' }, // Partition key
    ],
    AttributeDefinitions: [
      { AttributeName: 'OCPIIdentifier', AttributeType: 'S' },
      { AttributeName: 'party_id', AttributeType: 'S' },
      { AttributeName: 'country_code', AttributeType: 'S' },
    ],
    GlobalSecondaryIndexes: [
      {
        IndexName: 'party_id_country_code_index',
        KeySchema: [
          {
            AttributeName: 'party_id',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'country_code',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
    ],
    ProvisionedThroughput: {
      ReadCapacityUnits: 10,
      WriteCapacityUnits: 10,
    },
  },
  offerCodes: {
    TableName: 'offer-codes',
    KeySchema: [
      { AttributeName: 'offerCode', KeyType: 'HASH' }, // Partition key
    ],
    AttributeDefinitions: [
      { AttributeName: 'offerCode', AttributeType: 'S' },
      { AttributeName: 'status', AttributeType: 'S' },
      { AttributeName: 'userId', AttributeType: 'S' },
      { AttributeName: 'updatedDate', AttributeType: 'S' },
      { AttributeName: 'updatedDateTime', AttributeType: 'S' },
      { AttributeName: 'expiryDateTime', AttributeType: 'S' },
      { AttributeName: 'redemptionExpiryDateTime', AttributeType: 'S' },
    ],
    GlobalSecondaryIndexes: [
      {
        IndexName: 'userByUpdatedDateIndex',
        KeySchema: [
          {
            AttributeName: 'userId',
            KeyType: 'HASH',
          },
          { AttributeName: 'updatedDateTime', KeyType: 'RANGE' },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
      {
        IndexName: 'statusByUpdatedDateIndex',
        KeySchema: [
          {
            AttributeName: 'status',
            KeyType: 'HASH',
          },
          { AttributeName: 'updatedDateTime', KeyType: 'RANGE' },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
      {
        IndexName: 'statusExpiryDateIndex',
        KeySchema: [
          {
            AttributeName: 'status',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'expiryDateTime',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
      {
        IndexName: 'statusRedemptionExpiryDateIndex',
        KeySchema: [
          {
            AttributeName: 'status',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'redemptionExpiryDateTime',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
      {
        IndexName: 'updatedDateIndex',
        KeySchema: [
          {
            AttributeName: 'updatedDate',
            KeyType: 'HASH',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
    ],
    ProvisionedThroughput: {
      ReadCapacityUnits: 10,
      WriteCapacityUnits: 10,
    },
  },
  subscription: {
    TableName: 'subscription',
    KeySchema: [
      { AttributeName: 'transaction_id', KeyType: 'HASH' }, // Partition key
    ],
    AttributeDefinitions: [
      { AttributeName: 'transaction_id', AttributeType: 'S' },
      { AttributeName: 'subs_account_id', AttributeType: 'S' },
      { AttributeName: 'user_id', AttributeType: 'S' },
    ],
    GlobalSecondaryIndexes: [
      {
        IndexName: 'subs_id_index',
        KeySchema: [
          {
            AttributeName: 'subs_account_id',
            KeyType: 'HASH',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
      {
        IndexName: 'user_index',
        KeySchema: [
          {
            AttributeName: 'user_id',
            KeyType: 'HASH',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
    ],
    ProvisionedThroughput: {
      ReadCapacityUnits: 10,
      WriteCapacityUnits: 10,
    },
  },
  uberProUsers: {
    TableName: 'uber-pro-users',
    KeySchema: [
      { AttributeName: 'userId', KeyType: 'HASH' }, // Partition key
    ],
    AttributeDefinitions: [{ AttributeName: 'userId', AttributeType: 'S' }],
    ProvisionedThroughput: {
      ReadCapacityUnits: 10,
      WriteCapacityUnits: 10,
    },
  },
  tariffs_v1: {
    TableName: 'tariffs_v1',
    KeySchema: [
      { AttributeName: 'countryType', KeyType: 'HASH' }, // Partition key
      { AttributeName: 'versionTimestamp', KeyType: 'RANGE' }, //sort key
    ],
    AttributeDefinitions: [
      { AttributeName: 'countryType', AttributeType: 'S' },
      { AttributeName: 'versionTimestamp', AttributeType: 'S' },
      { AttributeName: 'tariffId', AttributeType: 'S' },
      { AttributeName: 'validFrom', AttributeType: 'S' },
    ],
    GlobalSecondaryIndexes: [
      {
        IndexName: 'tariffIdIndex',
        KeySchema: [
          {
            AttributeName: 'tariffId',
            KeyType: 'HASH',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
      {
        IndexName: 'tariffValidFromIndex',
        KeySchema: [
          {
            AttributeName: 'countryType',
            KeyType: 'HASH',
          },
          {
            AttributeName: 'validFrom',
            KeyType: 'RANGE',
          },
        ],
        Projection: {
          ProjectionType: 'ALL',
        },
        ProvisionedThroughput: {
          ReadCapacityUnits: 10,
          WriteCapacityUnits: 10,
        },
      },
    ],
    ProvisionedThroughput: {
      ReadCapacityUnits: 10,
      WriteCapacityUnits: 10,
    },
  },
};
