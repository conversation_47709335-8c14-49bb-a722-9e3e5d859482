const { Pool } = require('pg');
const AWS = require('aws-sdk');

const CHUNK_SIZE = 500;

const dbConnection = () =>
  new Pool({
    user: '',
    password: '',
    host: '',
    port: 5432,
    database: '',
    connectionTimeoutMillis: 10000,
  });

(async () => {
  const db = dbConnection();

  let chunkCount = 0;

  console.log(
    '🚀 Starting script to update partner_type for eligible memberships',
  );

  try {
    while (true) {
      console.log(`📦 Processing chunk #${++chunkCount}`);

      const { rows } = await db.query(
        `
          SELECT m.membership_id
          FROM customer.membership m
          JOIN customer.users u ON u.user_id = m.user_id
          WHERE u.origin_type_id = 2
            AND LENGTH(m.membership_external_id) = 9
            AND m.partner_type IS NULL
          LIMIT $1
        `,
        [CHUNK_SIZE],
      );

      if (rows.length === 0) {
        console.log('✅ No more rows to process.');
        break;
      }

      const membershipIds = rows.map((r) => r.membership_id);
      console.log(
        `Updating ${membershipIds.length} memberships (from ID ${
          membershipIds[0]
        } to ${membershipIds[membershipIds.length - 1]})`,
      );

      await db.query(
        `
          UPDATE customer.membership
          SET partner_type = 'ADAC'
          WHERE membership_id = ANY($1)
        `,
        [membershipIds],
      );
    }
  } catch (err) {
    console.error('❌ Error during update:', err);
  } finally {
    await db.end();
    console.log('🏁 Script finished and DB connection closed.');
  }
})();
