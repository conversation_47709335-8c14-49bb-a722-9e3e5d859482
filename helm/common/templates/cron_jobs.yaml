{{- if .Values.cronjobs }}
{{- range .Values.cronjobs }}
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ .cronJobName }}-cron-job
spec:
  schedule: {{ .cronExpression }}
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: {{ $.Values.serviceAccount }}
          containers:
            - name: {{ .cronJobName }}
              image: {{ $.Values.image.repository }}/{{ $.Values.imageRepo }}:{{ .cronImageTag }}
              volumeMounts:
                - name: secrets-store-inline
                  mountPath: /mnt/secrets-store
                  readOnly: true
              env:
                {{- range $.Values.env }}
                - name: {{ .name }}
                  {{- if .value }}
                  value: {{ tpl .value $ | quote }}
                  {{- else if .valueFrom }}
                  valueFrom:
                    secretKeyRef:
                      name: {{ .valueFrom.secretKeyRef.name }}
                      key: {{ .valueFrom.secretKeyRef.key }}
                  {{- end }}
                {{- end }}
          restartPolicy: OnFailure
          nodeSelector:
            'kubernetes.io/os': linux
          volumes:
            - name: secrets-store-inline
              csi:
                driver: secrets-store.csi.k8s.io
                readOnly: true
                volumeAttributes:
                  secretProviderClass: {{ $.Values.serverName }}-aws-secrets
{{- end }}
{{- end }}